using Newtonsoft.Json;

namespace Backend.Flights.CA.Model.Location
{
    public class LocationItem
    {
        [JsonProperty("iata")]
        public string Iata { get; set; }
        [JsonProperty("description")]
        public string Description { get; set; }
        [JsonProperty("mastercode")]
        public int? Mastercode { get; set; }
        [Json<PERSON>roperty("hits")]
        public int Hits { get; set; }
        [JsonProperty("latitude")]
        public double Latitude { get; set; }
        [JsonProperty("longitude")]
        public double Longitude { get; set; }
        [JsonProperty("type")]
        public string Type { get; set; }
        [JsonProperty("city")]
        public string City { get; set; }
        [JsonProperty("state")]
        public string State { get; set; }
        [JsonProperty("country")]
        public string Country { get; set; }
    }
}