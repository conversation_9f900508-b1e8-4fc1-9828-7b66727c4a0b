﻿using Backend.Flights.Models.Search;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;

namespace Backend.Flights.Models.Partners
{
    public class PartnerSearchRequest : SearchRequest
    {

        public Dictionary<string, object> ComplementaryData { get; set; }

        private static readonly Dictionary<string, IPartnerRequestFactory> dicPartners = new Dictionary<string, IPartnerRequestFactory>
        {
            {"Oktoplus", new OktoplusRequestFactory()}
        };

        public IPartnerRequest BuildPartnerRequest(ILogger logger, BrandContext brandCtx)
        {
            var partnerName = brandCtx.PartnerData.Name;

            if (!dicPartners.ContainsKey(partnerName))
                throw new ArgumentException("Partner Name");

            return dicPartners[partnerName].Build(this, logger, brandCtx);
        }

    }

}
