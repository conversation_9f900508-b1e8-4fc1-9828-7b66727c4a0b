﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using Newtonsoft.Json;

namespace Backend.Flights.Models.GtwAereo
{
    public class Segment
    {
        public string Departure { get; set; }
        public string Arrival { get; set; }
        public DateTime DepartureDate { get; set; }
        public DateTime ArrivalDate { get; set; }
        public string RateToken { get; set; }
        public string PackageGroup { get; set; }
        public int RouteRPH { get; set; }
        public int NumberOfStops { get; set; }
        public string FareType { get; set; }
        public int Duration { get; set; }
        public FareProfile FareProfile { get; set; }
        public List<Leg> Legs { get; set; }
        public string Provider { get; set; }
        public bool IsInternational { get; set; }
        public bool Refundable { get; set; }
        public string OperationalId { get; set; }
        /// <summary>
        /// AirTicket Property
        /// </summary>
        public bool National { get; set; }
        public BaggageByPTC Baggages { get; set; } // Novo campo para bagagens
    }

    public class FareProfile
    {
        public Baggage Baggage { get; set; }
        public List<FareProfileService> Services { get; set; }
        public string FareFamily { get; set; }

    }

    public class Baggage
    {
        public string Type { get; set; }
        public bool IsIncluded { get; set; }
        public decimal Quantity { get; set; }
        public string Uom { get; set; }
        public decimal Weight { get; set; }
    }

    public class FareProfileService
    {
        [JsonProperty(DefaultValueHandling = DefaultValueHandling.Populate)]
        [DefaultValue("")]
        public string Type { get; set; }
        public bool IsIncluded { get; set; }
        public string Description { get; set; }
    }

    public class Leg
    {
        public AirCompany ManagedBy { get; set; }
        public AirCompany OperatedBy { get; set; }
        public string FlightNumber { get; set; }
        public string Departure { get; set; }
        public string Arrival { get; set; }
        public string DepartureDescription { get; set; }
        public string ArrivalDescription { get; set; }
        public int DepartureCode { get; set; }
        public int ArrivalCode { get; set; }
        public int NumberOfStops { get; set; }
        public List<Stop> Stops { get; set; }
        public DateTime DepartureDate { get; set; }
        public DateTime ArrivalDate { get; set; }
        public int Duration { get; set; }
        public string AircraftCode { get; set; }
        public string FareBasis { get; set; }
        public string FareClass { get; set; }
        public SeatClass SeatClass { get; set; }
        public string SeatsLeft { get; set; }
        /// <summary>
        /// airToken Properties
        /// </summary>
        public string GdsStatus { get; set; }
        public string Status { get; set; }
    }

    public class Stop
    {
        public string Airport { get; set; }
        public DateTime DepartureDate { get; set; }
        public DateTime ArrivalDate { get; set; }
    }

    public class SeatClass
    {
        public string Code { get; set; }
        public string Description { get; set; }
    }

}
