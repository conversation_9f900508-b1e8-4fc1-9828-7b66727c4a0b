﻿using Backend.Flights.CA.Services.Config;
using Backend.Flights.CA.Services.Id;
using Backend.Flights.CA.UseCase.AirLocation;
using Backend.Flights.CA.UseCase.BrandContext;
using Backend.Flights.CA.UseCase.Config;
using Backend.Flights.CA.UseCase.ContentProvider;
using Backend.Flights.CA.UseCase.FeatureFlag;
using Backend.Flights.CA.UseCase.Id;
using Backend.Flights.CA.UseCase.Opportunities;
using Backend.Flights.CA.UseCase.Opportunities.Service;
using Backend.Flights.CA.UseCase.Promotions;
using Backend.Flights.CA.UseCase.RateToken;
using Backend.Flights.CA.UseCase.RateTokenCache;
using Backend.Flights.CA.UseCase.Search;
using Backend.Flights.Models;
using Backend.Flights.Services;
using Backend.Flights.Util;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.AspNetCore.Mvc.Versioning;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.IO;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;
using System.Collections.Generic;
using System.Linq;
using Backend.Flights.CA.UseCase.Checkout;
using Backend.Flights.CA.UseCase.Upsell;
using static Backend.Flights.Services.IConsul;
using ConfigurationManager = Backend.Flights.Util.ConfigurationManager;
using ConsulService = Backend.Flights.Services.ConsulService;
using Backend.Flights.CA.UseCase.Loc;
using System.Net.Http;
using System.Net;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Http;

namespace Backend.Flights
{
    public class Startup
    {
        private ConsulService _consulService;

        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;

            ConfigManager = new ConfigurationManager
            {
                AppSetting = Configuration.GetSection("AppSettings").GetChildren().ToDictionary(x => x.Key, x => x.Value)
            };

            _consulService = new ConsulService(Configuration.GetSection(CA.Services.Config.ConsulService.CONSUL_CONFIG).Value);

            ConfigManager.AppSetting.Add("ServiceUI.Url", _consulService.GetValue($"{STR_PROJECT_NAME}/serviceui.url"));
            ConfigManager.AppSetting.Add("Locations.Url", _consulService.GetValue($"{STR_PROJECT_NAME}/locations.url"));
            ConfigManager.AppSetting.Add("PushFare.Backend.Url", _consulService.GetValue($"{STR_PROJECT_NAME}/pushfare.backend.url"));
            ConfigManager.AppSetting.Add("Gateway.Sources", _consulService.GetValue(STR_KEY_GATEWAY_SOURCES));
            ConfigManager.AppSetting.Add("SearchFlights.URL", _consulService.GetValue($"{STR_PROJECT_NAME}/searchflights.url"));
            ConfigManager.AppSetting.Add("SearchFlights.Enabled", _consulService.GetValue($"{STR_PROJECT_NAME}/searchflights.enabled"));
#if DEBUG
            ConfigManager.AppSetting.Add("SearchFlights.APIKEY", Configuration.GetSection("AppSettings:SearchFlights.ApiKey").Value);
#else
            ConfigManager.AppSetting.Add("SearchFlights.APIKEY", Environment.GetEnvironmentVariable("SEARCH_FLIGHTS_API_KEY"));
#endif
            ConfigManager.AppSetting.Add("CacheType", _consulService.GetValueOrEmpty($"{STR_PROJECT_NAME}/cacheType"));

            // #if LOCAL // LOCALPRODUCAO (Local Produção)
            //             //ConfigManager.AppSetting.Add("Mongo.ConnectionData", "{\"Server\": \"mapa01-qa.addqm.mongodb.net\", \"User\": \"flights\", \"Pwd\": \"flights123\", \"Database\": \"db-flights\", \"IsAWS\": false }");
            //             ConfigManager.AppSetting.Add("Mongo.ConnectionData", "{\"Server\": \"iz8itxfl.adb.sa-saopaulo-1.oraclecloudapps.com:27017\", \"User\": \"db_flights\", \"Pwd\": \"joAW2gPg0Se2dsQHzZx0Ny79j\", \"Database\": \"db_flights\", \"IsAWS\": false, \"IsAutonomous\": true }");
            //             ConfigManager.AppSetting.Add("Redis.Url", "aaacnbgjnqaj4nlj3edoovakpkkgiw3xeimjhc4hv2sudpienvgnqvq-p.redis.sa-saopaulo-1.oci.oraclecloud.com:6379");
            // #else
            ConfigManager.AppSetting.Add("Mongo.ConnectionData", _consulService.GetValueOrEmpty($"{STR_PROJECT_NAME}/mongo.connectionData"));
            ConfigManager.AppSetting.Add("Redis.Url", _consulService.GetValueOrEmpty($"{STR_PROJECT_NAME}/redis.url"));
            // #endif

            var rawContexts = _consulService.GetValue($"platform/brand-contexts/flights");
            BrandContexts = JsonConvert.DeserializeObject<Dictionary<string, BrandContext>>(rawContexts);

            ConfigManager.AppSetting.Add("Cart.Url", _consulService.GetValue($"{STR_PROJECT_NAME}/cart.url"));
            ConfigManager.AppSetting.Add("SearchFlights.Exclusive.Days", _consulService.GetValueOrEmpty($"{STR_PROJECT_NAME}/searchflights.exclusive.days"));
            ConfigManager.AppSetting.Add("SearchFlights.Exclusive.Tower", _consulService.GetValueOrEmpty($"{STR_PROJECT_NAME}/searchflights.exclusive.tower"));
            ConfigManager.AppSetting.Add("UpSellBooking", _consulService.GetValueOrEmpty($"{STR_PROJECT_NAME}/upsell.booking")); // Chave utilizada para definir se no upsellOrCheckoutData será feita reserva dos voos ou não
            ConfigManager.AppSetting.Add("LogRequestInfo", _consulService.GetValueOrEmpty($"{STR_PROJECT_NAME}/log.request.info")); // Chave utilizada para logar as informações da requisição
            ConfigManager.AppSetting.Add("Matrix.Sales.Cache.Enabled", _consulService.GetValueOrEmpty($"{STR_PROJECT_NAME}/matrix.sales.cache.enabled")); // Chave utilizada para habilitar/desabilitar o cache de resultados de busca para matrix de vendas
        }

        public IConfiguration Configuration { get; }
        public ConfigurationManager ConfigManager { get; set; }
        Dictionary<string, BrandContext> BrandContexts { get; set; }

        public void ConfigureServices(IServiceCollection services)
        {
            services.AddLogging();
            services.AddCors(options => options.AddPolicy("AllowAnyOrigin", builder => builder.AllowAnyOrigin().AllowAnyHeader().AllowAnyMethod()));

            services.AddHttpContextAccessor();
            services.AddResponseCompression();
            services.AddHealthChecks();
            services.AddHttpClient(Options.DefaultName, c => { })
            .ConfigurePrimaryHttpMessageHandler(messageHandler =>
            {
                var handler = new HttpClientHandler();

                if (handler.SupportsAutomaticDecompression)
                {
                    handler.AutomaticDecompression = DecompressionMethods.Deflate | DecompressionMethods.GZip;
                }
                return handler;
            });

            services.AddSingleton<RecyclableMemoryStreamManager>(new RecyclableMemoryStreamManager(
                16 * 1024,        // Block size 16k
                1024 * 1024,      // Large block multiply 1m
                3 * 1024 * 1024,  // Maximum block size 3m
                15 * 1024 * 1024, // Maximum freeable small pool 15m
                5 * 1024 * 1024   // Maximum freeable large pool 5m
                ));

            services.AddSingleton(ConfigManager);
            services.AddSingleton(BrandContexts);
            services.AddSingleton<IConsul>(_consulService);
            services.AddSingleton<IAirGtw, AirGtwService>();
            services.AddSingleton<IPromotion, PromotionService>();
            services.AddSingleton<IContextBrands, ContextBrandsService>();
            services.AddSingleton<IConsulWatch, ConsulWatchService>();
            services.AddSingleton<IRetry, RetryService>();
            services.AddSingleton<ICorpLoginService, CorpLoginService>();
            services.AddScoped<IContextBrand, ContextBrandService>();
            services.AddScoped<IBranchConfigService, BranchConfigService>();

            // CA
            services.AddSingleton<CA.Services.Json.IJsonService, CA.Services.Json.JsonNewtonsoft>();
            services.AddSingleton<CA.Services.Http.IHttpClient, CA.Services.Http.HttpClient>();
            services.AddSingleton<IConfigService, CA.Services.Config.ConsulService>();
            services.AddSingleton<CA.Services.UserToken.IUserTokenService, CA.Services.UserToken.UserTokenService>();
            services.AddSingleton<CA.Services.Cache.ICacheService, CA.Services.Cache.MongoCache>();
            services.AddSingleton<CA.Services.Cache.ILocalCacheService, CA.Services.Cache.LocalCacheService>();
            services.AddSingleton<CA.Services.LocImportValidator.ILocImportValidator, CA.Services.LocImportValidator.VendaCompartilhadaLocImportValidator>();
            services.AddSingleton<CA.Services.LocImportValidator.ICheckListAirService, CA.Services.LocImportValidator.CheckListAirService>();
            services.AddSingleton<CA.Services.LocImportValidator.IPaymentCredipaxService, CA.Services.LocImportValidator.PaymentCredipaxService>();
            services.AddSingleton<IdConfigService>();
            services.AddSingleton<IWatchConfig, WatchConfig>();
            services.AddSingleton<IGetOpportunities, GetOpportunities>();
            services.AddSingleton<IGetPromotionsIndividuals, GetPromotionsIndividuals>();
            services.AddSingleton<IRequestPromotions, RequestPromotions>();
            services.AddSingleton<IGetRateTokenCache, GetRateTokenCache>();
            services.AddSingleton<IGetBrandContextById, GetBrandContextById>();
            services.AddSingleton<RequestPromotionsFromCachedFareGroup>();
            services.AddSingleton<GetFeatureFlag>();
            services.AddSingleton<WatchConfigHostedService>();
            services.AddSingleton<SearchLocationByIata>();
            services.AddSingleton<GetSourcesList>();
            services.AddSingleton<ContentProviderFilterSources>();
            services.AddSingleton<GetRateTokenAttributes>();
            services.AddSingleton<GetRateTokenPriceInfo>();
            services.AddSingleton<GetRealProvider>();
            services.AddSingleton<GetIdConfig>();
            services.AddSingleton<ComputeUpsell>();
            services.AddSingleton<GroupSameFlights>();
            services.AddSingleton<ComputeCheckoutCache>();
            services.AddSingleton<SerializeCheckoutCache>();
            services.AddSingleton<GetUpsellOrCheckout>();
            services.AddSingleton<DesserializeCheckoutCache>();
            services.AddSingleton<SmallDescriptionByIata>();
            services.AddSingleton<LocImportedToCart>();

            services.AddScoped<GetContentProviderUrl>();
            services.AddScoped<IGetCurrentBrandContext, GetCurrentBrandContext>();
            services.AddScoped<GetSearchUrl>();
            services.AddScoped<SearchParallel>();
            services.AddScoped<SearchSingleSource>();
            services.AddScoped<ValidateLocForImport>();
            services.AddScoped<LocImport>();

            services.AddSingleton<CA.Services.Http.IHttpClient, CA.Services.Http.HttpClient>();
            services.AddSingleton<CA.Services.Json.IJsonService, CA.Services.Json.JsonNewtonsoft>();
            services.AddSingleton<CA.Services.Cache.ICacheService, CA.Services.Cache.MongoCache>();

            services.AddScoped<IRandomIdService, NanoidRandomIdService>();
            services.AddScoped<CA.Services.Http.IHttpRequestService, CA.Services.Http.HttpRequestService>();
            services.AddScoped<CA.Services.AirGateway.IAirGateway, CA.Services.AirGateway.AirGateway>();
            services.AddScoped<CA.Services.LocImportValidator.ILocImportPaymentValidator, CA.Services.LocImportValidator.SysturLocImportPaymentValidator>();

            //Cache connection
            services.AddSingleton<ICacheService>(new CacheService(ConfigManager));

            services.AddMemoryCache();

            services.AddApiVersioning(o =>
            {
                o.ApiVersionReader = new HeaderApiVersionReader("x-api-version");
                o.ReportApiVersions = true;
                o.AssumeDefaultVersionWhenUnspecified = true;
                o.DefaultApiVersion = new ApiVersion(1, 0);
                o.UseApiBehavior = false;
            });

            services.AddVersionedApiExplorer(p =>
            {
                p.GroupNameFormat = "'v'VVV";
                p.SubstituteApiVersionInUrl = true;
            });

            services.AddTransient<IConfigureOptions<SwaggerGenOptions>, ConfigureSwaggerOptions>();
            services.AddSwaggerGen(options => options.CustomSchemaIds(x => x.FullName));

            services.AddControllers(options =>
            {
                options.InputFormatters.RemoveType<SystemTextJsonInputFormatter>();
                options.InputFormatters.Add(new NJsonInputFormatter(new JsonSerializerSettings(), services.BuildServiceProvider().GetRequiredService<ILogger<NJsonInputFormatter>>(), services.BuildServiceProvider().GetRequiredService<IHttpContextAccessor>(), services.BuildServiceProvider().GetRequiredService<ConfigurationManager>()));
            });

            services.AddHostedService((p) => p.GetService<IConsulWatch>());
            services.AddHostedService<WatchConfigHostedService>();
        }

        public class ConfigureSwaggerOptions : IConfigureOptions<SwaggerGenOptions>
        {
            readonly IApiVersionDescriptionProvider provider;

            public ConfigureSwaggerOptions(IApiVersionDescriptionProvider provider) => this.provider = provider;

            public void Configure(SwaggerGenOptions options)
            {
                foreach (var description in provider.ApiVersionDescriptions)
                    options.SwaggerDoc(description.GroupName, new OpenApiInfo() { Title = $"BFF Aéreo - {description.ApiVersion}", Version = description.ApiVersion.ToString(), });
            }
        }

        public void Configure(IApplicationBuilder app, IHostEnvironment env, IApiVersionDescriptionProvider provider)
        {
            app.UseHealthChecks("/health");

            app.UseMiddleware<CurlLoggingMiddleware>(app.ApplicationServices.GetRequiredService<ConfigurationManager>());

            if (env.IsDevelopment())
                app.UseDeveloperExceptionPage();
            else
                app.UseHsts();

            app.UseResponseCompression();

            app.UseRouting();

            app.UseCors("AllowAnyOrigin");

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });

            // Activate middleware for using Swagger
            app.UseSwagger();

            app.UseSwaggerUI(options =>
            {
                foreach (var description in provider.ApiVersionDescriptions)
                {
                    options.SwaggerEndpoint($"/swagger/{description.GroupName}/swagger.json", description.GroupName.ToUpperInvariant());
                }
                options.RoutePrefix = string.Empty;
            });
        }
    }
}
