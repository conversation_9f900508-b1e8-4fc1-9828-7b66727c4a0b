﻿using Backend.Flights.CA.Model.Cart;
using Newtonsoft.Json;

namespace Backend.Flights.CA.Model.Opportunities
{
    public class OpportunitiesRequestItem : CartItem
    {
        [JsonProperty("params")]
        public OpportunitiesRequestItemParam Params { get; set; }

        public OpportunitiesRequestItem(int rph, string rateToken, OpportunitiesRequestItemParam opportunitiesRequestItemParam) : base(rph, rateToken)
        {
            this.Params = opportunitiesRequestItemParam;
        }

        public class OpportunitiesRequestItemParam
        {
            [JsonProperty("operationalIds")]
            public string[] OperationalIds { get; set; }
            [JsonProperty("flights")]
            public string[] Flights { get; set; }

            public OpportunitiesRequestItemParam(string[] operationalIds, string[] flights)
            {
                this.OperationalIds = operationalIds;
                this.Flights = flights;
            }
        }
    }
}