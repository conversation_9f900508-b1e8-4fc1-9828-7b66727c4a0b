﻿using Backend.Flights.CA.Model;
using Backend.Flights.CA.Services.Config;
using Backend.Flights.CA.Services.Json;
using Backend.Flights.CA.UseCase.Config;
using Backend.Flights.Util;
using Microsoft.Extensions.Logging;
using System;

namespace Backend.Flights.CA.Services.Id
{
    public class IdConfigService
    {
        private readonly ILogger<IConfigService> _logger;
        private readonly IJsonService _json;

        public IdConfig Config { get; private set; }

        public IdConfigService(IWatchConfig config, IJsonService json, ILogger<IConfigService> logger)
        {
            _logger = logger;
            _json = json;

            config.ConfigChanged += Config_ConfigChanged;
            ParseConfig(config.GetConfig(IConfigService.ID_CONFIG).Result);
        }

        private void Config_ConfigChanged(object sender, Model.ConfigItemChangedEventArgs e)
        {
            if (e.Name == IConfigService.ID_CONFIG)
            {
                try
                {
                    ParseConfig(e.Value);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, ex.GetErrorLocation($"[IdConfigService]: Fail on update id config"));
                }
            }
        }

        private void ParseConfig(string config)
        {
            Config = _json.Deserialize<IdConfig>(config);
        }

    }
}