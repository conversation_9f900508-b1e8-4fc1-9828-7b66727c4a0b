﻿using Backend.Flights.Business;
using Backend.Flights.Models;
using Backend.Flights.Models.Promotions;
using Backend.Flights.Util;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace Backend.Flights.Services
{
    public class PromotionService : IPromotion
    {

        private readonly ILogger _logger;
        private readonly ICacheService _cache;
        private readonly IRetry _retry;

        public PromotionService(ILogger<IPromotion> logger, ICacheService cache, IRetry retry)
        {
            _logger = logger;
            _cache = cache;
            _retry = retry;
        }


        public async Task<PromotionResponse> GetPromotions(IContextBrand contextBrand, BffPromotionRequest bffRequest,
            string gtwUserToken)
        {
            // Recupera os ids solicitados do cache
            var cacheResult = await _cache.GetValue(bffRequest.FlightsIds);

            // Desserializa os dados do cache
            var rateTokensFareGroup = cacheResult.Select(r =>
            {
                if (r is null)
                {
                    return null;
                }

                return JsonConvert.DeserializeObject<UpsellAndCheckoutData.RateTokenFareGroupUpgCache>(r);
            }).ToArray();

            // Realiza as chamadas para promotions
            var promotions = await Task.WhenAll(
                rateTokensFareGroup
                    .Select(async fareGroup =>
                    {
                        if (fareGroup == null)
                        {
                            return null;
                        }

                        try
                        {
                            return await GetPromotions(
                                contextBrand,
                                CreatPromotionRequest(fareGroup, bffRequest.SelectedItems),
                                gtwUserToken);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, ex.GetErrorLocation("[GetPromotions]: Get Promotion Error"));
                            return null;
                        }
                    })
            );

            // Atualiza os valores de RateToken em cache
            /*
            var newCache = promotions
                .Select((promotion, idx) => (promotion: promotion, idx: idx))
                .Where(i => i.promotion != null && i.promotion.AvailableItems?.Count > 0)
                .ToDictionary(i => bffRequest.FlightsIds[i.idx],
                    i =>
                    {
                        var fg = rateTokensFareGroup[i.idx]!;
                        foreach (var availabeItem in i.promotion.AvailableItems.Where(a =>
                                     !string.IsNullOrEmpty(a.RateToken)))
                        {
                            fg.Tokens[fg.Tokens.Keys.ElementAt(availabeItem.RPH)] = availabeItem.RateToken;
                        }

                        return JsonConvert.SerializeObject(fg);
                    });

            if (newCache.Count > 0)
            {
                await _cache.BatchAdd(
                    newCache,
                    UpsellAndCheckoutData.CreateCacheData.expirySeconds,
                    _logger);
            }
            */

            // Retorna o promotions por faregroup
            return new PromotionResponse()
            {
                AvailableItems = promotions.Select((p, idx) =>
                {
                    var pir = p?.AvailableItems?.FirstOrDefault();
                    if (pir == null)
                    {
                        pir = new PromotionItemResponse(idx, null, null, null, null);
                    }
                    else
                    {
                        pir.RPH = idx;
                    }
                    return pir;
                }).ToList(),
                SelectedItems = new List<PromotionItemResponse>()
            };
        }

        private Task<PromotionResponse> GetPromotions(IContextBrand contextBrand, PromotionRequest request,
            string gtwUserToken) => GetPromotions(contextBrand.Context, request, gtwUserToken);

        public async Task<PromotionResponse> GetPromotions(BrandContext contextBrand, PromotionRequest request,
            string gtwUserToken)
        {
            try
            {
                return await _retry.ResilientCall(() => CallPromotionsService(contextBrand, request, gtwUserToken));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.GetErrorLocation("[GetPromotions]: Can't get promotion"));
                return null;
            }
        }

        private async Task<PromotionResponse> CallPromotionsService(BrandContext contextBrand, PromotionRequest request,
            string gtwUserToken)
        {
            var url = contextBrand.PromotionsApiGtwUrl;

            if (
                (request.AvailableItems.Count == 0 && request.SelectedItems.Count == 0) ||
                string.IsNullOrWhiteSpace(url)
            )
            {
                return new PromotionResponse
                {
                    AvailableItems = new List<PromotionItemResponse>(),
                    SelectedItems = new List<PromotionItemResponse>()
                };
            }

            var wc = new WebClient();
            var gtwId = SetAuthHeaders(wc, gtwUserToken);

            var body = JsonConvert.SerializeObject(request);

            var sw = Stopwatch.StartNew();
            var response = await wc.UploadStringTaskAsync(url, body);
            sw.Stop();

            var promotionMessage = sw.Elapsed;

            sw = Stopwatch.StartNew();
            var result = JsonConvert.DeserializeObject<PromotionResponse>(response);
            sw.Stop();

            // [log-disabled]  _logger.LogInformation(@"Promotions Request Time: {requestTime} 
            // Deserialization Time: {deserializationTime}
            // Gtw-Transaction-Id: {id}", promotionMessage, sw.Elapsed, gtwId);

            return result;
        }

        private string SetAuthHeaders(WebClient wc, string gtwSecUserToken)
        {

            var id = Guid.NewGuid().ToString();
            wc.Headers.Add(HttpRequestHeader.Accept, "application/json, text/javascript, */*; q=0.01");
            wc.Headers.Add(HttpRequestHeader.ContentType, "application/json; charset=utf-8");
            wc.Headers.Add("Gtw-Sec-User-Token", gtwSecUserToken);
            wc.Headers.Add("Gtw-Transaction-Id", id);

            return id;
        }

        private PromotionRequest CreatPromotionRequest(UpsellAndCheckoutData.RateTokenFareGroupUpgCache fareGroup,
            List<PromotionItem> selectedItems)
        {
            return new PromotionRequest
            {

                AvailableItems = fareGroup.Tokens.Values
                    .Select((token, idx) => new PromotionItem(idx, token))
                    .ToList(),
                SelectedItems = selectedItems
            };
            ;
        }

    }
}