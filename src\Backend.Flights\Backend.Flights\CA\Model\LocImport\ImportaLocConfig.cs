using System.Collections.Generic;

namespace Backend.Flights.CA.Model.LocImport
{
    public class ImportaLocConfig
    {
        public bool UseNewService { get; set; } = false;
        public Dictionary<string, EndpointConfig> Endpoints { get; set; }
        public EndpointConfig GetEndpoint(string endpointName)
        {
            if (Endpoints != null && Endpoints.TryGetValue(endpointName, out EndpointConfig value))
            {
                return value;
            }
            return null;
        }
    }

    public class EndpointConfig
    {
        public string Url { get; set; }
        public string Path { get; set; }
        public Dictionary<string, string> FixedQueryParams { get; set; }
        public string FullUrl
        {
            get
            {
                // Garante que a URL base termine com uma barra
                var baseUrl = Url.EndsWith("/") ? Url : Url + "/";

                if (Path == null)
                    return baseUrl;

                // Remove a barra do início do path, se houver, para evitar duplicação
                var endpointPath = Path.StartsWith("/") ? Path.Substring(1) : Path;
                var fullUrl = baseUrl + endpointPath;

                return fullUrl;
            }
        }
    }
}