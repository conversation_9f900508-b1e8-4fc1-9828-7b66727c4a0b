﻿using Backend.Flights.Models.GtwAereo;
using Backend.Flights.Models.Search;
using Backend.Flights.Util;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Backend.Flights.Business.Parsers
{
    public class MatrixParser
    {
        public static Models.Search.PriceMatrix ParseToPriceMatrix(FlightsResponse flightsResponse)
        {
            if (flightsResponse == null)
                return null;

            var pm = flightsResponse.Meta.PriceMatrix;

            var priceMatrix = new Models.Search.PriceMatrix() { AirCompanies = new List<PriceMatrixAirCompany>() };

            if (pm != null && pm.Columns.HasCount())
            {
                var i = pm.Columns.OrderBy(x => x.Rows.Where(y => y.BestPrice)).ThenBy(x => x.Rows.OrderBy(r => r.NumberOfStops));

                foreach (var col in pm.Columns)
                    priceMatrix.AirCompanies.Add(new PriceMatrixAirCompany
                    {
                        Cells = ParseToPriceMatrixCells(col.Rows),
                        AirCompany = SearchParser.ParseCiaName(col.AirCompanies[0].Name),
                        BestPriceAirCompany = col.Rows.OrderBy(r => r.Price).First().Price,
                        CiaCode = col.AirCompanies[0].Iata,
                        OriginSource = col.AirCompanies[0].Iata,
                        SearchKey = Guid.NewGuid().ToString()
                    });

                priceMatrix.AirCompanies = priceMatrix.AirCompanies.OrderBy(a => a.BestPriceAirCompany).ToList();
            }

            priceMatrix.AirCompanies = MatrixOrder(priceMatrix.AirCompanies);
            return priceMatrix;
        }

        private static List<PriceMatrixCell> ParseToPriceMatrixCells(List<PriceMatrixRow> rows)
        {
            var cells = new List<PriceMatrixCell>();

            for (int i = 0; i < 3; i++)
            {
                var row = rows.FirstOrDefault(r => r.NumberOfStops == i);
                if (row != null)
                {
                    cells.Add(new PriceMatrixCell
                    {
                        BestInstallments = 1,
                        Fare = row.PriceWithoutTax,
                        Tax = (row.Price - row.PriceWithoutTax),
                        Price = row.Price,
                        Type = (NumberOfStops)row.NumberOfStops
                    });
                }
                else
                {
                    cells.Add(new PriceMatrixCell
                    {
                        BestInstallments = 1,
                        Fare = 0,
                        Tax = 0,
                        Price = 0,
                        Type = (NumberOfStops)i
                    });
                }
            }

            return cells;
        }

        private static List<PriceMatrixAirCompany> MatrixOrder(List<PriceMatrixAirCompany> rows)
        {
            try
            {
                List<PriceMatrixAirCompany> ciasHelper = new List<PriceMatrixAirCompany>();

                if (rows.FirstOrDefault() != null)
                {
                    foreach (var tipo in rows.First().Cells.Select(x => x.Type).Distinct())
                    {
                        List<PriceMatrixAirCompany> pms = rows.Where(p => p.Cells.Where(pc => pc.Price > 0 && pc.Type == tipo).Count() > 0).ToList();

                        if (pms.Count > 0)
                        {
                            PriceMatrixAirCompany pmac = pms.OrderBy(p => p.Cells.Where(pc => pc.Price > 0 && pc.Type == tipo).Min(pc => pc.Price)).ThenBy(p => p.BestPriceAirCompany).First();

                            if (pmac != null && (ciasHelper.Count == 0 || !ciasHelper.Contains(pmac)))
                                ciasHelper.Add(pmac);
                        }
                    }

                    ciasHelper = ciasHelper.OrderBy(p => p.Cells.Where(pc => pc.Price > 0).Min(pc => pc.Price)).ThenBy(p => p.BestPriceAirCompany).ToList();

                    rows = rows.Where(p => p.Cells.Where(pc => pc.Price > 0).Count() > 0).ToList();
                    rows = rows.OrderBy(p => p.Cells.Where(pc => pc.Price > 0).Min(pc => pc.Price)).ThenBy(p => p.BestPriceAirCompany).ToList();

                    ciasHelper.AddRange(rows.Where(x => !ciasHelper.Contains(x)));
                }

                return ciasHelper;
            }
            catch (Exception)
            {
                //we need to return to the order's logic, sometimes we get NullPointerException 
                return rows;
            }
        }
    }
}
