﻿using System.Collections.Generic;

namespace Backend.Flights.Models.Serviceui
{
    public class SearchData
    {
        public AirSearchData AirSearchData { get; set; }
        public int SearchMode { get; set; }
        public bool IsMultiProduct { get; set; }
        public int QueryStringVersion { get; set; }
    }

    public class AirSearchData
    {
        public List<CityPairRequest> CityPairsRequest { get; set; }
        public int NumberADTs { get; set; }
        public int NumberCHDs { get; set; }
        public int NumberINFs { get; set; }
        public bool IsExecutive { get; set; }
    }

    public class CityPairRequest
    {
        public string Origin { get; set; }
        public string Destination { get; set; }
        public int DepartureYear { get; set; }
        public int DepartureMonth { get; set; }
        public int DepartureDay { get; set; }
        public int NumberADTs { get; set; }
        public int NumberCHDs { get; set; }
        public int NumberINFs { get; set; }
    }

    public class FilterContext
    {
        public int Strategy { get; set; }
        public object[] RangeKeys { get; set; }
        public bool InclusiveByRight { get; set; }
        public bool InclusiveByLeft { get; set; }
        public string ApplyTo { get; set; }
    }
}
