﻿using Backend.Flights.CA.Model.LocImport;
using Backend.Flights.CA.Services.AirGateway;
using System;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Backend.Flights.CA.Services.Config;
using Backend.Flights.CA.UseCase.Config;
using Backend.Flights.Models.Markdown.ImportaLoc;

using Result = Backend.Flights.CA.SafeResult<object, Backend.Flights.CA.Model.LocImport.LocImportErrorResult>;

namespace Backend.Flights.CA.UseCase.Loc
{
    public class LocImport
    {
        private readonly ValidateLocForImport _validateLocForImport;
        private readonly LocImportedToCart _locImportedToCart;
        private readonly IAirGateway _airGateway;

        private string[] _allowedStates = Array.Empty<string>();

        public LocImport(
            IAirGateway airGateway,
            ValidateLocForImport validateLocForImport,
            LocImportedToCart locImportedToCart,
            IWatchConfig watchConfig)
        {
            _airGateway = airGateway;
            _validateLocForImport = validateLocForImport;
            _locImportedToCart = locImportedToCart;

            watchConfig.ConfigChanged += (_, a) =>
            {
                if (a.Name == IConfigService.IMPORTALOC_VALID_STATUS)
                {
                    ProcessValidStates(a.Value);
                }
            };

            ProcessValidStates(watchConfig.GetConfig(IConfigService.IMPORTALOC_VALID_STATUS).Result);
        }

        private void ProcessValidStates(string config)
        {
            _allowedStates = !string.IsNullOrWhiteSpace(config) ? config?.Split("|") : Array.Empty<string>();
        }

        public async Task<Result> Execute(Model.Context.BrandContext brandContext, string loc, string source, string packageGroup)
        {
            if (packageGroup.ToUpper() == "VHI" || packageGroup.ToUpper() == "VHIPLUS")
            {
                return Result.Fail(LocImportErrorResult.Error($"Não é permitido importar tarifa {packageGroup.ToUpper()}."));
            }

            var importedLoc = await _airGateway.LocImport(brandContext, loc, source, packageGroup);

            if (importedLoc.IsError)
            {
                return Result.Fail(LocImportErrorResult.HttpError(importedLoc.Error.Item1, importedLoc.Error.Item2, importedLoc.Error.Item3));
            }

            ImportaLocResponse importaLocResponse = _locImportedToCart.toImportaLocResponse(importedLoc.Result);
            var packageGroupResponse = _locImportedToCart.getPackageGroup(importaLocResponse);
            var locStatus = _locImportedToCart.getLocStatus(importaLocResponse);

            var validationError = await _validateLocForImport.Execute(loc, source, packageGroupResponse, brandContext);

            if (validationError != null)
            {
                return Result.Fail(validationError);
            }

            if (_allowedStates.Length > 0 && !_allowedStates.Contains(locStatus))
            {
                var errorMessage = new StringBuilder("Localizador com status inválido (");
                errorMessage.Append(locStatus);
                errorMessage.Append("), status válido");
                errorMessage.Append(_allowedStates.Length > 1 ? "s: " : ": ");
                errorMessage.Append(string.Join(", ", _allowedStates));

                return Result.Fail(LocImportErrorResult.Error(
                    errorMessage.ToString()));
            }

            return Result.Success(await _locImportedToCart.Execute(brandContext, importaLocResponse, loc, source));
        }
    }
}
