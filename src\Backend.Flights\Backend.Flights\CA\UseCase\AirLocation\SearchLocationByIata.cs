using Backend.Flights.CA.Model.Location;
using Backend.Flights.CA.Services.Cache;
using Backend.Flights.CA.Services.Config;
using Backend.Flights.CA.Services.Http;
using Backend.Flights.CA.Services.Json;
using Backend.Flights.CA.UseCase.Config;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using Microsoft.Extensions.Logging;

namespace Backend.Flights.CA.UseCase.AirLocation
{
    public class SearchLocationByIata
    {
        private const string TYPE_AIRPORT = "AIRPORT";

        private readonly ILogger _logger;
        private readonly ILocalCacheService _localCacheService;
        private readonly ICacheService _cache;
        private readonly IJsonService _json;
        private readonly IHttpClient _httpClient;
        private readonly IWatchConfig _watchConfig;

        public SearchLocationByIata(
            ILocalCacheService localCacheService, ICacheService cache, IJsonService json,
            IHttpClient httpClient, IWatchConfig watchConfig, ILogger<SearchLocationByIata> logger)
        {
            _logger = logger;
            _cache = cache;
            _localCacheService = localCacheService;
            _json = json;
            _httpClient = httpClient;
            _watchConfig = watchConfig;
        }

        async Task<LocationItem> FromService(string iata)
        {
            var url = await _watchConfig.GetConfig(IConfigService.LOCATION_URL);

            var (json, error, httpResponse) = await _httpClient.GetString($"{url}locations?productType=AIR&q={HttpUtility.UrlEncode(iata)}");

            if (error != null)
            {
                return null;
            }

            var apiRes = _json.Deserialize<LocationResponse>(json);
            if (apiRes.Locations?.Length <= 0)
            {
                return null;
            }
            var exactIata = apiRes.Locations.Where(i =>
                i.Iata.Equals(iata, StringComparison.CurrentCultureIgnoreCase)).ToArray();

            if (exactIata.Length > 0)
            {
                var res = exactIata.FirstOrDefault(i =>
                              i.Type.Equals(TYPE_AIRPORT, StringComparison.CurrentCultureIgnoreCase)) ??
                          exactIata.First();

                // TODO: corrigir cadastro de location para remover essa gambiarra
                if (res is { Iata: "SAO", Type: TYPE_AIRPORT })
                {
                    res.City = "São Paulo";
                }

                return res;
            }

            return null;
        }

        async Task<LocationItem> FromCache(string cacheKey)
        {
            var json = await _cache.GetItem(cacheKey);
            return !string.IsNullOrEmpty(json) ? _json.Deserialize<LocationItem>(json) : null;
        }

        async Task AddToCache(string cacheKey, LocationItem value)
        {
            try
            {
                await _cache.SetItem(cacheKey, _json.Serialize(value), 24 * 60 * 60);
            }
            catch (Exception ex)
            {
                _logger.LogInformation(ex, $"[AddToCache] - Error while adding location to cache. Key: {cacheKey}. Value: {value}");
            }
        }

        public async Task<LocationItem> Execute(string iata)
        {
            var cacheKey = $"l/i/{iata}";
            var value = await _localCacheService.Get<LocationItem>(cacheKey);

            if (value != null)
            {
                return value;
            }

            value = await FromCache(cacheKey);

            if (value != null)
            {
                await _localCacheService.Set(cacheKey, value);
                return value;
            }

            value = await FromService(iata);

            if (value == null)
            {
                return null;
            }

            await Task.WhenAll(
                _localCacheService.Set(cacheKey, value),
                AddToCache(cacheKey, value)
            );
            return value;
        }
    }
}