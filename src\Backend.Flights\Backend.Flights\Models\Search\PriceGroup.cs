﻿using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Backend.Flights.Models.Search
{
    internal class ReducedNameAttribute : Attribute
    {
        public string Name { get; set; }
        public ReducedNameAttribute(string name) => this.Name = name;
    }
    internal class MustSkipPropertyIfContractResolverIsFlaggedAttribute : Attribute { }
    internal class AvailabilityContractResolver : DefaultContractResolver
    {
        private readonly bool mustSkipFlaggedProperty;
        private readonly bool serializeFullPropNames;
        public AvailabilityContractResolver(bool serializeFullPropNames, bool mustSkipFlaggedProperty) : base()
        {
            this.NamingStrategy = new CamelCaseNamingStrategy();
            this.serializeFullPropNames = serializeFullPropNames;
            this.mustSkipFlaggedProperty = mustSkipFlaggedProperty;
        }
        protected override IList<JsonProperty> CreateProperties(Type type, MemberSerialization memberSerialization)
        {
            IEnumerable<JsonProperty> list = base.CreateProperties(type, memberSerialization);
            if (mustSkipFlaggedProperty)
                list = from x in list
                       where !x.AttributeProvider.GetAttributes(false).Any(a => a is MustSkipPropertyIfContractResolverIsFlaggedAttribute)
                       select x;

            if (!this.serializeFullPropNames)
                foreach (var prop in list)
                    if (prop.AttributeProvider.GetAttributes(false).FirstOrDefault(a => a is ReducedNameAttribute) is ReducedNameAttribute attr)
                        prop.PropertyName = attr.Name;
            return list.ToList();
        }
    }

    public class PriceGroup
    {
        public string UID { get; set; }
        [ReducedName("sck")]
        public string SearchCacheKey { get; set; } // Chave do cache para vinculação
        [ReducedName("s")]
        public List<Segments> Segments { get; set; }
        [ReducedName("di")]
        public DebugInformation DebugInformation { get; set; }
        [ReducedName("t")]
        public decimal Total { get; set; }
        [ReducedName("i")]
        public int Installments { get; set; }
        [ReducedName("io")]
        public List<InstallmentOption> InstallmentOptions { get; set; }
        [ReducedName("tx")]
        public decimal Tax { get; set; }
        [ReducedName("f")]
        public decimal Fee { get; set; }
        [ReducedName("d")]
        public decimal DisplayFare { get; set; }
        [ReducedName("fr")]
        public decimal Fare { get; set; }
        [ReducedName("cf")]
        public decimal ChildFare { get; set; }
        [ReducedName("if")]
        public decimal InfantFare { get; set; }
        [ReducedName("pd")]
        public PriceDetailByPassengerType PriceDetails { get; set; }
        [ReducedName("hl")]
        public string Highlight { get; set; }
        [ReducedName("bg")]
        public BaggageByPTC Baggages { get; set; } // Novo campo Bagagens
    }

    public class BaggageByPTC
    {
        [ReducedName("adt")]
        public List<BaggageType> ADT { get; set; } // Bagagens para adultos

        [ReducedName("chd")]
        public List<BaggageType> CHD { get; set; } // Bagagens para crianças

        [ReducedName("inf")]
        public List<BaggageType> INF { get; set; } // Bagagens para bebês
    }

    public class BaggageType
    {
        [ReducedName("t")]
        public string Type { get; set; } // "checked" ou "carryon" (Despachada e Bagagem de mão)

        [ReducedName("c")]
        public int Count { get; set; } // Quantidade de bagagens

        [ReducedName("w")]
        public double? Weight { get; set; } // Peso em quilos

        [ReducedName("d")]
        public BaggageDimensions? Dimensions { get; set; } // Dimensões da bagagem

        [ReducedName("ls")]
        public double? LinearSize { get; set; } // Tamanho linear
    }

    public class BaggageDimensions
    {
        [ReducedName("l")]
        public double? Length { get; set; } // Comprimento

        [ReducedName("w")]
        public double? Width { get; set; } // Largura

        [ReducedName("d")]
        public double? Depth { get; set; } // Altura
    }

    public class AddAggrement
    {
        [ReducedName("ac")]
        public string AccountCode { get; set; }
        [ReducedName("ptc")]
        public string PTC { get; set; }
    }

    public class PriceDetailByPassengerType
    {
        [ReducedName("a")]
        public PriceDetail Adult { get; set; }
        [ReducedName("c")]
        public PriceDetail Child { get; set; }
        [ReducedName("i")]
        public PriceDetail Infant { get; set; }
    }
    public class PriceDetail
    {
        [ReducedName("c")]
        public int Count { get; set; }
        [ReducedName("f")]
        public decimal Fee { get; set; }
        [ReducedName("t")]
        public decimal Tax { get; set; }
    }

    public class Segments
    {
        [ReducedName("fl")]
        public List<Flight> Flights { get; set; }
        [ReducedName("si")]
        public int SegmentIndex { get; set; }
        [ReducedName("o")]
        public string Origin { get; set; }
        [ReducedName("d")]
        public string Destination { get; set; }
        [ReducedName("c")]
        public string IssuerCia { get; set; }
        [ReducedName("cn")]
        public string IssuerCiaName { get; set; }
        [ReducedName("tmd")]
        public int TotalMinuteDuration { get; set; }
        [ReducedName("ci")]
        public string CorrelationId { get; set; }
        [ReducedName("sk"), MustSkipPropertyIfContractResolverIsFlagged]
        public string SellKey { get; set; }
        [ReducedName("op")]
        public string OriginProvider { get; set; }
        [ReducedName("ns")]
        public int NumberOfStops { get; set; }
        [ReducedName("r")]
        public bool Refundable { get; set; }
        [ReducedName("t")]
        public string FareType { get; set; }
        [ReducedName("l")]
        public List<Leg> Legs { get; set; }
        [ReducedName("i")]
        public string OperationalId { get; set; }
    }

    public class DebugInformation
    {
        [ReducedName("m")]
        public decimal Markup { get; set; }
        [ReducedName("fb")]
        public string FareBasis { get; set; }
        [ReducedName("ft")]
        public string FareType { get; set; }
        [ReducedName("pi")]
        public string PricingId { get; set; }
        [ReducedName("pg")]
        public string PackageGroup { get; set; }
        [ReducedName("aa")]
        public AddAggrement AddAggrement { get; set; }
        [ReducedName("oi")]
        public string OfficeId { get; set; }
        [ReducedName("nv")]
        public decimal NetValue { get; set; }
        [ReducedName("rav")]
        public TaxRav Rav { get; set; }
    }

    public class TaxRav
    {
        [ReducedName("cd")]
        public string Code { get; set; }
        [ReducedName("dc")]
        public string Description { get; set; }
        [ReducedName("v")]
        public List<TaxRavValues> Values { get; set; }
        [ReducedName("dt")]
        public List<TaxRavDetail> Details { get; set; }
    }

    public class TaxRavDetail
    {
        [ReducedName("cd")]
        public string Code { get; set; }
        [ReducedName("dc")]
        public string Description { get; set; }
        [ReducedName("a")]
        public decimal Amount { get; set; }
    }

    public class TaxRavValues
    {
        [ReducedName("c")]
        public string Currency { get; set; }
        [ReducedName("a")]
        public decimal Amount { get; set; }
    }

    public class Flight
    {
        [ReducedName("aa")]
        public string ArrivalAirport { get; set; }
        [ReducedName("aad")]
        public string ArrivalAirportDescription { get; set; }
        [ReducedName("da")]
        public string DepartureAirport { get; set; }
        [ReducedName("dad")]
        public string DepartureAirportDescription { get; set; }
        [ReducedName("ad")]
        public DateTime ArrivalDateTime { get; set; }
        [ReducedName("dd")]
        public DateTime DepartureDateTime { get; set; }
        [ReducedName("c")]
        public string CiaCode { get; set; }
        [ReducedName("cn")]
        public string CiaName { get; set; }
        [ReducedName("cbn")]
        public string Cabin { get; set; }
        [ReducedName("b")]
        public BaggageInfo BaggageInfo { get; set; }
        [ReducedName("s")]
        public List<Stop> Stops { get; set; }
        [ReducedName("ns")]
        public int NumberOfStops { get; set; }
        [ReducedName("fn")]
        public string FlightNumber { get; set; }
        [ReducedName("md")]
        public int MinutesDuration { get; set; }
        [ReducedName("sl")]
        public string SeatsLeft { get; set; }
    }

    public class Leg
    {
        [ReducedName("c")]
        public string FareClass { get; set; }
    }

    public class Stop
    {
        [ReducedName("a")]
        public string Airport { get; set; }
        [ReducedName("dd")]
        public DateTime DepartureDate { get; set; }
        [ReducedName("ad")]
        public DateTime ArrivalDate { get; set; }
    }

    public class BaggageInfo
    {
        [ReducedName("h")]
        public int HandBaggageQuantity { get; set; }
        [ReducedName("c")]
        public int CheckedBaggageQuantity { get; set; }
        [ReducedName("hw")]
        public int HandBaggageWeight { get; set; }
        [ReducedName("cw")]
        public int CheckedBaggageWeight { get; set; }
    }

}
