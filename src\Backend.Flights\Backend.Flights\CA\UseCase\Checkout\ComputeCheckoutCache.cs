﻿using Backend.Flights.CA.Model.Search.Result;
using Backend.Flights.CA.UseCase.AirLocation;
using Backend.Flights.CA.UseCase.RateToken;
using Backend.Flights.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Backend.Flights.CA.UseCase.Checkout
{
    public class ComputeCheckoutCache
    {
        private readonly SearchLocationByIata _searchLocationByIata;
        private readonly GetRateTokenPriceInfo _getRateTokenPriceInfo;

        public ComputeCheckoutCache(
            SearchLocationByIata searchLocationByIata,
            GetRateTokenPriceInfo getRateTokenPriceInfo)
        {
            _searchLocationByIata = searchLocationByIata;
            _getRateTokenPriceInfo = getRateTokenPriceInfo;
        }

        public async Task<Model.Cache.CartCheckout> Execute(string id, IList<string> upsells, Dictionary<string, SearchResultFlight> flights)
        {
            var currentItem = flights[id];
            var firstSegment = currentItem.Segments.First();
            var lastSegment = currentItem.Segments.Last();

            var departureLocation = await _searchLocationByIata.Execute(firstSegment.Departure);
            var isMulti =
                currentItem.Segments.Length > 1 &&
                firstSegment.Departure != lastSegment.Arrival &&
                lastSegment.Departure != firstSegment.Arrival;

            var arrivalLocation = await _searchLocationByIata.Execute(
                (isMulti ? lastSegment : firstSegment).Arrival
                );

            var dicPaxCount = new Dictionary<string, int>(
                currentItem.FareGroup.Fares
                    .GroupBy(fare => fare.PassengersType)
                    .Select(fare => new KeyValuePair<string, int>(fare.Key, fare.Sum(fareItem => fareItem.PassengersCount)))
                );

            var (costPrice, _) = _getRateTokenPriceInfo.FromToken(firstSegment.RateToken).GetValueOrDefault((0, 0));

            var isRefundable = currentItem.Segments.All(segment => segment.Refundable);

            var groupedSegments = currentItem.Segments
                .GroupBy(segment => segment.RouteRPH)
                .ToArray();

            var rateTokenInfo = groupedSegments.Select(g => g.Select(s => new Model.Cart.CartFlightItem.RateTokenInfo(s.RouteRPH, s.PackageGroup, s.RateToken)).ToArray()).ToArray();
            var defrateTokenInfo = new List<Model.Cart.CartFlightItem.RateTokenInfo>();
            foreach(var pair in rateTokenInfo)
            {
                defrateTokenInfo.AddRange(pair);
            }
            var groupedSegmentsFirst = groupedSegments.Select(item => item.First());

            var serviceHandLuggage = groupedSegmentsFirst
                .Select(segment => segment.FareProfile.Services.Where(service => service.Type == "HAND_LUGGAGE").ToArray())
                .ToArray();

            var fullUpsells = new List<string>();
            if (upsells.Count > 0)
            {
                fullUpsells.Add(id);
                fullUpsells.AddRange(upsells);
            }

            Model.Cart.CartFlightItem.Location depLoc = new Model.Cart.CartFlightItem.Location(departureLocation.Iata, departureLocation.City, departureLocation.State, departureLocation.Country);
            Model.Cart.CartFlightItem.Location arrLoc = new Model.Cart.CartFlightItem.Location(arrivalLocation.Iata, arrivalLocation.City, arrivalLocation.State, arrivalLocation.Country);
            Model.Cart.CartFlightItem.Pax pax = new Model.Cart.CartFlightItem.Pax(dicPaxCount.GetValueOrDefault("ADT"), dicPaxCount.GetValueOrDefault("CHD"), dicPaxCount.GetValueOrDefault("INF"));
            Model.Cart.CartFlightItem.NotifyInfo notifyInfo = new Model.Cart.CartFlightItem.NotifyInfo(costPrice, currentItem.FareGroup.Fares.First(fare => fare.PassengersType == "ADT").PriceWithTax);
            Model.Cart.CartFlightItem.FareGroupInfo fareGroupInfo = new Model.Cart.CartFlightItem.FareGroupInfo(
                currentItem.FareGroup.Currency,
                currentItem.FareGroup.PriceWithTax,
                currentItem.FareGroup.PriceWithoutTax,
                currentItem.FareGroup.Discount,
                currentItem.FareGroup.DiscountPercentage,
                isRefundable,
                defrateTokenInfo.ToArray()
            );

            return new Model.Cache.CartCheckout(
                new Model.Cart.CartFlightItem(
                    currentItem.provider,
                    depLoc,
                    arrLoc,
                    firstSegment.Legs.First().DepartureDate,
                    lastSegment.Legs.First().DepartureDate,
                    firstSegment.PackageGroup,
                    pax,
                    notifyInfo,
                    fareGroupInfo,
                    isMulti,
                    groupedSegmentsFirst.Select((segment, idx) =>
                    {
                        return new Model.Cart.CartFlightItem.Segment(
                            Array.Empty<Model.Cart.CartFlightItem.Leg>(),
                            new Model.Cart.CartFlightItem.Bagage(
                                serviceHandLuggage[idx].Any(service => !service.IsIncluded) ? 0 : 1,
                                decimal.ToInt32(segment.FareProfile.Baggage?.Weight ?? 0),
                                serviceHandLuggage[idx].Any(service => !service.IsIncluded) ? 0 :
                                    HandBaggageWeightService.parse(serviceHandLuggage[idx].FirstOrDefault()?.Description),
                                decimal.ToInt32(segment.FareProfile.Baggage?.Weight ?? 0)
                                ),
                            segment.Duration,
                            segment.NumberOfStops
                            );
                    }).ToArray()),
                new Dictionary<string, Model.Cache.CartCheckout.Option>(
                    groupedSegments.SelectMany(segmentGroup =>
                        segmentGroup.Select((segment, segmentIdx) => {
                           var SegmentHour = $"{int.Parse(segment.DepartureDate.ToString("HHmm"))}{int.Parse(segment.ArrivalDate.ToString("HHmm"))}";
                            
                           return new KeyValuePair<string, Model.Cache.CartCheckout.Option>(
                            $"{segmentGroup.Key}-{segmentIdx}-{SegmentHour}",
                            new Model.Cache.CartCheckout.Option(
                                segment.RateToken,
                                segment.PackageGroup,
                                new Model.Cart.CartFlightItem.Segment(
                                    segment.Legs.Select(leg =>
                                        new Model.Cart.CartFlightItem.Leg(
                                            leg.Departure, null, leg.DepartureDate,
                                            leg.Arrival, null, leg.ArrivalDate,
                                            new Model.Cart.CartFlightItem.AirCompany(leg.OperatedBy.Name, leg.OperatedBy.Iata),
                                            null,
                                            leg.SeatClass?.Description,
                                            leg.Duration,
                                            leg.FlightNumber,
                                            new Model.Cart.CartFlightItem.AirCompany(leg.ManagedBy.Name, leg.ManagedBy.Iata)
                                        )
                                    ).ToArray(),
                                    null, 0, 0
                            )));
                        })
                    )),
                fullUpsells.Select(upsell => new Model.Cache.CartCheckout.UpsellOption(
                        upsell,
                        flights[upsell].Segments.First().FareProfile)
                ).ToArray());
        }
    }
}
