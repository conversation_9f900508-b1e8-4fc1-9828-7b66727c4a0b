﻿using Backend.Flights.Business;
using Backend.Flights.CA.Services.Http;
using Backend.Flights.CA.Services.Json;
using Backend.Flights.Models.Markdown;
using Backend.Flights.Services;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace Backend.Flights.Controllers
{
    [ApiController, ApiVersion("1.0"), Route("api/[controller]")]
    public class MarkdownController : ControllerBase
    {
        private readonly Markdown mkdService;
        private readonly IHttpClient _httpClient;
        private readonly IJsonService _json;
        
        public MarkdownController(ILogger<Availability> logger, IContextBrand brandContext)
        {
            mkdService = new Markdown(logger, brandContext.Context, _httpClient, _json);
        }

        [Route("[action]"), EnableCors("AllowAnyOrigin"), HttpGet]
        public async Task<ActionResult<FullResponseData>> GetCredipaxData(string cpf, string locatorCode, int reservation) => await mkdService.GetCredipaxData(cpf, locatorCode, reservation);

        [Route("[action]"), EnableCors("AllowAnyOrigin"), HttpGet]
        public async Task<ActionResult<EstimatesResponse>> GetEstimates(EstimatesRequest request) => await mkdService.GetEstimates(request);
    }
}
