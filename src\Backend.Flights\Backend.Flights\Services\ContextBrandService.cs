﻿using Backend.Flights.CA.Services.UserToken;
using Backend.Flights.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Net;
using System.Text;

namespace Backend.Flights.Services
{
    public class ContextBrandService : IContextBrand
    {
        private readonly BrandContext _context;

        public BrandContext Context => _context;

        public ContextBrandService(
            ILogger<ContextBrandService> logger,
            IHttpContextAccessor httpContextAccessor,
            IContextBrands contextBrands,
            ICorpLoginService corpLogin)
        {
            var req = httpContextAccessor.HttpContext.Request;
            var brandUrlHeader = string.Empty;

            if (req.Headers.ContainsKey("X-BRAND-URL"))
                brandUrlHeader = req.Headers["X-BRAND-URL"];

            var host = string.IsNullOrEmpty(brandUrlHeader) ? req.Host.Host : brandUrlHeader;

            if (req.Cookies.ContainsKey(IContextBrand.COOKIE_ACCESS_TOKEN) &&
                req.Cookies.TryGetValue(IContextBrand.COOKIE_ACCESS_TOKEN, out var accessToken) &&
                !string.IsNullOrWhiteSpace(accessToken))
            {
                var parts = accessToken.Split('.', StringSplitOptions.RemoveEmptyEntries);
                if (parts.Length == 3)
                {
                    try
                    {
                        var base64Body = parts[1]
                            .Replace("-", "+")
                            .Replace("_", "/")
                            .Replace(".", "=");
                        var padding = base64Body.Length % 4;
                        if (padding > 0)
                        {
                            base64Body += new string('=', 4 - padding);
                        }
                        var jwtPayload = JsonConvert.DeserializeObject<Models.JWT.JWTPayload>(
                            Encoding.UTF8.GetString(Convert.FromBase64String(base64Body)));
                        var credential = jwtPayload?.Credential;
                        if (credential?.UserType?.Equals(IUserTokenService.USER_TYPE_VENDOR,
                                StringComparison.InvariantCultureIgnoreCase) == true &&
                            credential?.BranchId.HasValue == true &&
                            corpLogin.ValidateToken(accessToken))
                        {
                            _context = contextBrands.GetContext(IContextBrand.LOJA_CTX).Clone();
                            _context.GatewayHeaders.BranchId = credential?.BranchId?.ToString();

                            // [log-disabled] logger.LogInformation("User as vendor with branch-id {branchid}", _context.GatewayHeaders.BranchId);

                            return;
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogWarning(ex, "Fail on detect vendor context");
                    }
                }
            }

            _context = contextBrands.GetContext(host);
        }

        public void SetAuthHeaders(WebClient wc, BrandContext bc = null)
        {
            bc ??= _context;
            var gh = bc.GatewayHeaders;
            wc.Headers.Add(GatewayHeaders.USERNAME, gh.UserName);
            wc.Headers.Add(GatewayHeaders.PASSWORD, gh.Password);
            wc.Headers.Add(GatewayHeaders.BRANCHID, gh.BranchId);
            wc.Headers.Add(GatewayHeaders.AGENTSIGN, gh.AgentSign);
            wc.Headers.Add(GatewayHeaders.ACCEPT_ENCODING, gh.AcceptEncoding);

        }

        public BrandContext GetCurrentBrandContext()
        {
            return _context;

        }
    }
}