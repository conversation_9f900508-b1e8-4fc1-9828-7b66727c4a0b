using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Backend.Flights.Services
{
    public class RetryService : IRetry
    {
        private readonly ILogger _logger;

        public RetryService(ILogger<RetryService> logger)
        {
            _logger = logger;
        }

        public async Task<T> ResilientCall<T>(Func<Task<T>> action, int retries = 3, int timeIncrement = 100)
        {
            try
            {
                return await action();
            }
            catch (Exception ex)
            {
                if (retries <= 0)
                {
                    throw;
                }

                await Task.Delay(timeIncrement);

                _logger.LogInformation(ex, "Retray action after {timeIncrease}ms", timeIncrement);

                return await ResilientCall<T>(action, retries - 1, timeIncrement * 2);
            }
        }
    }
}