using System;
using System.Linq;
using Backend.Flights.Models.Branch;
using Microsoft.Extensions.Logging;
using static Backend.Flights.Services.IConsul;

namespace Backend.Flights.Services
{
    public class BranchConfigService : IBranchConfigService
    {
        private readonly ILogger<BranchConfigService> _logger;
        private readonly IConsul _consul;
        private const string BRANCH_CONFIG_URL = STR_PROJECT_NAME + "/v4.branch";

        public BranchConfigService(ILogger<BranchConfigService> logger, IConsul consul)
        {
            _logger = logger;
            _consul = consul;
        }

        public BranchConfig VerifyApiByBranch(int branchId)
        {
            try
            {
                var result = _consul.GetValueAsync(BRANCH_CONFIG_URL).Result;
                var branchIds = result.Split(",").Select(int.Parse).ToArray();
                return new BranchConfig(branchIds.Contains(branchId));
            }
            catch (Exception ex)
            {
                var errorMessage = $"O Valor da chave: '{BRANCH_CONFIG_URL}' do consul está inválida!";
                _logger.LogError(ex, errorMessage);
                return new BranchConfig(false, ex, errorMessage);
            }
        }
    }
}