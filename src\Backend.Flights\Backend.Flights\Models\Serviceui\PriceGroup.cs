﻿using System.Collections.Generic;

namespace Backend.Flights.Models.Serviceui
{
    public class PriceGroup
    {
        public decimal Price { get; set; }
        public int Installments { get; set; }
        public List<PriceGroupCityPair> CityPairs { get; set; }
        public string GroupingKey { get; set; }
        public decimal Tax { get; set; }
        public decimal Fee { get; set; }
        public decimal OriginalPrice { get; set; }
        public string SearchKey { get; set; }
    }

    public class PriceGroupCityPair
    {
        public string Origin { get; set; }
        public string Destination { get; set; }
        public List<PriceFlightGroupReference> FlightGroupsReference { get; set; }
        public string Type { get; set; }
    }

    public class PriceFlightGroupReference
    {
        public int FlightGroupIndex { get; set; }
        public string FlightGroupHash { get; set; }
        public List<int> RecommendationIndexes { get; set; }
    }
}
