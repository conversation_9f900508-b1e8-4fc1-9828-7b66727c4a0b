﻿using Backend.Flights.Business;
using Backend.Flights.CA.UseCase.AirLocation;
using Backend.Flights.CA.UseCase.RateToken;
using Backend.Flights.Models.GtwAereo;
using Backend.Flights.Models.Markdown.ImportaLoc;
using Backend.Flights.Services;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Backend.Flights.CA.UseCase.Loc
{
    public class LocImportedToCart
    {
        private static readonly Dictionary<string, string> dictPassengerTypes = new Dictionary<string, string> { { "ADT", "adult" }, { "CHD", "child" }, { "INF", "baby" } };
        private readonly SearchLocationByIata _searchLocationByIata;
        private readonly GetRateTokenPriceInfo _getRateTokenPriceInfo;

        public LocImportedToCart(SearchLocationByIata searchLocationByIata, GetRateTokenPriceInfo getRateTokenPriceInfo)
        {
            _searchLocationByIata = searchLocationByIata;
            _getRateTokenPriceInfo = getRateTokenPriceInfo;
        }

        private async Task<object> CreateLocation(string iata)
        {
            var location = await _searchLocationByIata.Execute(iata);

            return new
            {
                IATA = location.Iata,
                city = location.City,
                state = location.State,
                country = location.Country
            };
        }

        async Task<string> GetLegLocationDescription(string iata)
        {
            var location = await _searchLocationByIata.Execute(iata);
            return location.Description;
        }

        static object ToIataNameAndCode(AirCompany iataSource)
        {
            return new
            {
                name = iataSource.Name,
                iata = iataSource.Iata
            };
        }

        async Task<object> CreateCheckoutLegs(Segment segment)
        {
            return await Task.WhenAll((segment.Legs).Select(async (Leg leg) => new
            {
                departure = leg.Departure,
                departureAirport = await GetLegLocationDescription(leg.Departure),
                departureDate = leg.DepartureDate,
                arrival = leg.Arrival,
                arrivalAirport = await GetLegLocationDescription(leg.Arrival),
                arrivalDate = leg.ArrivalDate,
                operatedBy = ToIataNameAndCode(leg.OperatedBy),
                seatClass = leg.SeatClass?.Description ?? leg.SeatClass?.Code,
                duration = leg.Duration,
                flightNumber = leg.FlightNumber,
                managedBy = ToIataNameAndCode(leg.ManagedBy),
            }));
        }

        static object CreateBaggageInfo(Segment segment)
        {
            if (segment == null || segment?.FareProfile == null)
            {
                return new
                {
                    checkWeight = 0,
                };
            }

            return new
            {
                hand = segment.FareProfile.Services?.Any(s => s.Type == "HAND_LUGGAGE" && !s.IsIncluded) == true ? 0 : 1,
                @checked = decimal.ToInt32(segment.FareProfile.Baggage?.Quantity ?? 0),
                handWeight = segment.FareProfile.Services?.Any(s => s.Type == "HAND_LUGGAGE" && !s.IsIncluded) == true ? 0 : HandBaggageWeightService.parse(segment.FareProfile.Services?.FirstOrDefault(s => s.Type == "HAND_LUGGAGE")?.Description),
                checkWeight = decimal.ToInt32(segment.FareProfile.Baggage?.Weight ?? 0)
            };
        }

        async Task<object> CreateGroupSegments(Segment segment)
        {
            return new
            {
                legs = await CreateCheckoutLegs(segment),
                baggage = CreateBaggageInfo(segment),
                totalMinuteDuration = segment.Duration,
                numberOfStops = segment.NumberOfStops
            };
        }

        static int CountPax(string type, Booking booking)
        {
            return booking.Paxs.Where(pax => dictPassengerTypes[pax.PassengerType] == type).ToList().Count;
        }

        public ImportaLocResponse toImportaLocResponse(dynamic json)
        {
            var jsonResult = JsonConvert.SerializeObject(json);
            return JsonConvert.DeserializeObject<ImportaLocResponse>(jsonResult);
        }

        static bool IsRefundable(PriceGroup priceGroup)
        {
            var jsonResult = JsonConvert.SerializeObject(priceGroup);
            var result = JsonConvert.DeserializeObject<PriceGroup>(jsonResult);

            if (priceGroup.Segments == null || priceGroup.Segments[0].FareProfile == null)
                return false;

            return priceGroup.Segments.All(segment =>
            {
                return segment?.FareProfile?.Services?
                    .Find(service => service.Type != null && service.Type.Equals("REFUNDABLE"))
                    ?.IsIncluded ?? false;
            });
        }

        private static bool IsMultiDestinations(Model.Context.BrandContext brandContext, PriceGroup priceGroup)
        {
            var segments = priceGroup?.Segments ?? new List<Segment>();
            var cityPairs = segments.Select(x => new Models.Search.CityPair
            {
                OriginIata = x.Departure,
                DestinationIata = x.Arrival
            }).ToList();

            return Availability.GetSearchType(cityPairs, brandContext.IsCvc()) == Availability.SearchType.Multidestination;
        }

        private static dynamic getRateToken(PriceGroup priceGroup, string mainToken)
        {
            var segments = priceGroup?.Segments ?? new List<Segment>();

            return from segment in segments
                   select new
                   {
                       routeRPH = segment.RouteRPH,
                       packageGroup = segment.PackageGroup,
                       rateToken = segment.RouteRPH == 1 ? mainToken ?? segment.RateToken : segment.RateToken
                   };
        }

        public string getPackageGroup(ImportaLocResponse importaLocResponse)
        {
            Booking booking = importaLocResponse.AirBookings[0].Booking;
            PriceGroup priceGroup = booking.Airs[0].PriceGroup;
            return (string)priceGroup?.Segments?.FirstOrDefault()?.PackageGroup ?? "";
        }

        public string getLocStatus(ImportaLocResponse importaLocResponse)
        {
            return (string)importaLocResponse.AirBookings[0].Booking?.Status;
        }

        public async Task<object> Execute(Model.Context.BrandContext brandContext, ImportaLocResponse importaLocResponse, string loc, string source)
        {

            Booking booking = importaLocResponse.AirBookings[0].Booking;
            Air air = booking.Airs[0];
            PriceGroup priceGroup = air.PriceGroup;
            List<AirTicket> airTickets = air.AirTickets;
            string mainToken = air.BookingToken ?? air.ReservationToken;

            var fares = priceGroup.FareGroup.Fares;
            var fareAdt = fares.FirstOrDefault(f => f.PassengersType == "ADT");
            var finalPrice = fareAdt?.PriceWithTax;

            var firstSegment = priceGroup?.Segments?.FirstOrDefault();
            var lastSegment = priceGroup?.Segments?.LastOrDefault();
            var firstLeg = firstSegment?.Legs?.FirstOrDefault();
            var lastLeg = lastSegment?.Legs?.LastOrDefault();
            var departureLocation = firstLeg?.Departure == null ? null : await CreateLocation((string)firstLeg.Departure);
            var arrivalLocation = lastLeg?.Departure == null ? null : await CreateLocation((string)lastLeg.Arrival);
            bool isMultiDestinations = IsMultiDestinations(brandContext, priceGroup);

            var tokenPriceInfo = _getRateTokenPriceInfo.FromToken(priceGroup.Segments.First().RateToken);
            var segments = await Task.WhenAll((priceGroup?.Segments ?? new List<Segment>()).Select(CreateGroupSegments).ToList()).ConfigureAwait(false);

            return new
            {
                type = "flight",
                data = new
                {
                    importLoc = true,
                    player = source,
                    statusLoc = getLocStatus(importaLocResponse),
                    loc,
                    departureLocation,
                    arrivalLocation,
                    productStartDate = firstLeg?.DepartureDate,
                    productEndDate = lastLeg?.DepartureDate,
                    packageGroup = getPackageGroup(importaLocResponse),
                    paxGroup = new
                    {
                        adult = CountPax("adult", booking),
                        children = CountPax("child", booking),
                        babies = CountPax("baby", booking)
                    },
                    notify = new
                    {
                        costPrice = tokenPriceInfo?.costPrice,
                        finalPrice,
                    },
                    fareGroup = new
                    {
                        currency = (string)priceGroup?.FareGroup?.Currency,
                        priceWithTax = (double?)priceGroup?.FareGroup?.PriceWithTax,
                        priceWithoutTax = (double?)priceGroup?.FareGroup?.PriceWithoutTax,
                        refundable = IsRefundable(priceGroup),
                        rateTokens = getRateToken(priceGroup, mainToken),
                        discount = priceGroup?.FareGroup?.Discount,
                        discountPercentage = priceGroup?.FareGroup.DiscountPercentage,
                    },
                    isMulti = isMultiDestinations,
                    segments = segments,
                    airTickets = (airTickets ?? new List<AirTicket>()).Select((AirTicket airTicket) => new
                    {
                        code = (string)airTicket.Code,
                        status = (string)airTicket.Status,
                        creationDate = airTicket.CreationDate,
                        validatingBy = new
                        {
                            validatingBy = (string)airTicket.ValidatingBy?.Iata,
                        },
                        pax = new
                        {
                            passengerType = (string)airTicket.Pax?.PassengerType,
                            firstName = (string)airTicket.Pax?.FirstName,
                            lastName = (string)airTicket.Pax?.LastName,
                            gender = (string)airTicket.Pax?.Gender,
                        },
                        airTokens = (airTicket?.AirTokens ?? new List<AirToken>()).Select((AirToken airToken) => new
                        {
                            segment = new
                            {
                                departure = airToken?.Segment?.Departure,
                                departureDate = airToken?.Segment?.DepartureDate,
                                arrival = (string)airToken?.Segment?.Arrival,
                                arrivalDate = airToken?.Segment?.ArrivalDate,
                                rateToken = (string)airToken?.Segment?.RateToken,
                                numberOfStops = (double?)airToken?.Segment?.NumberOfStops,
                                duration = (double?)airToken?.Segment?.Duration,
                                legs = (airToken?.Segment?.Legs ?? new List<Leg>()).Select((Leg leg) => new
                                {
                                    managedBy = (string)leg.ManagedBy?.Iata,
                                    operatedBy = (string)leg.OperatedBy?.Iata,
                                    gdsStatus = (string)leg.GdsStatus,
                                    status = (string)leg.Status,
                                    flightNumber = (string)leg.FlightNumber,
                                    numberOfStops = (double?)leg.NumberOfStops,
                                    stops = leg.Stops != null ? (object)leg.Stops : null,
                                    duration = leg?.Duration != null ? (double?)leg.Duration : null,
                                    departure = (string)leg?.Departure,
                                    departureDate = leg?.DepartureDate != null ? leg?.DepartureDate : null,
                                    arrival = (string)leg?.Arrival,
                                    arrivalDate = leg?.ArrivalDate != null ? leg?.ArrivalDate : null,
                                    aircraftCode = (string)leg?.AircraftCode,
                                    seatClass = leg?.SeatClass != null ? (string)leg.SeatClass.Code : null,
                                    fareBasis = leg?.FareBasis != null ? (string)leg.FareBasis : null,
                                }),
                                national = airToken?.Segment?.National ?? false,
                            },
                            reservationToken = (string)airToken?.ReservationToken,
                        }),
                        fare = new
                        {
                            passengensCount = airTicket?.Fare?.passengersCount ?? 0,
                            priceWithTax = airTicket?.Fare?.priceWithTax ?? 0,
                            priceWithoutTax = airTicket?.Fare?.priceWithoutTax ?? 0,
                            taxes = (airTicket?.Fare?.taxes ?? new List<Models.Markdown.Tax>()).Select((Models.Markdown.Tax tax) => new
                            {
                                code = (string)tax?.code,
                                values = (tax?.values ?? new List<Models.Markdown.Value>()).Select((Models.Markdown.Value value) => new
                                {
                                    currency = (string)value?.currency,
                                    amount = value?.amount ?? 0,
                                }),
                            }),
                        },
                        payments = (airTicket?.Payments ?? new List<Payment>()).Select((Payment payment) => new
                        {
                            type = (string)payment?.Type,
                            currency = (string)payment?.Currency,
                            value = payment?.Value ?? 0,
                        }),
                        gds = new
                        {
                            provider = (string)airTicket?.Gds?.Provider,
                            reservationCode = (string)airTicket?.Gds?.ReservationCode,
                        },
                    }),
                }
            };
        }
    }
}
