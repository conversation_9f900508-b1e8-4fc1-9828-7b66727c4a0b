﻿using Backend.Flights.Util;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Driver;
using StackExchange.Redis;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;

namespace Backend.Flights.Services
{
    public interface ICacheService
    {
        Task BatchAdd(Dictionary<string, string> data, int expirySeconds, ILogger logger);
        Task<string> GetValue(string key);
        Task<string[]> GetValue(IEnumerable<string> keys);
    }

    public sealed class CacheService : ICacheService
    {
        private ICacheService svc;
        public CacheService(ConfigurationManager cfgMgr)
        {
            if (cfgMgr.AppSetting["CacheType"] == "MONGO")
                svc = new CacheServiceMongo(cfgMgr);
            else
                svc = new CacheServiceRedis(cfgMgr);
        }

        async public Task BatchAdd(Dictionary<string, string> data, int expirySeconds, ILogger logger) => await svc.BatchAdd(data, expirySeconds, logger);
        async public Task<string> GetValue(string key) => (await GetValue(new string[] { key }))[0];
        async public Task<string[]> GetValue(IEnumerable<string> keys) => await svc.GetValue(keys);

        private sealed class CacheServiceRedis : ICacheService
        {
            const string KEYPREFIX = "CACHE";
            private readonly ConnectionMultiplexer redisConn;
            public CacheServiceRedis(ConfigurationManager cfgMgr)
            {
                // #if LOCAL  // Produção Local
                //                 var redisUrl = "localhost";
                // #else
                if (cfgMgr.AppSetting.TryGetValue("Redis.Url", out string redisUrl) && !string.IsNullOrEmpty(redisUrl))
                    //#endif
                    redisConn = ConnectionMultiplexer.Connect(redisUrl);
            }

            async public Task<string> GetValue(string key) => (await GetValue(new string[] { key }))[0];
            async public Task<string[]> GetValue(IEnumerable<string> keys)
            {
                if (redisConn == null)
                    throw new Exception("Redis not configured");

                var rediskeys = new List<RedisKey>();
                foreach (var k in keys)
                    rediskeys.Add($"{KEYPREFIX}-{k}");

                if (rediskeys.Count == 0)
                    return new string[0];

                var db = redisConn.GetDatabase();
                var results = await db.StringGetAsync(rediskeys.ToArray());
                return results.Select(x => (string)x).ToArray();
            }

            async public Task BatchAdd(Dictionary<string, string> data, int expirySeconds, ILogger logger)
            {
                if (redisConn == null)
                    throw new Exception("Redis not configured");

                var sw = new Stopwatch();
                sw.Start();

                var ret = await batchAdd(data, expirySeconds);

                sw.Stop();
                if (ret)
                {
                    // [log-disabled] log(logger, "BatchAdd", sw.ElapsedMilliseconds, $"{data.Count} valores inseridos.");
                }
                else
                {
                    log(logger, "ErrorBatchAdd", sw.ElapsedMilliseconds, $"{data.Count} valores deveriam ser inseridos.");
                    throw new Exception("ErrorAddingToRedis");
                }
            }

            private void log(ILogger logger, string action, long duration, string msg)
            {
                if (logger == null) return;

                using (logger.BeginScope(new Dictionary<string, object> { ["duration"] = duration, ["short_message"] = action, ["operation"] = "REDISADD" }))
                    logger.LogInformation(msg);
            }

            async private Task<bool> batchAdd(Dictionary<string, string> data, int expirySeconds)
            {
                const int batchSize = 100;

                var keys = new List<RedisKey>(batchSize);
                var values = new List<RedisValue>(batchSize);

                //O método de envio em batch padrão do Redis não possui expiração. Como a expiração é importante
                //para descartar valores que não serão mais utilizados, enviaremos os dados em bloco juntamente com um
                //script em Lua que chamará o Set, que tem como setar expiração.
                var lua = $@"
                local count = 0
                for i, key in ipairs(KEYS) do
                    redis.call('SETEX', '{KEYPREFIX}-' .. key, {expirySeconds}, ARGV[i])
                    count = count + 1
                end
                return count";

                var db = redisConn.GetDatabase();
                foreach (var pair in data)
                {
                    keys.Add(pair.Key);
                    values.Add(pair.Value);

                    if (keys.Count == batchSize)
                    {
                        if (!await sendBatch())
                            return false;

                        keys.Clear();
                        values.Clear();
                    }
                }
                if (keys.Count != 0 && !await sendBatch())
                    return false;
                return true;

                async Task<bool> sendBatch()
                {
                    var count = (int)await db.ScriptEvaluateAsync(lua, keys.ToArray(), values.ToArray());
                    return count == keys.Count;
                }
            }
        }

        private sealed class CacheServiceMongo : ICacheService
        {
            public class Connection
            {
                public string Server { get; set; }
                public string User { get; set; }
                public string Pwd { get; set; }
                public string Database { get; set; }
                public bool IsAWS { get; set; }
                public bool IsAutonomous { get; set; } // Oracle Autonomous JSON Database

                public string BuildConnectionString()
                {
                    var userPwd = $"{this.User}:{this.Pwd}";
                    userPwd = userPwd == ":" ? "" : $"{userPwd}@";

                    // #if LOCAL // Produção Local
                    // return $"mongodb://{userPwd}localhost:27017/{this.Database}?authSource=admin";
                    // #endif

                    if (IsAutonomous)
                    {
                        // Sobrescreve as propriedades com os valores do ambiente
                        this.User = Environment.GetEnvironmentVariable("MONGODB_USERNAME_OCI");
                        this.Pwd = Environment.GetEnvironmentVariable("MONGODB_PASSWORD_OCI");

                        if (string.IsNullOrEmpty(this.User) || string.IsNullOrEmpty(this.Pwd))
                        {
                            throw new Exception("MongoDB credentials not found in Vault");
                        }

                        // Monta a string de conexão usando os valores do ambiente
                        return $"mongodb://{this.User}:{this.Pwd}@{this.Server}/" +
                               $"{this.Database}?authMechanism=PLAIN&authSource=$external&ssl=true&retryWrites=false&loadBalanced=true";
                    }
                    else
                    {
                        // Configuração antiga para MongoDB normal usando os valores do consult (configuração)
                        if (!IsAWS)
                        {
                            return $"mongodb+srv://{userPwd}{this.Server}/{this.Database}?authSource=admin&retryWrites=true&w=majority";
                        }
                        else
                        {
                            return $"mongodb://{userPwd}{this.Server}/{this.Database}?authSource={this.Database}&readPreference=primary&appname=sub-backend-flights&ssl=true";
                        }
                    }
                }
            }

            private class CacheData
            {
                [BsonRepresentation(BsonType.ObjectId)]
                public string _id { get; set; }
                public string key { get; set; }
                public string value { get; set; }
                public DateTime expiration { get; set; }
            }

            private readonly IMongoCollection<CacheData> cacheCollection;

            public CacheServiceMongo(ConfigurationManager cfgMgr)
            {
                try
                {
                    "[CacheServiceMongo]: Inicializando CacheServiceMongo".LogInfo();

                    Connection connData = null;

                    // #if LOCAL // LOCALPRODUCAO (Local Produção)
                    // //connData = new Connection { Server = "localhost:27017", User = "", Pwd = "", Database = "db-flights", IsAWS = false };
                    // // connData = new Connection { Server = "mapa01-qa.addqm.mongodb.net", User = "flights", Pwd = "flights123", Database = "db-flights", IsAWS = false };
                    // connData = new Connection { Server = "iz8itxfl.adb.sa-saopaulo-1.oraclecloudapps.com:27017", User = "db_flights", Pwd = "joAW2gPg0Se2dsQHzZx0Ny79j", Database = "db_flights", IsAWS = false, IsAutonomous = true };
                    // #else
                    if (cfgMgr.AppSetting.TryGetValue("Mongo.ConnectionData", out string mongoConnData))
                        connData = Newtonsoft.Json.JsonConvert.DeserializeObject<Connection>(mongoConnData);
                    // #endif

                    if (connData != null)
                    {
                        "[CacheServiceMongo]: Inicializando Configurações do MongoClient".LogInfo();

                        var connectionString = connData.BuildConnectionString();
                        $"[CacheServiceMongo]: String de Conexão: {ConnectionStringMasker.MaskMongoDbPassword(connectionString)}".LogInfo();

                        //var settings = MongoClientSettings.FromUrl(new MongoUrl(connectionString));
                        ////settings.AllowInsecureTls = true;

                        // #if LOCAL // Produção Local (mongo local)
                        // settings.AllowInsecureTls = false;
                        // #endif

                        var client = new MongoClient(connectionString);

                        // No Oracle Autonomous JSON Database, o nome do banco é o mesmo do usuário
                        var databaseName = connData.IsAutonomous ? connData.User : connData.Database;
                        var database = client.GetDatabase(databaseName);

                        $"[CacheServiceMongo]: Conectado ao banco de dados: {database.DatabaseNamespace.DatabaseName}".LogInfo();

                        cacheCollection = database.GetCollection<CacheData>("rateTokensAndFareProfiles");

                        "[CacheServiceMongo]: Coleção 'rateTokensAndFareProfiles' obtida".LogInfo();

                        if (!connData.IsAutonomous)
                        {
                            var keys = Builders<CacheData>.IndexKeys;
                            cacheCollection.Indexes.CreateMany(new[] {
                                new CreateIndexModel<CacheData>(keys.Ascending(x => x.key), new CreateIndexOptions { Name = "key", Unique = true }),
                                new CreateIndexModel<CacheData>(keys.Ascending(x => x.expiration), new CreateIndexOptions { Name = "expirationIndex", ExpireAfter = TimeSpan.Zero }) // TTL Index
                            });
                        }
                        else
                        {
                            // Foi comentado porque no inicio não estava suportando esses index, depois de uma atualização do pessoal começou a suportar
                            // "[CacheServiceMongo]: Pulando criação do índice TTL no Oracle Autonomous JSON Database.".LogInfo();
                            // Criar apenas índice único no Oracle Autonomous
                            // var keys = Builders<CacheData>.IndexKeys;
                            // cacheCollection.Indexes.CreateOne(new CreateIndexModel<CacheData>(
                            //     keys.Ascending(x => x.key), new CreateIndexOptions { Name = "key", Unique = true }));

                            "[CacheServiceMongo]: Iniciando criação de índices no Oracle Autonomous JSON Database.".LogInfo();

                            var keys = Builders<CacheData>.IndexKeys;
                            cacheCollection.Indexes.CreateMany(new[] {
                                new CreateIndexModel<CacheData>(keys.Ascending(x => x.key), new CreateIndexOptions { Name = "key", Unique = true }),
                                new CreateIndexModel<CacheData>(keys.Ascending(x => x.expiration), new CreateIndexOptions { Name = "expirationIndex", ExpireAfter = TimeSpan.Zero }) // TTL Index
                            });
                        }

                        "[CacheServiceMongo]: Índices criados na coleção 'rateTokensAndFareProfiles'".LogInfo();
                    }
                    $"[CacheServiceMongo]: Inicializado com sucesso!".LogInfo();
                }
                catch (Exception ex)
                {
                    $"[CacheServiceMongo]: Erro ao inicializar CacheServiceMongo: {ex.GetErrorLocation()}".LogError();
                    throw;
                }
            }

            async public Task<string> GetValue(string key) => (await GetValue(new string[] { key }))[0];
            async public Task<string[]> GetValue(IEnumerable<string> keys)
            {
                if (cacheCollection == null)
                    throw new Exception("mongo not configured");

                if (!keys.Any())
                    return new string[0];

                var filter = new FilterDefinitionBuilder<CacheData>().In(x => x.key, keys);
                var mongoData = (await cacheCollection.FindAsync(filter)).ToEnumerable().ToDictionary(x => x.key, x => x.value);
                return (from x in keys select mongoData.ContainsKey(x) ? mongoData[x] : null).ToArray();
            }

            async public Task BatchAdd(Dictionary<string, string> data, int expirySeconds, ILogger logger)
            {
                if (cacheCollection == null)
                    throw new Exception("mongo not configured");

                var sw = Stopwatch.StartNew();

                try
                {
                    await batchAdd(data, expirySeconds);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[CacheServiceMongo] ERRO no BatchAdd: {ex.Message}");
                    Console.WriteLine($"[CacheServiceMongo] Stack Trace do BatchAdd: {ex.StackTrace}");
                    throw; // Mantém o stack trace original
                }

                sw.Stop();
                // [log-disabled] log(logger, "BatchAdd", sw.ElapsedMilliseconds, $"{data.Count} valores inseridos.");
            }

            private void log(ILogger logger, string action, long duration, string msg)
            {
                if (logger == null) return;

                using (logger.BeginScope(new Dictionary<string, object> { ["duration"] = duration, ["short_message"] = action, ["operation"] = "MONGOADD" }))
                    logger.LogInformation(msg);
            }

            async private Task batchAdd(Dictionary<string, string> data, int expirySeconds)
            {
                try
                {
                    Console.WriteLine($"[CacheServiceMongo] Iniciando batchAdd com {data.Count} registros");

                    var cacheData = from x in data select new CacheData { key = x.Key, value = x.Value, expiration = DateTime.UtcNow.AddSeconds(expirySeconds) };

                    // foreach (var item in data)
                    // {
                    //     Console.WriteLine($"[CacheServiceMongo] Registro - Key: {item.Key}, Tamanho do Value: {item.Value?.Length ?? 0} caracteres");
                    // }

                    // Deletando em lotes de 500 registros
                    // Feito isso pois no autonomous JSON Database da Oracle, o DeleteManyAsync estava dando erro
                    // ORA-01704: string literal too long -> https://docs.oracle.com/error-help/db/ora-01704

                    const int batchSize = 500;
                    var keys = data.Keys.ToList();

                    Console.WriteLine($"[CacheServiceMongo] Deletando {keys.Count} chaves em lotes de {batchSize} registros");

                    for (int i = 0; i < keys.Count; i += batchSize)
                    {
                        var batchKeys = keys.Skip(i).Take(batchSize);
                        Console.WriteLine($"[CacheServiceMongo] Deletando lote {i / batchSize + 1} de {(keys.Count + batchSize - 1) / batchSize}");

                        try
                        {
                            await cacheCollection.DeleteManyAsync(
                                Builders<CacheData>.Filter.Or(
                                    batchKeys.Select(d => Builders<CacheData>.Filter.Eq(m => m.key, d))
                                )
                            );
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"[CacheServiceMongo] ERRO ao deletar lote {i / batchSize + 1}: {ex.Message}");
                            // Continua com o próximo lote mesmo se houver erro
                        }
                    }

                    Console.WriteLine("[CacheServiceMongo] Iniciando InsertManyAsync");
                    await cacheCollection.InsertManyAsync(cacheData);
                    Console.WriteLine("[CacheServiceMongo] InsertManyAsync concluído");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"[CacheServiceMongo] ERRO no batchAdd: {ex.Message}");
                    Console.WriteLine($"[CacheServiceMongo] Stack Trace: {ex.StackTrace}");
                    throw;
                }
            }
        }
    }
}