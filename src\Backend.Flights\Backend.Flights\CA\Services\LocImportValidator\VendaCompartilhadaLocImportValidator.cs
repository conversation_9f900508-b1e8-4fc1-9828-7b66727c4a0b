﻿using System;
using Backend.Flights.CA.Model.LocImport;
using Backend.Flights.CA.Services.Config;
using Backend.Flights.CA.Services.Http;
using Backend.Flights.CA.Services.Json;
using Backend.Flights.CA.UseCase.Config;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using System.Web;

namespace Backend.Flights.CA.Services.LocImportValidator
{
    public class VendaCompartilhadaLocImportValidator : ILocImportValidator
    {
        private readonly ILogger _logger;
        private readonly IHttpClient _httpClient;
        private readonly IWatchConfig _watchConfig;
        private readonly IJsonService _json;

        public VendaCompartilhadaLocImportValidator(IHttpClient httpClient, IWatchConfig watchConfig, ILogger<VendaCompartilhadaLocImportValidator> logger, IJsonService json)
        {
            _httpClient = httpClient;
            _watchConfig = watchConfig;
            _logger = logger;
            _json = json;
        }

        public async Task<SafeResult<ImportLocValidationResponse, Tuple<int, string>>> Validate(string loc, string source, string packageGroup, int branch, string userToken)
        {
            var url = await _watchConfig.GetConfig(IConfigService.IMPORTALOC_VALIDATE_URL);
            var (res, httpError, httpResponse) = await _httpClient.GetString($"{url}codigoFilial={branch}&localizador={HttpUtility.UrlEncode(loc)}&packageGroup={HttpUtility.UrlEncode(packageGroup)}&source={HttpUtility.UrlEncode(source)}",
                new System.Collections.Generic.Dictionary<string, string>()
                {
                {"gtw-sec-user-token", userToken },
                {"gtw-transaction-id", $"atlas-{Guid.NewGuid()}" },
                });

            if (httpError != null)
            {
                _logger.LogError("[IMPORTALOC]: Fail on request loc import falidation. http error: {httpError}. message: {message}. body: {body}",
                    httpError.HttpErrorCode,
                    httpError.Message,
                    httpError.Body);
                return SafeResult<ImportLocValidationResponse, Tuple<int, string>>.Fail((httpError.HttpErrorCode, httpError.Message).ToTuple());
            }

            return SafeResult<ImportLocValidationResponse, Tuple<int, string>>.Success(_json.Deserialize<ImportLocValidationResponse>(res));
        }
    }

}