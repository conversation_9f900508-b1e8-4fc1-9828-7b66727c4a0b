using System;
using System.IO;
using System.Text;

namespace Backend.Flights.Util
{
    /// <summary>
    /// Utilitários para compressão e descompressão de dados usando GZIP
    /// </summary>
    /// <remarks>
    /// Esta classe fornece métodos para comprimir e descomprimir strings usando o algoritmo GZIP.
    /// Útil para reduzir o tamanho de dados JSON antes de salvar no cache ou banco de dados.
    /// 
    /// Exemplo de uso:
    /// <code>
    /// var originalData = JsonConvert.SerializeObject(largeObject);
    /// var compressedData = CompressionUtil.CompressString(originalData);
    /// // Salvar compressedData no cache/banco
    /// 
    /// // Para recuperar:
    /// var decompressedData = CompressionUtil.DecompressString(compressedData);
    /// var originalObject = JsonConvert.DeserializeObject<MyType>(decompressedData);
    /// </code>
    /// </remarks>
    public static class CompressionUtil
    {
        /// <summary>
        /// Comprime uma string usando GZIP e retorna como Base64
        /// </summary>
        /// <param name="input">String a ser comprimida</param>
        /// <returns>String comprimida em formato Base64, ou a string original se vazia</returns>
        /// <remarks>
        /// A compressão GZIP é ideal para dados JSON pois pode reduzir o tamanho em 70-90%.
        /// O resultado é codificado em Base64 para garantir compatibilidade com sistemas de cache.
        /// </remarks>
        public static string CompressString(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            var bytes = Encoding.UTF8.GetBytes(input);
            using var msi = new MemoryStream(bytes);
            using var mso = new MemoryStream();
            using (var gs = new System.IO.Compression.GZipStream(mso, System.IO.Compression.CompressionMode.Compress))
            {
                msi.CopyTo(gs);
            }
            return Convert.ToBase64String(mso.ToArray());
        }

        /// <summary>
        /// Descomprime uma string que foi comprimida com GZIP
        /// </summary>
        /// <param name="input">String comprimida em formato Base64</param>
        /// <returns>String descomprimida, ou a string original se a descompressão falhar</returns>
        /// <remarks>
        /// Este método é tolerante a falhas - se a string não estiver comprimida ou se houver erro
        /// na descompressão, retorna a string original. Isso garante compatibilidade com dados
        /// existentes que podem não estar comprimidos.
        /// </remarks>
        public static string DecompressString(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            try
            {
                var bytes = Convert.FromBase64String(input);
                using var msi = new MemoryStream(bytes);
                using var mso = new MemoryStream();
                using (var gs = new System.IO.Compression.GZipStream(msi, System.IO.Compression.CompressionMode.Decompress))
                {
                    gs.CopyTo(mso);
                }
                return Encoding.UTF8.GetString(mso.ToArray());
            }
            catch
            {
                // Se falhar na descompressão, retorna o valor original (para compatibilidade)
                return input;
            }
        }

        /// <summary>
        /// Calcula a taxa de compressão entre o tamanho original e comprimido
        /// </summary>
        /// <param name="originalSize">Tamanho original em bytes</param>
        /// <param name="compressedSize">Tamanho comprimido em bytes</param>
        /// <returns>Taxa de compressão em porcentagem (0-100)</returns>
        public static double CalculateCompressionRatio(int originalSize, int compressedSize)
        {
            if (originalSize == 0) return 0;
            return (double)compressedSize / originalSize * 100;
        }

        /// <summary>
        /// Verifica se uma string está comprimida (tenta decodificar como Base64)
        /// </summary>
        /// <param name="input">String a ser verificada</param>
        /// <returns>True se a string parece estar comprimida</returns>
        public static bool IsCompressed(string input)
        {
            if (string.IsNullOrEmpty(input))
                return false;

            try
            {
                Convert.FromBase64String(input);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
} 