using Backend.Flights.CA.UseCase.BrandContext;
using Backend.Flights.CA.UseCase.Loc;
using Backend.Flights.Models.Booking;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Backend.Flights.Controllers
{
    [ApiController, ApiVersion("1.0"), Route("api/[controller]")]
    public class BookingController : Controller
    {
        private readonly ILogger<BookingController> _logger;
        private readonly LocImport _locImport;

        public BookingController(
            ILogger<BookingController> logger,
            LocImport locImport)
        {
            _logger = logger;
            _locImport = locImport;
        }

        [HttpPost("{locatorCode}")]
        [EnableCors("AllowAnyOrigin")]
        public async Task<object> ImportaLoc(
            string locatorCode,
            [FromBody] BookingImportaLocRequest request,
            [FromServices] IGetCurrentBrandContext getCurrentBrandContext)
        {
            JsonResult res;

            try
            {
                if (string.IsNullOrWhiteSpace(locatorCode) ||
                    string.IsNullOrWhiteSpace(request.Source) ||
                    string.IsNullOrWhiteSpace(request.PackageGroup)
                   )
                {
                    res = Json(new
                    {
                        ttl = "Requisição inválida",
                        msg = "É necessário informar todos os parâmetros"
                    });
                    res.StatusCode = 400;
                    return res;
                }

                var brandContext = await getCurrentBrandContext.Execute(null, true);
                var importedLoc = await _locImport.Execute(brandContext, locatorCode, request.Source, request.PackageGroup);

                if (importedLoc.IsError)
                {
                    var error = importedLoc.Error;
                    if (error.IsHttpError)
                    {
                        res = Json(new
                        {
                            ttl = "Falha de execução",
                            msg = "Não foi possível realizar a busca",
                            extraInfo = new
                            {
                                error.HttpCode,
                                error.HttpMessage,
                                error.GtwErrorCode
                            }
                        });
                        res.StatusCode = 500;
                        return res;
                    }

                    res = Json(new
                    {
                        ttl = "Ops, código inválido",
                        msg = error.ErrorMessage
                    });
                    res.StatusCode = 422;
                    return res;
                }

                return Json(importedLoc.Result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Falha ao realizar importaloc");
                res = Json(new
                {
                    ttl = "Falha de execução",
                    msg = "Não foi possível realizar a busca",
                    extraInfo = new
                    {
                        error = ex.Message
                    }
                });
                res.StatusCode = 500;
                return res;
            }
        }
    }
}