using Backend.Flights.CA.Model.Opportunities;
using Backend.Flights.CA.Model.Promotions;
using Backend.Flights.CA.Services.Json;
using System.Collections.Generic;
using System.Linq;

namespace Backend.Flights.CA.UseCase.Opportunities.Service
{
    public class GetPromotionsIndividuals : IGetPromotionsIndividuals
    {
        private readonly IJsonService _json;

        public GetPromotionsIndividuals(IJsonService json)
        {
            _json = json;
        }

        public PromotionsResponse Execute(OpportunitiesResponse opportunities)
        {
            return new PromotionsResponse()
            {
                AvailableItems = opportunities
                    .AvailableItems
                    ?.Select((i) => ParseItem(i, _json))
                    .ToList(),
                SelectedItems = opportunities
                    .SelectedItems
                    ?.Select((i) => ParseItem(i, _json))
                    .ToList(),
            };
        }

        private static PromotionItemResponse ParseItem(OpportunitiesResponseItem opportunity, IJsonService json)
        {
            if (opportunity == null)
            {
                return null;
            }

            var hasCombo = opportunity.Original?.hasCombo == true;
            var promotion = opportunity.Original?.promotion;
            var statements = promotion?.statements;

            var res = new PromotionItemResponse(
                opportunity.RPH,
                opportunity.RateToken,
                opportunity.RateToken,
                json.Deserialize<PromotionInfo>(json.Serialize((promotion))) ?? new PromotionInfo(),
                json.Deserialize<List<Statement>>(json.Serialize((statements))),
                opportunity.RouteRPH
                );

            if (hasCombo != true) return res;

            res.Promotion.HasCombo = true;

            return res;
        }
    }
}