using System.Collections.Generic;
using System.Net.Http.Headers;
using System.Runtime.Serialization;

namespace Backend.Flights.Models
{
    public class BrandContext
    {
        public const string CVC_USERNAME = "scvc";
        public string GatewayUrl { get; set; }
        public string GatewayUrlV1 { get; set; }

        public string PromotionsApiGtwUrl { get; set; }

        public GatewayHeaders GatewayHeaders { get; set; }
        public CredipaxData CredipaxData { get; set; }
        public Partner PartnerData { get; set; }

        public ContentProvider ContentProvider { get; set; }

        public bool isCvc()
        {
            return GatewayHeaders.UserName.Equals(CVC_USERNAME);
        }

        public BrandContext Clone()
        {
            return new BrandContext()
            {
                GatewayUrl = GatewayUrl,
                GatewayUrlV1 = GatewayUrlV1,
                PromotionsApiGtwUrl = PromotionsApiGtwUrl,
                GatewayHeaders = GatewayHeaders == null ? null : new GatewayHeaders()
                {
                    UserName = GatewayHeaders.UserName,
                    Password = GatewayHeaders.Password,
                    BranchId = GatewayHeaders.BranchId,
                    AgentSign = GatewayHeaders.AgentSign,
                    AcceptEncoding = GatewayHeaders.AcceptEncoding,
                },
                CredipaxData = CredipaxData == null ? null : new CredipaxData()
                {
                    Url = CredipaxData.Url,
                    UserToken = CredipaxData.UserToken,
                    AllowedProviders = (string[])CredipaxData.AllowedProviders?.Clone(),
                    CheckAvailability = CredipaxData.CheckAvailability == null ? null : new CredipaxData.Availability()
                    {
                        Url = CredipaxData.CheckAvailability.Url,
                        UserToken = CredipaxData.CheckAvailability.UserToken,
                    },
                },
                PartnerData = PartnerData == null ? null : new Partner()
                {
                    Authorization = PartnerData.Authorization,
                    Name = PartnerData.Name,
                    BrokerUrl = PartnerData.BrokerUrl,
                },
                ContentProvider = ContentProvider == null ? null : new ContentProvider()
                {
                    Use = ContentProvider.Use,
                    Url = ContentProvider.Url,
                },
            };
        }
    }

    [DataContract]
    public class GatewayHeaders
    {
        public const string USERNAME = "Gtw-Username", PASSWORD = "Gtw-Password", BRANCHID = "Gtw-Branch-Id", AGENTSIGN = "Gtw-Agent-Sign", ACCEPT_ENCODING = "Accept-Encoding";

        [DataMember(Name = USERNAME)]
        public string UserName { get; set; }

        [DataMember(Name = PASSWORD)]
        public string Password { get; set; }

        [DataMember(Name = BRANCHID)]
        public string BranchId { get; set; }

        [DataMember(Name = AGENTSIGN)]
        public string AgentSign { get; set; }

        [DataMember(Name = ACCEPT_ENCODING)]
        public string AcceptEncoding { get; set; }

        public void SetHeaders(HttpRequestHeaders req)
        {
            req.Add(USERNAME, UserName);
            req.Add(PASSWORD, Password);
            req.Add(BRANCHID, BranchId);
            req.Add(AGENTSIGN, AgentSign);
            req.Add(ACCEPT_ENCODING, "gzip");
        }

        public Dictionary<string, string> GetHeaders()
        {
            return new Dictionary<string, string>()
            {
                {USERNAME, UserName},
                {PASSWORD, Password},
                {BRANCHID, BranchId},
                {AGENTSIGN, AgentSign},
                {ACCEPT_ENCODING, AcceptEncoding},
            };
        }
    }

    public class CredipaxData
    {
        public string Url { get; set; }
        public string UserToken { get; set; }
        public string[] AllowedProviders { get; set; }
        public Availability CheckAvailability { get; set; }

        public class Availability
        {
            public string Url { get; set; }
            public string UserToken { get; set; }
        }
    }

    public class Partner
    {
        public string Name { get; set; }

        public string Authorization { get; set; }

        public string BrokerUrl { get; set; }

    }

    public class ContentProvider
    {
        public float Use { get; set; }
        public string Url { get; set; }
    }
}