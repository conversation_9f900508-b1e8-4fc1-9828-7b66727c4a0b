﻿using Backend.Flights.CA.Services.Http;
using Backend.Flights.CA.Services.Json;
using Backend.Flights.Models;
using Backend.Flights.Models.Partners;
using Backend.Flights.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace Backend.Flights.Business
{
    public class Partner
    {
        private readonly PartnerService svc;
        private readonly BrandContext brandCtx;
        private readonly ILogger _logger;
        private readonly IHttpClient _httpClient;
        private readonly IJsonService _json;


        public Partner(ILogger logger, BrandContext brandCtx, IHttpClient httpClient, IJsonService json)
        {
            this._logger = logger;
            this.brandCtx = brandCtx;
            this._httpClient = httpClient;
            this._json = json;
            svc = new PartnerService(_logger, brandCtx, _httpClient, _json);
        }

        public async Task GetPartnerFlights(PartnerSearchRequest request, HttpResponse originResponse)
        {
            var service = new PartnerService(_logger, brandCtx, _httpClient, _json);

            await service.SearchFlightsPartnerPrecification(request.BuildPartnerRequest(_logger, brandCtx), originResponse);
        }


    }
}
