﻿using System.Collections.Generic;

namespace Backend.Flights.Models.Locations
{
    public class LocationsResponse
    {
        public List<Location> Locations { get; set; }
    }

    public class Location
    {
        public Location()
        {
        }

        public Location(
        string iata = null,
        string city = null,
        string state = null,
        string country = null
        )
        {
            Iata = iata;
            City = city;
            State = state;
            Country = country;
        }

        public string Iata { get; set; }
        public string Description { get; set; }
        public int? Mastercode { get; set; }
        public int Hits { get; set; }
        public double Latitude { get; set; }
        public double Longitude { get; set; }
        public string Type { get; set; }
        public string City { get; set; }
        public string State { get; set; }
        public string Country { get; set; }
    }

    public class LocationComparer : IEqualityComparer<Location>
    {
        public bool Equals(Location a, Location b)
        {
            return (a.Iata.Equals(b.Iata) && a.Type.Equals(b.Type));
        }

        public int GetHashCode(Location loc)
        {
            int hashIataCode = string.IsNullOrEmpty(loc.Iata) ? 0 : loc.Iata.GetHashCode();
            int hashTypeCode = string.IsNullOrEmpty(loc.Type) ? 0 : loc.Type.GetHashCode();

            return hashIataCode ^ hashTypeCode;
        }
    }
}
