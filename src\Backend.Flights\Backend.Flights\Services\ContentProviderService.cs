using Backend.Flights.CA.Services.ContentProvider;
using Backend.Flights.Models;
using System;
using System.Linq;

namespace Backend.Flights.Services
{
    public class ContentProviderService
    {
        #region Constants

        public const string SOURCE_GDS = "GDS";
        public const string SOURCE_SABRE = "SAB";
        public const string SOURCE_AMADEUS = "AMD";
        public const string SOURCE_GALILEU = "GLL";

        #endregion

        #region Static variables

        private static readonly string[] _gds = { SOURCE_SABRE, SOURCE_AMADEUS, SOURCE_GALILEU };

        private static readonly Random rnd = new Random();

        #endregion

        #region Singleton

        public static ContentProviderService Instance { get; } = new ContentProviderService();

        private ContentProviderService() { }

        #endregion

        #region Public functions

        public bool ShouldUseContentProvider(BrandContext context)
        {
            var cp = context.ContentProvider?.Use ?? 0;
            return cp == 1 ||
                   (cp > 0 && rnd.NextDouble() <= cp);
        }

        public string GetRequestUrl(BrandContext brandContext)
        {
            return brandContext.ContentProvider.Url + IContentProvider.URL_SUFIX;
        }

        public string[] filterSources(string[] sources)
        {
            if (sources.Any(s => _gds.Contains(s)))
            {
                return sources
                    .Where(s => !_gds.Contains(s))
                    .Concat(new[] { SOURCE_GDS })
                    .ToArray();
            }
            return sources;
        }

        #endregion
    }
}