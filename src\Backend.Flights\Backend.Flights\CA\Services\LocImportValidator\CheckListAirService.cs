using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Xml.Linq;
using Backend.Flights.CA.Model.Context;
using Backend.Flights.CA.Model.LocImport;
using Backend.Flights.CA.Services.Config;
using Backend.Flights.CA.Services.Http;
using Backend.Flights.CA.UseCase.Config;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Backend.Flights.CA.Services.LocImportValidator
{
    public class CheckListAirService : ICheckListAirService
    {
        private readonly IHttpClient _httpClient;
        private readonly ILogger<CheckListAirService> _logger;
        private readonly IWatchConfig _watchConfig;

        public CheckListAirService(IHttpClient httpClient, ILogger<CheckListAirService> logger, IWatchConfig watchConfig)
        {
            _httpClient = httpClient;
            _logger = logger;
            _watchConfig = watchConfig;
        }

        public async Task<SafeResult<CheckListAirResponse, Tuple<int, string>>> Validate(string loc, string source, string packageGroup, int branch, string userToken, BrandContext brandContext)
        {
            _logger.LogInformation("[IMPORTALOC][CheckListAirService]: Iniciando validação do CheckListAir para loc={Loc}, source={Source}, packageGroup={PackageGroup}, branch={Branch}", loc, source, packageGroup, branch);

            // Obtém a configuração do endpoint "checklist"
            var endpointConfig = (await _watchConfig.GetConfig<ImportaLocConfig>(IConfigService.IMPORTALOC_CONFIG)).GetEndpoint("checklist");

            var request = new CheckListAirRequest
            {
                QueryParameters = new Dictionary<string, string>
                {
                    { "localizador", HttpUtility.UrlEncode(loc) },
                    { "source", HttpUtility.UrlEncode(source) },
                    { "packageGroup", HttpUtility.UrlEncode(packageGroup) },
                    { "codigoFilial", branch.ToString() },
                    { "gatewayOrigin", endpointConfig.FixedQueryParams["gatewayOrigin"] },
                    { "codigoEmpresa", endpointConfig.FixedQueryParams["codigoEmpresa"] },
                    { "importacao", endpointConfig.FixedQueryParams["importacao"] }
                }
            };
            var headers = UserHeaderGenerator.GenerateGatewaySoapHeaders(brandContext.User, userToken);

            return await GetCheckListAirAsync(request, headers, endpointConfig);
        }

        public async Task<SafeResult<CheckListAirResponse, Tuple<int, string>>> GetCheckListAirAsync(CheckListAirRequest request, Dictionary<string, string> headers, EndpointConfig endpointConfig)
        {
            try
            {
                // 1. Constrói o envelope SOAP com os parâmetros recebidos
                string soapEnvelope = BuildSoapEnvelope(request);

                // 2. Recupera o endpoint SOAP a partir da configuração (FullUrl já monta URL base + path)
                string soapEndpoint = endpointConfig.FullUrl;

                if (string.IsNullOrWhiteSpace(soapEndpoint))
                {
                    return SafeResult<CheckListAirResponse, Tuple<int, string>>.Fail(new Tuple<int, string>(500, "SOAP endpoint não configurado"));
                }

                // 3. Garante que os headers existam e define o Content-Type correto
                headers ??= new Dictionary<string, string>();
                headers["content-type"] = "text/xml";

                // 4. Envia a requisição SOAP utilizando o método PostString do HttpClient
                var (responseString, errorResult, httpResponse) = await _httpClient.PostString(soapEndpoint, soapEnvelope, headers);

                if (errorResult != null)
                {
                    return SafeResult<CheckListAirResponse, Tuple<int, string>>.Fail(new Tuple<int, string>(errorResult.HttpErrorCode, errorResult.Message));
                }

                // 5. Faz o parsing da resposta para extrair o elemento <reserva> e desserializar para CheckListAirResponse
                CheckListAirResponse result = ParseSoapResponse(responseString);

                return SafeResult<CheckListAirResponse, Tuple<int, string>>.Success(result);
            }
            catch (Exception ex)
            {
                return SafeResult<CheckListAirResponse, Tuple<int, string>>.Fail(new Tuple<int, string>(500, ex.Message));
            }
        }

        /// <summary>
        /// Constrói o envelope SOAP conforme o padrão esperado:
        /// - Cabeçalho fixo com namespaces
        /// - Corpo com cada parâmetro do request como elemento XML
        /// - Footer para fechar o XML
        /// </summary>
        /// <param name="request">Objeto com os parâmetros da requisição.</param>
        /// <returns>Envelope SOAP completo como string.</returns>
        private static string BuildSoapEnvelope(CheckListAirRequest request)
        {
            string headerSoap = @"<soapenv:Envelope xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"" xmlns:cvc=""http://www.cvc.com.br/CVCWSCheckListAereo/"">
    <soapenv:Header/>
    <soapenv:Body>
      <cvc:ValidarPreEmissaoRQ>
";
            StringBuilder bodySoap = new StringBuilder();
            if (request.QueryParameters != null)
            {
                foreach (var param in request.QueryParameters)
                {
                    // Escapa o valor para evitar problemas com caracteres especiais
                    string escapedValue = System.Security.SecurityElement.Escape(param.Value);
                    bodySoap.AppendLine($"<{param.Key}>{escapedValue}</{param.Key}>");
                }
            }
            string footerSoap = @"      </cvc:ValidarPreEmissaoRQ>
    </soapenv:Body>
</soapenv:Envelope>";
            return headerSoap + bodySoap.ToString() + footerSoap;
        }

        /// <summary>
        /// Faz o parsing do XML de resposta, converte o elemento <reserva> para JSON e desserializa para CheckListAirResponse.
        /// </summary>
        /// <param name="soapResponse">A string com a resposta SOAP completa.</param>
        /// <returns>Objeto CheckListAirResponse com os dados desserializados.</returns>
        private CheckListAirResponse ParseSoapResponse(string soapResponse)
        {
            try
            {
                // Faz o parsing do XML
                XDocument doc = XDocument.Parse(soapResponse);

                // Define os namespaces utilizados no SOAP
                XNamespace soapenv = "http://schemas.xmlsoap.org/soap/envelope/";
                XNamespace cvc = "http://www.cvc.com.br/CVCWSCheckListAereo/";

                // Navega pelo XML para encontrar o elemento <reserva>
                XElement reservaElement = doc.Root?
                    .Element(soapenv + "Body")?
                    .Element(cvc + "ValidarPreEmissaoRS")?
                    .Element("reserva");

                if (reservaElement == null)
                {
                    throw new Exception("[IMPORTALOC][CheckListAirService]: Elemento 'reserva' não encontrado.");
                }

                // Converte o XElement para JSON (omitindo o objeto raiz)
                string json = JsonConvert.SerializeXNode(reservaElement, Formatting.None, omitRootObject: true);

                // Desserializa o JSON para o objeto CheckListAirResponse
                var result = JsonConvert.DeserializeObject<CheckListAirResponse>(json);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[IMPORTALOC][CheckListAirService]: Erro ao fazer parsing e deserialização da resposta SOAP.");
                throw;
            }
        }
    }
}
