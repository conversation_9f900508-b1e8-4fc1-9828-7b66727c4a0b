using Backend.Flights.CA.UseCase.Config;
using Microsoft.Extensions.Hosting;
using System.Threading;
using System.Threading.Tasks;

namespace Backend.Flights.CA.Services.Config
{
    public class WatchConfigHostedService : BackgroundService
    {
        private readonly IWatchConfig _config;

        public WatchConfigHostedService(IWatchConfig config)
        {
            _config = config;
        }

        protected override Task ExecuteAsync(CancellationToken stoppingToken)
        {
            return _config.WatchChange(5 * 60 * 1000, stoppingToken);
        }
    }
}