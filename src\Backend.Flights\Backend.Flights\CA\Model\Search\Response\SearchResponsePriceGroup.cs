﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;

namespace Backend.Flights.CA.Model.Search.Response
{
    public class SearchResponsePriceGroup
    {
        [JsonProperty("uid")]
        public string UID { get; }
        [JsonProperty("s")]
        public ResponseSegment[] Segments { get; }
        [JsonProperty("di")] 
        public ResponseDebugInformation DebugInformation { get; }
        [JsonProperty("t")] 
        public decimal Total { get; }
        [JsonProperty("i")] 
        public int Installments { get; }
        [JsonProperty("tx")]
        public decimal Tax { get; }
        [JsonProperty("f")] 
        public decimal Fee { get; }
        [JsonProperty("d")] 
        public decimal DisplayFare { get; }
        [JsonProperty("fr")] 
        public decimal Fare { get; }
        [JsonProperty("cf")] 
        public decimal ChildFare { get; }
        [JsonProperty("if")] 
        public decimal InfantFare { get; }
        [JsonProperty("pd")] 
        public ResponsePriceDetailByPassengerType PriceDetails { get; }
        [JsonProperty("hl")] 
        public string Highlight { get; }

        public SearchResponsePriceGroup(string uid, ResponseSegment[] segments, ResponseDebugInformation debugInformation, decimal total, int installments, decimal tax, decimal fee, decimal displayFare, decimal fare, decimal childFare, decimal infantFare, ResponsePriceDetailByPassengerType priceDetails, string highlight)
        {
            UID = uid;
            Segments = segments;
            DebugInformation = debugInformation;
            Total = total;
            Installments = installments;
            Tax = tax;
            Fee = fee;
            DisplayFare = displayFare;
            Fare = fare;
            ChildFare = childFare;
            InfantFare = infantFare;
            PriceDetails = priceDetails;
            Highlight = highlight;
        }

        public class ResponseBaggageInfo
        {
            [JsonProperty("h")] 
            public int HandBaggageQuantity { get; }
            [JsonProperty("c")] 
            public int CheckedBaggageQuantity { get; }
            [JsonProperty("hw")] 
            public int HandBaggageWeight { get; }
            [JsonProperty("cw")] 
            public int CheckedBaggageWeight { get; }

            public ResponseBaggageInfo(int handBaggageQuantity, int checkedBaggageQuantity, int handBaggageWeight, int checkedBaggageWeight)
            {
                HandBaggageQuantity = handBaggageQuantity;
                CheckedBaggageQuantity = checkedBaggageQuantity;
                HandBaggageWeight = handBaggageWeight;
                CheckedBaggageWeight = checkedBaggageWeight;
            }
        }

        public class ResponseStop
        {
            [JsonProperty("a")] 
            public string Airport { get; }
            [JsonProperty("dd")] 
            public DateTime DepartureDate { get; }
            [JsonProperty("ad")] 
            public DateTime ArrivalDate { get; }

            public ResponseStop(string airport, DateTime departureDate, DateTime arrivalDate)
            {
                Airport = airport;
                DepartureDate = departureDate;
                ArrivalDate = arrivalDate;
            }
        }

        public class ResponseFlight
        {
            [JsonProperty("aa")] 
            public string ArrivalAirport { get; }
            [JsonProperty("aad")] 
            public string ArrivalAirportDescription { get; }
            [JsonProperty("da")] 
            public string DepartureAirport { get; }
            [JsonProperty("dad")] 
            public string DepartureAirportDescription { get; }
            [JsonProperty("ad")] 
            public DateTime ArrivalDateTime { get; }
            [JsonProperty("dd")] 
            public DateTime DepartureDateTime { get; }
            [JsonProperty("c")] 
            public string CiaCode { get; }
            [JsonProperty("cn")] 
            public string CiaName { get; }
            [JsonProperty("cbn")] 
            public string Cabin { get; }
            [JsonProperty("b")] 
            public ResponseBaggageInfo BaggageInfo { get; }
            [JsonProperty("s")] 
            public ResponseStop[] Stops { get; }
            [JsonProperty("ns")] 
            public int NumberOfStops { get; }
            [JsonProperty("fn")] 
            public string FlightNumber { get; }
            [JsonProperty("md")] 
            public int MinutesDuration { get; }
            [JsonProperty("sl")] 
            public string SeatsLeft { get; }

            public ResponseFlight(string arrivalAirport, string arrivalAirportDescription, string departureAirport, string departureAirportDescription, DateTime arrivalDateTime, DateTime departureDateTime, string ciaCode, string ciaName, string cabin, ResponseBaggageInfo baggageInfo, ResponseStop[] stops, int numberOfStops, string flightNumber, int minutesDuration, string seatsLeft)
            {
                ArrivalAirport = arrivalAirport;
                ArrivalAirportDescription = arrivalAirportDescription;
                DepartureAirport = departureAirport;
                DepartureAirportDescription = departureAirportDescription;
                ArrivalDateTime = arrivalDateTime;
                DepartureDateTime = departureDateTime;
                CiaCode = ciaCode;
                CiaName = ciaName;
                Cabin = cabin;
                BaggageInfo = baggageInfo;
                Stops = stops;
                NumberOfStops = numberOfStops;
                FlightNumber = flightNumber;
                MinutesDuration = minutesDuration;
                SeatsLeft = seatsLeft;
            }
        }

        public class ResponseLeg
        {
            [JsonProperty("c")] 
            public string FareClass { get; }

            public ResponseLeg(string fareClass)
            {
                FareClass = fareClass;
            }
        }

        public class ResponseSegment
        {
            [JsonProperty("fl")] 
            public ResponseFlight[] Flights { get; }
            [JsonProperty("si")] 
            public int SegmentIndex { get; }
            [JsonProperty("o")] 
            public string Origin { get; }
            [JsonProperty("d")] 
            public string Destination { get; }
            [JsonProperty("c")] 
            public string IssuerCia { get; }
            [JsonProperty("cn")] 
            public string IssuerCiaName { get; }
            [JsonProperty("tmd")] 
            public int TotalMinuteDuration { get; }
            [JsonProperty("op")] 
            public string OriginProvider { get; }
            [JsonProperty("ns")] 
            public int NumberOfStops { get; }
            [JsonProperty("r")] 
            public bool Refundable { get; }
            [JsonProperty("t")] 
            public string FareType { get; }
            [JsonProperty("l")] 
            public ResponseLeg[] Legs { get; }
            [JsonProperty("i")]
            public string OperationalId { get; set; }

            public ResponseSegment(ResponseFlight[] flights, int segmentIndex, string origin, string destination, string issuerCia, string issuerCiaName, int totalMinuteDuration, string originProvider, int numberOfStops, bool refundable, string fareType, ResponseLeg[] legs, string operationalId)
            {
                Flights = flights;
                SegmentIndex = segmentIndex;
                Origin = origin;
                Destination = destination;
                IssuerCia = issuerCia;
                IssuerCiaName = issuerCiaName;
                TotalMinuteDuration = totalMinuteDuration;
                OriginProvider = originProvider;
                NumberOfStops = numberOfStops;
                Refundable = refundable;
                FareType = fareType;
                Legs = legs;
                OperationalId = operationalId;
            }
        }

        public class AddAggrement 
        {
            [JsonProperty("ac")]
            public string AccountCode {get; set;}
            [JsonProperty("ptc")]
            public string PTC {get; set;}
        }

        public class TaxRav
        {
            [JsonProperty("cd")]
            public string Code { get; set; }
            [JsonProperty("dc")]
            public string Description { get; set; }
            [JsonProperty("v")]
            public List<TaxRavValues> Values { get; set; }
            [JsonProperty("dt")]
            public List<TaxRavDetail> Details {get; set;}
        }

         public class TaxRavValues
        {
            [JsonProperty("c")]
            public string Currency { get; set; }
            [JsonProperty("a")]
            public decimal Amount { get; set; }
        }

        public class TaxRavDetail
        {
            [JsonProperty("cd")]
            public string Code { get; set; }
            [JsonProperty("dc")]
            public string Description { get; set; }
            [JsonProperty("a")]
            public decimal Amount { get; set; }
        }

        public class ResponseDebugInformation
        {
            [JsonProperty("m")] 
            public decimal Markup { get; }
            [JsonProperty("fb")] 
            public string FareBasis { get; }
            [JsonProperty("ft")] 
            public string FareType { get; }
            [JsonProperty("pi")] 
            public string PricingId { get; }
            [JsonProperty("pg")] 
            public string PackageGroup { get; }
            [JsonProperty("aa")]
            public AddAggrement AddAggrement { get; set; }
            [JsonProperty("oi")]
            public string OfficeId { get; set; }
            [JsonProperty("nv")]
            public decimal NetValue { get; set; }
            [JsonProperty("rav")]
            public TaxRav Rav { get; set; }

            public ResponseDebugInformation(decimal markup, string fareBasis, string fareType, string pricingId, string packageGroup, AddAggrement addAggrement, string officeId, decimal netValue, TaxRav taxRav)
            {
                AddAggrement = addAggrement;
                Rav = taxRav;
                OfficeId = officeId;
                NetValue = netValue;
                Markup = markup;
                FareBasis = fareBasis;
                FareType = fareType;
                PricingId = pricingId;
                PackageGroup = packageGroup;
            }
        }

        public class ResponsePriceDetail
        {
            [JsonProperty("c")] 
            public int Count { get; }
            [JsonProperty("f")] 
            public decimal Fee { get; }
            [JsonProperty("t")] 
            public decimal Tax { get; }

            public ResponsePriceDetail(int count, decimal fee, decimal tax)
            {
                Count = count;
                Fee = fee;
                Tax = tax;
            }
        }

        public class ResponsePriceDetailByPassengerType
        {
            [JsonProperty("a")] 
            public ResponsePriceDetail Adult { get; }
            [JsonProperty("c")] 
            public ResponsePriceDetail Child { get; }
            [JsonProperty("i")] 
            public ResponsePriceDetail Infant { get; }

            public ResponsePriceDetailByPassengerType(ResponsePriceDetail adult, ResponsePriceDetail child, ResponsePriceDetail infant)
            {
                Adult = adult;
                Child = child;
                Infant = infant;
            }
        }
    }
}