using System.IO;

namespace Backend.Flights.CA.Services.Protobuf
{
    public class ProtobufService : IProtobuf
    {
        public byte[] Serialize<T>(T source)
        {
            using var mms = new MemoryStream();

            ProtoBuf.Serializer.Serialize(mms, source);

            return mms.ToArray();
        }

        public T Deserialize<T>(byte[] source)
        {
            throw new System.NotImplementedException();
        }
    }
}