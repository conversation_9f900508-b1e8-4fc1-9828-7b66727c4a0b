﻿using Backend.Flights.CA.Model;
using Backend.Flights.Util;
using Microsoft.Extensions.Logging;
using Microsoft.IO;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace Backend.Flights.CA.Services.Http
{
    public class HttpClient : IHttpClient
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ILogger<HttpClient> _log;
        private readonly RecyclableMemoryStreamManager _recyclableMemoryStreamManager;
        private readonly ConfigurationManager _config;

        public HttpClient(
            IHttpClientFactory httpClientFactory,
            ILogger<HttpClient> log,
            RecyclableMemoryStreamManager recyclableMemoryStreamManager,
            ConfigurationManager config)
        {
            _httpClientFactory = httpClientFactory;
            _log = log;
            _recyclableMemoryStreamManager = recyclableMemoryStreamManager;
            _config = config;

            ServicePointManager.DefaultConnectionLimit = 30;
        }

        public static void SetHeaders(HttpRequestMessage httpRequestMessage, Dictionary<string, string> headers)
        {
            foreach (var h in headers ?? new Dictionary<string, string>())
            {
                httpRequestMessage.Headers.Add(h.Key, h.Value);
            }
        }

        private static Task<Stream> ParseStream(HttpResponseMessage response)
        {
            return response.Content.ReadAsStreamAsync();
        }

        private static Task<string> ParseString(HttpResponseMessage response)
        {
            return response.Content.ReadAsStringAsync();
        }

        private async Task<MemoryStream> ParseMemoryStream(HttpResponseMessage response)
        {
            using var resStream = await response.Content.ReadAsStreamAsync();
            var stream = _recyclableMemoryStreamManager.GetStream();
            await resStream.CopyToAsync(stream);
            stream.Position = 0;
            return stream;
        }

        private async Task<(T, HttpErrorResult, HttpResponseMessage)> MakeRequest<T>(
            HttpRequestMessage httpRequestMessage,
            Func<HttpResponseMessage, Task<T>> parseResponse,
            bool returnOnHeaderRecivied = false,
            int? timeout = null
            )
            where T : class
        {
            try
            {
                var httpClient = _httpClientFactory.CreateClient();
                httpClient.Timeout = TimeSpan.FromSeconds(timeout ?? 60);

                var ts = new Stopwatch();
                try
                {
                    await LogExternalRequests(httpRequestMessage);

                    ts.Start();
                    var res = await httpClient.SendAsync(httpRequestMessage, returnOnHeaderRecivied ? HttpCompletionOption.ResponseHeadersRead : HttpCompletionOption.ResponseContentRead);
                    ts.Stop();

                    if (res.IsSuccessStatusCode)
                    {
                        // LogWithHeaders(httpRequestMessage, "[HttpClient] - Response GTW - Http {method} to {url} done in {time}ms",
                        //     httpRequestMessage.Method.Method,
                        //     httpRequestMessage.RequestUri?.ToString(),
                        //     ts.ElapsedMilliseconds);

                        return (await parseResponse(res), null, res);
                    }

                    // var data = await (httpRequestMessage.Content?.ReadAsStringAsync() ?? Task.FromResult(string.Empty));
                    // // [log-disabled]  _log.LogInformation("Fail on http {method} to {url} with headers {headers} and data {data}",
                    //     httpRequestMessage.Method.Method,
                    //     httpRequestMessage.RequestUri?.ToString(),
                    //     httpRequestMessage.Headers.ToString(),
                    //     data);

                    var code = (int)res.StatusCode;
                    var content = await res.Content.ReadAsStringAsync();
                    return (null, new HttpErrorResult(
                        code,
                        res.StatusCode.ToString(),
                        content
                        ),
                        res
                    );

                }
                catch (HttpRequestException ex)
                {
                    Console.WriteLine("[MakeRequest] - HttpRequestException Error " + ex.Message);

                    var curlCommand = await httpRequestMessage.ToCurlCommand(true);
                    var data = await (httpRequestMessage.Content?.ReadAsStringAsync() ?? Task.FromResult(string.Empty));
                    _log.LogInformation("Fail on http {method} to {url} with headers {headers} and data {data} {exception} {curlCommand}",
                        httpRequestMessage.Method.Method,
                        httpRequestMessage.RequestUri?.ToString(),
                        httpRequestMessage.Headers.ToString(),
                        data,
                        ex.Message,
                        curlCommand);

                    return (null, new HttpErrorResult(
                         500,
                         ex.Message,
                         data
                     ), null);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("[MakeRequest] - Exception Error " + ex.Message);

                var curlCommand = await httpRequestMessage.ToCurlCommand(true);
                string logMessage;

                var (searchId, _) = ExtractHeaders(httpRequestMessage);

                if (ex is TaskCanceledException)
                    logMessage = $"Timeout on http: {httpRequestMessage.Method.Method} error: {ex.Message}";
                else
                    logMessage = $"Fail on http: {httpRequestMessage.Method.Method} error: {ex.Message}";

                using (_log.BeginScope(new Dictionary<string, object>
                {
                    ["searchId"] = searchId,
                    ["curlCommand"] = curlCommand
                }))
                    _log.LogInformation(logMessage);

                if (ex is TaskCanceledException)
                    throw new TimeoutException($"Timeout: {ex.Message}", ex);
                else
                    throw ex;
            }

        }

        private (string searchId, string transactionId) ExtractHeaders(HttpRequestMessage httpRequestMessage)
        {
            try
            {
                var searchId = httpRequestMessage.Headers.Contains("searchId") ? httpRequestMessage.Headers.GetValues("searchId").FirstOrDefault() : "";
                var transactionId = httpRequestMessage.Headers.Contains("transactionId") ? httpRequestMessage.Headers.GetValues("transactionId").FirstOrDefault() : "";
                return (searchId, transactionId);
            }
            catch (Exception ex)
            {
                _log.LogWarning("[HttpClient] - Error extracting headers: {exception}", ex.Message);
                return ("", "");
            }
        }

        private void LogWithHeaders(HttpRequestMessage httpRequestMessage, string message, params object[] args)
        {
            try
            {
                var (searchId, transactionId) = ExtractHeaders(httpRequestMessage);

                using (_log.BeginScope(new Dictionary<string, object>
                {
                    ["searchId"] = searchId,
                    ["transactionId"] = transactionId
                }))
                {
                    _log.LogInformation(message, args);
                }
            }
            catch (Exception ex)
            {
                // Log silencioso para não travar o processo principal
            }
        }

        private async Task LogExternalRequests(HttpRequestMessage httpRequestMessage)
        {
            try
            {
                var (searchId, transactionId) = ExtractHeaders(httpRequestMessage);
                var hasConfig = _config.AppSetting.TryGetValue("LogRequestInfo", out string logEnabled) && logEnabled.ToLower() == "true";

                using (_log.BeginScope(new Dictionary<string, object>
                {
                    ["searchId"] = searchId,
                    ["transactionId"] = transactionId,
                    ["metadata"] = await httpRequestMessage.ToCurlCommand(hasConfig)
                }))
                {
                    _log.LogInformation("[HttpClient] - Requesting {method} to {url}",
                        httpRequestMessage.Method.Method,
                        httpRequestMessage.RequestUri?.ToString());
                }
            }
            catch (Exception ex)
            {
                _log.LogInformation("[HttpClient] - Error while logging request info: {exception}", ex);
            }
        }

        public Task<(string, HttpErrorResult, HttpResponseMessage)> GetString(string url, Dictionary<string, string> headers = null, int? timeout = null, bool returnOnHeaderRecivied = false)
        {
            var req = new HttpRequestMessage(HttpMethod.Get, url);
            SetHeaders(req, headers);
            return MakeRequest(req, ParseString, returnOnHeaderRecivied, timeout);
        }

        public Task<(string, HttpErrorResult, HttpResponseMessage)> PostString(string url, string data, Dictionary<string, string> headers = null, int? timeout = null)
        {
            var req = new HttpRequestMessage(HttpMethod.Post, url);
            var contentType = headers.GetValueOrDefault("content-type", "text/plain");
            headers.Remove("content-type");
            req.Content = new StringContent(data, Encoding.UTF8, contentType);
            SetHeaders(req, headers);
            return MakeRequest(req, ParseString, timeout: timeout);
        }

        public Task<(MemoryStream, HttpErrorResult, HttpResponseMessage)> GetMemoryStream(string url, Dictionary<string, string> headers = null, int? timeout = null)
        {
            var req = new HttpRequestMessage(HttpMethod.Get, url);
            SetHeaders(req, headers);
            return MakeRequest(req, ParseMemoryStream, timeout: timeout);
        }

        public Task<(MemoryStream, HttpErrorResult, HttpResponseMessage)> PostMemoryStream(string url, string data, Dictionary<string, string> headers = null, int? timeout = null)
        {
            var req = new HttpRequestMessage(HttpMethod.Post, url);
            var contentType = headers.GetValueOrDefault("content-type", "text/plain");
            headers.Remove("content-type");
            req.Content = new StringContent(data, Encoding.UTF8, contentType);
            SetHeaders(req, headers);
            return MakeRequest(req, ParseMemoryStream, timeout: timeout);
        }

        public Task<(Stream, HttpErrorResult, HttpResponseMessage)> GetResponseStream(string url, Dictionary<string, string> headers = null, int? timeout = null)
        {
            var req = new HttpRequestMessage(HttpMethod.Get, url);
            SetHeaders(req, headers);
            return MakeRequest(req, ParseStream, true, timeout);
        }

        public Task<(Stream, HttpErrorResult, HttpResponseMessage)> PostResponseStream(string url, string data, Dictionary<string, string> headers = null, int? timeout = null)
        {
            var req = new HttpRequestMessage(HttpMethod.Post, url);
            var contentType = headers.GetValueOrDefault("content-type", "text/plain");
            headers.Remove("content-type");
            req.Content = new StringContent(data, Encoding.UTF8, contentType);
            SetHeaders(req, headers);
            return MakeRequest(req, ParseStream, true, timeout);
        }
    }
}