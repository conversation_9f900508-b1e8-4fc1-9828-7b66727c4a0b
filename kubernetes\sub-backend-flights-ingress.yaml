apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: sub-backend-flights-ingress
  namespace: sub-flights
  annotations:
    nginx.org/ssl-backends: "sub-backend-flights-service"
spec:
  ingressClassName: "nginx-private"
  rules:
    - host: sub-backend-flights.__SERVICE_ENV__-cvc.com.br
      http:
        paths:
          - path: /
            pathType: ImplementationSpecific
            backend:
              service:
                name: sub-backend-flights-service
                port:
                  number: 80
    - host: www.submarinoviagens.com.br
      http:
        paths:
          - path: /api/locations/
            pathType: Prefix
            backend:
              service:
                name: sub-backend-flights-service
                port:
                  number: 80
