﻿using Backend.Flights.CA.Model.LocImport;
using Backend.Flights.CA.Services.LocImportValidator;
using System.Linq;
using System.Threading.Tasks;
using Backend.Flights.CA.Services.Config;
using Backend.Flights.CA.UseCase.Config;
using System;
using Backend.Flights.CA.Model;

namespace Backend.Flights.CA.UseCase.Loc
{
    public class ValidateLocForImport
    {
        private readonly ILocImportValidator _validator;
        private readonly ILocImportPaymentValidator _paymentValidator;
        private readonly IWatchConfig _watchConfig;
        private readonly ICheckListAirService _checkListAirService;
        private readonly IPaymentCredipaxService _paymentCredipaxService;

        public ValidateLocForImport(ILocImportValidator validator, IWatchConfig watchConfig, ILocImportPaymentValidator paymentValidator, ICheckListAirService checkListAirService, IPaymentCredipaxService paymentCredipaxService)
        {
            _validator = validator;
            _watchConfig = watchConfig;
            _paymentValidator = paymentValidator;
            _checkListAirService = checkListAirService;
            _paymentCredipaxService = paymentCredipaxService;
        }

        public async Task<LocImportErrorResult> Execute(string loc, string source, string packageGroup, Model.Context.BrandContext brandContext)
        {
            var branch = brandContext.BranchId!.Value;
            var userToken = brandContext.UserToken;

            if (packageGroup.ToUpper() == "VHI" || packageGroup.ToUpper() == "VHIPLUS")
            {
                return LocImportErrorResult.Error($"Não é permitido importar tarifa {packageGroup.ToUpper()}.");
            }

            if ((await _watchConfig.GetConfig(IConfigService.IMPORTALOC_SYSTUR_VALIDATE)) != "true")
            {
                return null;
            }

            var newImportaLoc = await _watchConfig.GetConfig<ImportaLocConfig>(IConfigService.IMPORTALOC_CONFIG);
            newImportaLoc ??= new ImportaLocConfig();

            SafeResult<CheckListAirResponse, Tuple<int, string>> validation = null;

            if (newImportaLoc.UseNewService)
            {
                Console.WriteLine("[IMPORTALOC]: Using new service");

                // Se estiver usando o novo serviço, o resultado já vem mapeado corretamente
                validation = await _checkListAirService.Validate(loc, source, packageGroup, branch, userToken, brandContext);
            }
            else
            {
                Console.WriteLine("[IMPORTALOC]: Using old service (lambda)");

                // Para o serviço antigo, converte o resultado
                var oldValidation = await _validator.Validate(loc, source, packageGroup, branch, userToken);

                if (oldValidation != null && oldValidation.Result != null)
                {
                    if (oldValidation.IsError)
                    {
                        validation = SafeResult<CheckListAirResponse, Tuple<int, string>>.Fail(oldValidation.Error);
                    }
                    else
                    {
                        var convertedResult = CheckListAirResponseConverter(oldValidation.Result);
                        validation = SafeResult<CheckListAirResponse, Tuple<int, string>>.Success(convertedResult);
                    }
                }
            }

            if (validation == null || validation.Result == null)
            {
                if (validation != null && validation.IsError)
                {
                    return LocImportErrorResult.HttpError(validation.Error.Item1, "[IMPORTALOC][PaymentCredipax]: " + validation.Error.Item2);
                }

                return LocImportErrorResult.Error("[IMPORTALOC][PaymentCredipax]: Erro ao validar loc - [Validate] = Service returned 200 with null result.");
            }

            if (validation.IsError)
            {
                return LocImportErrorResult.HttpError(validation.Error.Item1, validation.Error.Item2);
            }

            var validationResult = validation.Result;

            if ((validationResult.Mec as string)?.ToUpper() == "CIA")
            {
                return LocImportErrorResult.Error("Não é permitido importar merchan CIA.");
            }

            if (!string.IsNullOrEmpty(validationResult.ErrorText))
            {
                return LocImportErrorResult.Error(validationResult.ErrorText);
            }

            if (validationResult.CheckList.Values.Any(v => !v))
            {
                var error = "O loc não passou na validação de todos os requisitos, não aprovados: " +
                    string.Join(", ", validationResult.CheckList.Where(i => !i.Value).Select(i => i.Key)) +
                    ".";
                return LocImportErrorResult.Error(error);
            }

            SafeResult<ImportLocPaymentValidationResponse, HttpErrorResult> paymentValidation = null;

            if (newImportaLoc.UseNewService)
            {
                paymentValidation = await _paymentCredipaxService.Validate(loc, branch, userToken);
            }
            else
            {
                paymentValidation = await _paymentValidator.Validate(loc, branch, userToken);
            }

            if (paymentValidation.IsError)
            {
                return LocImportErrorResult.HttpError(paymentValidation.Error.HttpErrorCode, "[IMPORTALOC][PaymentCredipax]: " + paymentValidation.Error.Message);
            }

            if (!string.Equals(paymentValidation.Result?.Status ?? string.Empty, "NOK"))
            {
                return LocImportErrorResult.Error("Reserva já existente no systur");
            }

            if (paymentValidation.Result?.Orders?.Length > 0 && paymentValidation.Result.Message.Contains("encontrada para o localizador informado"))
            {
                return LocImportErrorResult.Error(paymentValidation.Result.Message);
            }

            return null;
        }

        private static CheckListAirResponse CheckListAirResponseConverter(ImportLocValidationResponse oldResponse)
        {
            return new CheckListAirResponse
            {
                CheckList = oldResponse?.CheckList,
                Erro = (oldResponse?.Erro as string) != null ? oldResponse?.Erro.ToString() : oldResponse?.ErrorText,
                Mec = oldResponse?.Mec?.ToString()
            };
        }
    }
}
