using Backend.Flights.Business.Parsers;
using Backend.Flights.CA.Model.Opportunities;
using Backend.Flights.CA.Services.Http;
using Backend.Flights.CA.Services.Json;
using Backend.Flights.CA.UseCase.Promotions;
using Backend.Flights.Models;
using Backend.Flights.Models.GtwAereo;
using Backend.Flights.Models.Partners;
using Backend.Flights.Models.Search;
using Backend.Flights.Models.Search.Exclusive;
using Backend.Flights.Services;
using Backend.Flights.Util;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace Backend.Flights.Business
{
    /// <summary>
    /// Version 1: Propriedades com nomes extensos, envio cumulativo de payload por source
    /// Version 2: Propriedades com nomes reduzidos, envio simples de payload por source
    /// Version 3: Propriedades com nomes reduzidos, envio simples de payload por source, cache de rate token e upsell, cálculo de matriz e filtros no client
    /// </summary>
    public enum ReturnCfg { Version1, Version2, Version3 }
    public class Availability
    {
        private readonly string[] sourceList;
        private readonly ConfigurationManager _config;
        private readonly BrandContext brandCtx;
        private readonly ILogger _logger;
        private readonly ReturnCfg returnCfg;
        private readonly ICacheService cache;
        private readonly bool _enableContentProvider;
        private readonly IHttpClient _httpClient;
        private readonly IJsonService _json;


        public Availability(
            ILogger logger,
            ConfigurationManager config,
            ICacheService cache,
            BrandContext brandCtx,
            ReturnCfg returnCfg,
            bool enableContentProvider,
            IHttpClient httpClient,
            IJsonService json
            )
        {
            _config = config;
            this.brandCtx = brandCtx;
            _logger = logger;
            sourceList = _config.AppSetting["Gateway.Sources"].Split(",");
            this.returnCfg = returnCfg;
            this.cache = cache;
            _enableContentProvider = enableContentProvider;
            _httpClient = httpClient;
            _json = json;
        }

        public async ValueTask<int> SearchSync(SearchRequest request, StreamWriter sw)
        {
            var gtw = new GtwAereo(brandCtx, _config, _logger, _enableContentProvider, _httpClient, _json);
            var srcs = (gtw.UseContentProvider ? ContentProviderService.Instance.filterSources(sourceList) : sourceList);
            var tasks = srcs.Select(source => gtw.Flights(request, source));
            return await searchSync(srcs, tasks, sw);
        }

        public async ValueTask<int> MarkdownSearchSync(MarkdownSearchRequest request, StreamWriter sw)
        {
            var task = new GtwAereo(brandCtx, _config, _logger, _enableContentProvider, _httpClient, _json).Flights(request, request.Provider, new Dictionary<string, string> { { "Gtw-Booking-Token", request.BookingToken } });
            return await searchSync(new[] { request.Provider }, new[] { task }, sw);
        }

        private async ValueTask<int> searchSync(string[] providers, IEnumerable<Task<string>> tasks, StreamWriter sw)
        {
            var results = await Task.WhenAll(tasks);

            var allResults = new List<FlightsResponse>();
            FlightsResponse mergedResult = null;
            for (var i = 0; i < providers.Length; i++)
            {
                var response = JsonConvert.DeserializeObject<FlightsResponse>(results[i]);
                if (response?.Flights?.HasCount() == true)
                {
                    // Change the capitalization of the air companies
                    makeCaseCorrection(response);
                    var rt = response.Flights.FirstOrDefault()?.Segments?.FirstOrDefault()?.RateToken;
                    SetProvider(response, providers[i]);

                    allResults.Add(response);
                    mergedResult = mergedResult == null ? response : mergeResults(mergedResult, response);
                }
            }
            if (this.returnCfg == ReturnCfg.Version3)
                Parallel.ForEach(allResults, x => UpsellAndCheckoutData.CacheFlights(_logger, cache, x.Flights));

            var searchResponse = parseToSearchResponse(mergedResult, this.returnCfg == ReturnCfg.Version3 ? null : mergedResult, sourceList.Length, sourceList.Length, _logger);

            var jsonSerializerContractResolver = new AvailabilityContractResolver(this.returnCfg == ReturnCfg.Version1, this.returnCfg == ReturnCfg.Version3);
            await sendData(sw, serializeData(searchResponse, jsonSerializerContractResolver));

            return mergedResult?.Flights.Count ?? 0;
        }


        public async ValueTask<(int totalResults, string searchCacheKey)> SearchAsync(SearchRequest request, StreamWriter sw)
        {
            var gtw = new GtwAereo(brandCtx, _config, _logger, _enableContentProvider, _httpClient, _json);

            var srcs = gtw.UseContentProvider ? ContentProviderService.Instance.filterSources(sourceList) : sourceList;

            srcs = FilterSources(request, srcs);

            var procResults = new ProcessingResults(sw, returnCfg, _logger, cache, srcs.Length, _config);

            // Cria as tarefas para cada source da lista (Consul e SearchRequest)
            var tasks = srcs.Select(async source => procResults.Process(source, await gtw.Flights(request, source)));

            var totals = await Task.WhenAll(tasks);

            if (returnCfg == ReturnCfg.Version1 || returnCfg == ReturnCfg.Version2) //Na versão 3, o push fare será chamado pelo client.
            {
                var mergedResult = procResults.GetSearchFullResponse();
                var data = mergedResult == null || mergedResult.PriceGroups.Count == 0 ? (null, 0, null) : (mergedResult.PriceGroups[0].Segments[0].IssuerCia, mergedResult.PriceGroups[0].DisplayFare, mergedResult.PriceMatrix);
                await CallPushFare(_logger, _config, request, data.IssuerCia, data.DisplayFare, data.PriceMatrix, brandCtx);
            }

            // Salva os voos mais baratos por provider no cache (apenas se habilitado)
            await procResults.SaveResultsToCache();

            var searchCacheKey = procResults.GetSearchCacheKey();
            return (totals.Sum(x => x), searchCacheKey);
        }

        private static string[] FilterSources(SearchRequest request, string[] srcs)
        {
            Console.WriteLine("[AsyncStream][SearchAsync] AirCompanies " + (request?.AirCompanies != null ? string.Join(", ", request.AirCompanies) : "None"));
            Console.WriteLine("[AsyncStream][SearchAsync] Sources: " + string.Join(", ", srcs));

            // Armazena a lista original de `srcs`
            var originalSrcs = srcs;

            // Aplica o filtro de `Sources` do `request`, se foi informado
            if (request.Sources?.Any(s => !string.IsNullOrWhiteSpace(s)) == true)
            {
                srcs = srcs.Intersect(request.Sources).ToArray();
                Console.WriteLine("[AsyncStream][SearchAsync] Sources (filtered): " + (srcs.Any() ? string.Join(", ", srcs) : "Nenhum source correspondente encontrado"));

                // Se `srcs` estiver vazio após o filtro, restaura a lista original
                if (!srcs.Any())
                {
                    Console.WriteLine("[AsyncStream][SearchAsync] Nenhum source correspondente encontrado. Retornando a lista original.");
                    srcs = originalSrcs;
                }
            }

            return srcs;
        }

        public async Task<List<SearchExclusiveResponse>> SearchExclusive(
            IRequestPromotions promotion,
            CA.Model.Context.BrandContext brandContext,
            SearchRequest request)
        {
            var gtw = new GtwAereo(brandCtx, _config, _logger, _enableContentProvider, _httpClient, _json);

            List<(DateValues Start, DateValues End)> dateValues = BuildDates(request);
            var tasks = dateValues.Select(async dates =>
            {
                try
                {
                    var (start, end) = dates;
                    request.CityPairs.First().DepartureDate = start;
                    request.CityPairs.Last().DepartureDate = end;
                    return await gtw.Flights(request, "FRT");
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Erro ao buscar voos exclusivos para as datas: {ex.Message}");
                    return null;
                }
            });

            var results = await Task.WhenAll(tasks);
            var exclusiveResponse = BuildExclusiveResponse(results);
            exclusiveResponse = ApplyTowerFilter(exclusiveResponse);
            exclusiveResponse = await ApplyPromotion(promotion, brandContext, exclusiveResponse);

            return exclusiveResponse;

        }

        private List<SearchExclusiveResponse> BuildExclusiveResponse(string[] results)
        {
            var flightResponse = new List<SearchExclusiveResponse>();
            foreach (var result in results)
            {
                try
                {
                    var response = JsonConvert.DeserializeObject<SearchExclusiveResponse>(result);
                    if (response.Flights.HasCount())
                    {
                        flightResponse.Add(response);
                    }

                }
                catch (Exception ex)
                {
                    _logger.LogError($"Falha ao converter o payload do gateway: {ex.Message}");
                }

            }
            return flightResponse;
        }
        private List<(DateValues Start, DateValues End)> BuildDates(SearchRequest request)
        {
            var startDate = request.CityPairs.First().DepartureDate.ToDateTime();
            var endDate = request.CityPairs.Last().DepartureDate.ToDateTime();
            var configExclusiveDays = _config.AppSetting["SearchFlights.Exclusive.Days"];
            var numberOfDays = int.TryParse(configExclusiveDays, out var parsedDays) ? parsedDays : 7;
            var dateValues = new List<(DateValues Start, DateValues End)>();

            for (int i = -numberOfDays; i <= numberOfDays; i++)
            {
                // GTW só permite pesquisas maior que a data corrente.
                if (startDate.AddDays(i) >= DateTime.Today && startDate.AddDays(i) != request.CityPairs.First().DepartureDate.ToDateTime())
                {

                    var currentStartDate = startDate.AddDays(i);
                    var currentEndDate = endDate.AddDays(i);
                    var start = new DateValues
                    {
                        Day = currentStartDate.Day,
                        Month = currentStartDate.Month,
                        Year = currentStartDate.Year
                    };

                    var end = new DateValues
                    {
                        Day = currentEndDate.Day,
                        Month = currentEndDate.Month,
                        Year = currentEndDate.Year
                    };

                    dateValues.Add((start, end));
                }
            }

            // Ordenar as datas
            return dateValues.OrderBy(d => d.Start.Year)
                       .ThenBy(d => d.Start.Month)
                       .ThenBy(d => d.Start.Day)
                       .ToList();
        }

        private List<SearchExclusiveResponse> ApplyTowerFilter(List<SearchExclusiveResponse> responses)
        {
            var towerConfig = _config.AppSetting["SearchFlights.Exclusive.Tower"];

            // Se towerConfig for vazia, retorna responses sem modificação
            if (string.IsNullOrEmpty(towerConfig))
                return responses;

            var tower = towerConfig.Trim().Split(',');

            // Se houver exatamente dois elementos, (NAC, INTER) retorna a lista original sem filtrar
            if (tower.Length == 2)
                return responses;

            // Verificar se "INTER" está presente em tower
            bool isNational = !tower.Contains("INTER");

            // Aplicar o filtro com base na condição isNational
            List<SearchExclusiveResponse> filteredResponses = responses
                .Where(res => res.Flights.Any(flight =>
                    flight.Segments.Any(segment => segment.National == isNational)))
                .ToList();

            return filteredResponses;
        }
        private async Task<List<SearchExclusiveResponse>> ApplyPromotion(IRequestPromotions promotion, CA.Model.Context.BrandContext brandContext, List<SearchExclusiveResponse> responses)
        {

            var result = new List<SearchExclusiveResponse>();

            foreach (var response in responses)
            {

                if (response.Flights != null && response.Flights.Any())
                {
                    var flight = response.Flights.First();
                    var opportunitiesRequestItems = new[] { new OpportunitiesRequestItem(0, flight.Segments.First().RateToken, null) };

                    var promotionsResponse = await promotion.Execute(brandContext, brandContext.UserToken, opportunitiesRequestItems, null);

                    if (promotionsResponse?.AvailableItems != null || promotionsResponse.AvailableItems.Any())
                    {
                        var priceWithPromo = promotionsResponse.AvailableItems.First().Promotion.PriceWithTax;
                        flight.FareGroup.IsPromotional = priceWithPromo > 0;
                        flight.FareGroup.PromoPriceWithTax = priceWithPromo;
                    }
                    response.Flights[0] = flight;
                    result.Add(response);
                }
            }

            return result;
        }

        public async Task<string> GoogleFlightsSearch(GoogleFlightsRequest request)
        {
            var gtwResponse = await new GtwAereo(brandCtx, _config, _logger, _enableContentProvider, _httpClient, _json).GoogleFlights(request, _config.AppSetting["Gateway.Sources"].ToString());
            var retorno = GetGoogleFlightSelectedFlight(request.Price, gtwResponse, request);

            return retorno;
        }

        public string GetGoogleFlightSelectedFlight(decimal price, string response, GoogleFlightsRequest request)
        {
            try
            {
                var obj = JsonConvert.DeserializeObject<FlightsResponse>(response);
                var selectedPriceRange = obj.Flights.FirstOrDefault(x => x.FareGroup.PriceWithTax == price);

                if (selectedPriceRange == null)
                    selectedPriceRange = selectPriceWithMargin(price, obj);

                string tokens = string.Empty;
                if (selectedPriceRange != null)
                {
                    for (int leg = 0; leg < selectedPriceRange.Segments.Max(x => x.RouteRPH) + 1; leg++)
                    {
                        if (string.IsNullOrEmpty(tokens))
                        {
                            tokens = selectedPriceRange.Segments.FirstOrDefault(x => x.RouteRPH == leg && x.Legs.Any(y => y.FlightNumber == request.CityPairs[leg].FlightNumber)).RateToken;
                        }
                        else
                        {
                            tokens = string.Concat(tokens, ",", selectedPriceRange.Segments.FirstOrDefault(x => x.RouteRPH == leg).RateToken);
                        }
                    }


                    // [log-disabled] LogGoogleFlights(request,
                    //                  "success",
                    //                  "Preço encontrado com sucesso!",
                    //                  "Sucesso"
                    //                  );
                }
                else
                {
                    LogGoogleFlights(request,
                                    "error",
                                    "Voo informado por Google não foi encontrado em nosso gtw aereo!",
                                    string.Concat("Melhor preço encontrando na pesquisa: ", obj.Flights.FirstOrDefault().FareGroup.PriceWithTax.ToString()));

                }
                return tokens;
            }
            catch (Exception ex)
            {
                LogGoogleFlights(request,
                                "error",
                                "Excpetion erro ao buscar voo informado por Google",
                                ex.Message);
                throw ex;
            }
        }

        public async Task GetPartnerFlights(PartnerSearchRequest request, HttpResponse originResponse)
        {
            var bus = new Partner(_logger, brandCtx, _httpClient, _json);

            await bus.GetPartnerFlights(request, originResponse);
        }

        private static Models.GtwAereo.Flight selectPriceWithMargin(decimal price, FlightsResponse obj)
        {
            //margem de 2% de erro no valor 
            var requestedMarginValue = (price * 2) / 100;
            var minPriceWithMargin = price - requestedMarginValue;
            var maxPriceWithMargin = price + requestedMarginValue;

            return obj.Flights.FirstOrDefault(x => x.FareGroup.PriceWithTax >= minPriceWithMargin && x.FareGroup.PriceWithTax <= maxPriceWithMargin);
        }

        private void LogGoogleFlights(GoogleFlightsRequest request, string shortMessage, string message, string informationMessage)
        {
            _logger.BeginScope(new Dictionary<string, object>
            {
                ["short_message"] = shortMessage,
                ["operation"] = "GOOGLEFLIGHTS",
                ["message"] = message,
                ["requested_price"] = request.Price,
                ["requested_airCompany"] = request.AirCompany,
                ["origin"] = request.CityPairs[0].OriginIata,
                ["destination"] = request.CityPairs[0].DestinationIata
            });
            _logger.LogInformation(informationMessage);
        }

        public enum SearchType { OneWay, RoundTrip, Multidestination }
        public static SearchType GetSearchType(List<CityPair> iataPairs, bool isCvc)
        {
            static string F(string iata) => GtwAereo.getParentIata(iata);

            if (iataPairs.Count < 2) return SearchType.OneWay;
            if (iataPairs.Count > 2) return SearchType.Multidestination;
            if (isCvc) return SearchType.RoundTrip; //AWFUL BUG WITH PARENT IATAS 
            return F(iataPairs[0].OriginIata) != F(iataPairs[1].DestinationIata) || F(iataPairs[1].OriginIata) != F(iataPairs[0].DestinationIata) ? SearchType.Multidestination : SearchType.RoundTrip;
        }

        async static public Task CallPushFare(ILogger _logger, ConfigurationManager _config, SearchRequest request, string cia, decimal bestPrice, Models.Search.PriceMatrix matrix, BrandContext brandCtx)
        {
            //O Push Fare só deve ser chamado para Ida e Volta.
            if (GetSearchType(request.CityPairs, brandCtx.isCvc()) != SearchType.RoundTrip)
                return;

            var sw = new Stopwatch();
            sw.Start();

            // Call the push fare microservice to notify that a search was performed successfully
            try
            {
                if (matrix == null)
                    logMsg(false, "No data", string.Empty);
                else
                {
                    var pushFareUrl = _config.AppSetting["PushFare.Backend.Url"];
                    var pushfare = new Push(pushFareUrl, "api/v1/enqueue");
                    pushfare.SetRequestData(request);
                    pushfare.SetResponseData(cia, bestPrice, matrix, request.CityPairs[0].OriginIata, request.CityPairs[0].DestinationIata);

                    var result = await pushfare.AsyncPush();
                    // [log-disabled] logMsg(result.StatusCode == 200, $"{result.StatusCode} - {result.Response}", result.Payload);
                }
            }
            catch (Exception ex)
            {
                logMsg(false, "Unexpected error", ex.Message + "\n" + ex.StackTrace);
            }

            void logMsg(bool sucess, string shortMessage, string longMessage)
            {
                sw.Stop();
                if (_logger == null) return;

                using (_logger.BeginScope(new Dictionary<string, object>
                {
                    ["duration"] = sw.ElapsedMilliseconds,
                    ["short_message"] = shortMessage,
                    ["operation"] = "PUSHFARE",
                    ["sucess"] = sucess ? 1 : 0,
                    ["origin"] = request.CityPairs[0].OriginIata,
                    ["destination"] = request.CityPairs[0].DestinationIata
                }))
                    _logger.LogInformation(longMessage);
            }
        }

        private class ProcessingResults
        {
            private readonly StreamWriter sw;
            private readonly ReturnCfg returnCfg;
            private readonly AvailabilityContractResolver jsonSerializerContractResolver;
            private readonly ICacheService cache;
            private readonly ILogger logger;
            private readonly int expectedResults;
            private readonly string searchCacheKey;
            private readonly Dictionary<string, FlightsResponse> cachedResults;
            private readonly object cacheLock = new object();
            private readonly bool cacheEnabled;

            public ProcessingResults(StreamWriter sw, ReturnCfg returnCfg, ILogger logger, ICacheService cache, int expectedResults, ConfigurationManager config)
            {
                this.sw = sw;
                this.returnCfg = returnCfg;
                this.cache = cache;
                this.logger = logger;
                this.expectedResults = expectedResults;
                this.searchCacheKey = $"search_results_{Guid.NewGuid()}";
                this.cachedResults = new Dictionary<string, FlightsResponse>();
                jsonSerializerContractResolver = new AvailabilityContractResolver(this.returnCfg == ReturnCfg.Version1, this.returnCfg == ReturnCfg.Version3);

                // Verifica se o cache está habilitado via configuração do Consul
                // Se a chave não existir ou for diferente de "true", considera desabilitado
                this.cacheEnabled = config.AppSetting.TryGetValue("Matrix.Sales.Cache.Enabled", out string cacheEnabledValue) && cacheEnabledValue.ToLower() == "true";
            }

            private readonly object processResultLock = new object();
            private readonly object sendResultLock = new object();

            private int resultsProcessed = 0;
            private FlightsResponse mergedResult = null;
            public Models.GtwAereo.SearchResponse GetSearchFullResponse() => mergedResult == null ? null : parseToSearchResponse(mergedResult, mergedResult, 100, expectedResults, logger, GetSearchCacheKey());

            public string GetSearchCacheKey() => cacheEnabled ? searchCacheKey : null;

            /// <summary>
            /// This is a callback function that is called from each thread in the GtwAereo().Flights() call. After
            /// a thread has completed its call to the backend server to query the flights list, this callback will be
            /// called to process the result
            /// The method has a mutually exclusive code region to avoid a race condition where multiple results arrive
            /// in succession and try to update the same instance of the object, which considering how the merge
            /// method was built could cause one or more results to be lost (if the race condition happened)
            /// </summary>
            /// <param name="provider"></param>
            /// <param name="response"></param>
            public int Process(string provider, string response)
            {
                ReadOnlyMemory<char> dataToSend;
                int totalResults = 0;
                lock (processResultLock)
                {
                    object searchResponse = null;
                    resultsProcessed++;

                    void setErrorResponse(string errMsg, int processedSources, int expectedSources)
                        => searchResponse = new { PriceGroups = new object[0], Error = errMsg, CompletedWork = 100 * processedSources / expectedSources };
                    try
                    {
                        var result = JsonConvert.DeserializeObject<FlightsResponse>(response);
                        if (result.Error != null)
                            setErrorResponse($"Gateway: {result.Error.Code} - {result.Error.CorrelationId} - {result.Error.Message}", resultsProcessed, expectedResults);
                        else if (result.Flights.Count > 0 || resultsProcessed == expectedResults)
                        {
                            if (result.Flights.Count > 0)
                            {
                                totalResults += result.Flights.Count;
                                makeCaseCorrection(result);
                                SetProvider(result, provider);

                                // Salva o resultado no cache para consulta posterior (apenas se habilitado)
                                if (cacheEnabled)
                                {
                                    lock (cacheLock)
                                    {
                                        cachedResults[provider] = result;
                                    }
                                }

                                if (this.returnCfg == ReturnCfg.Version3)
                                {
                                    // Salva o cache do upsell no avail (asyncstream)
                                    UpsellAndCheckoutData.CacheFlights(logger, cache, result.Flights);
                                }

                                //Merged result will be kept to generate price's matrix and, if necessary (if cummulativeReturn is expected), the merged result will be sent to client.
                                if (this.returnCfg != ReturnCfg.Version3)
                                    mergedResult = mergedResult == null ? result : mergeResults(mergedResult, result);
                            }
                            searchResponse = parseToSearchResponse(this.returnCfg == ReturnCfg.Version1 ? mergedResult : result, this.returnCfg == ReturnCfg.Version3 ? null : mergedResult, resultsProcessed, expectedResults, logger, GetSearchCacheKey());
                        }
                    }
                    catch (Exception ex)
                    {
                        // Pega apenas 100 caracteres do payload para evitar problemas de tamanho
                        var payloadSnippet = response?.Length > 100 ? response.Substring(0, 100) : response;

                        logger?.LogError(ex, ex.GetErrorLocation($"Erro ao processar resultado do provider {provider}: Gtw Response: {payloadSnippet}"));
                        setErrorResponse("GTW Response: " + payloadSnippet + " - " + ex.Message, resultsProcessed, expectedResults);
                    }
                    finally
                    {
                        dataToSend = serializeData(searchResponse, jsonSerializerContractResolver);
                    }
                }

                lock (sendResultLock)
                {
                    Task.WaitAll(sendData(sw, dataToSend));
                }
                return totalResults;
            }

            /// <summary>
            /// Salva o response do gateway no cache (metrics)
            /// </summary>
            public async Task SaveResultsToCache()
            {
                // Verifica se o cache está habilitado via configuração
                if (!cacheEnabled) return;

                if (cachedResults.Count == 0) return;

                // Filtra os voos mais baratos por provider e por número de paradas
                var filteredResults = new Dictionary<string, Models.GtwAereo.FlightsResponse>();

                // COMENTADO: Lógica de filtro por paradas - mantido para uso futuro se necessário
                /*
                foreach (var kvp in cachedResults)
                {
                    var provider = kvp.Key;
                    var flightsResponse = kvp.Value;

                    // Agrupa voos por categoria de paradas (3 grupos: Direto, 1 Parada, 2+ Paradas)
                    var flightsByCategory = flightsResponse.Flights
                        .Where(f => f.FareGroup?.PriceWithTax > 0) // Filtra voos com preço válido
                        .GroupBy(f => GetCategoryByStops(GetNumberOfStops(f)))
                        .ToDictionary(g => g.Key, g => g.OrderBy(f => f.FareGroup.PriceWithTax).ToList());

                    var topFlightsByCategory = new List<Models.GtwAereo.Flight>();

                    // Pega os voos mais baratos de cada categoria
                    foreach (var category in flightsByCategory)
                    {
                        var categoryName = GetCategoryName(category.Key);
                        var topFlightsInCategory = category.Value.Take(GetTopCountByCategory(category.Key)).ToList();

                        if (topFlightsInCategory.Any())
                        {
                            topFlightsByCategory.AddRange(topFlightsInCategory);
                            //logger?.LogInformation($"Provider {provider} - {categoryName}: {category.Value.Count} voos -> {topFlightsInCategory.Count} voos mais baratos (preço: {topFlightsInCategory.First().FareGroup.PriceWithTax:C} - {topFlightsInCategory.Last().FareGroup.PriceWithTax:C})");
                        }
                    }

                    if (topFlightsByCategory.Any())
                    {
                        var filteredResponse = new Models.GtwAereo.FlightsResponse
                        {
                            Flights = topFlightsByCategory,
                            Meta = flightsResponse.Meta,
                            Error = flightsResponse.Error
                        };

                        filteredResults[provider] = filteredResponse;

                        logger?.LogInformation($"Provider {provider}: {flightsResponse.Flights.Count} voos -> {topFlightsByCategory.Count} voos filtrados por categoria");
                    }
                }
                */

                // Salva todos os resultados sem filtro por paradas
                var providersWithResults = new List<string>();

                foreach (var kvp in cachedResults)
                {
                    filteredResults[kvp.Key] = kvp.Value;

                    // Verifica se o provider retornou resultados não vazios
                    if (kvp.Value.Flights != null && kvp.Value.Flights.Count > 0)
                    {
                        providersWithResults.Add(kvp.Key);
                        logger?.LogInformation($"[SaveResultsToCache]: Provider {kvp.Key}: {kvp.Value.Flights.Count} voos salvos (com resultados)");
                    }
                    else
                    {
                        logger?.LogInformation($"[SaveResultsToCache]: Provider {kvp.Key}: 0 voos salvos (sem resultados)");
                    }
                }

                var cacheData = new
                {
                    SearchCacheKey = searchCacheKey,
                    Results = cachedResults,
                    ProcessedAt = DateTime.UtcNow,
                    TotalProviders = expectedResults,
                    ProcessedProviders = resultsProcessed,
                    ProvidersWithResults = providersWithResults,
                    Note = "[SaveResultsToCache]: Todos os voos salvos sem filtro (compressão GZIP aplicada)"
                };

                var cacheValue = JsonConvert.SerializeObject(cacheData);

                // Comprime o JSON antes de salvar no cache
                var compressedValue = CompressionUtil.CompressString(cacheValue);
                var originalSize = cacheValue.Length;
                var compressedSize = compressedValue.Length;
                var compressionRatio = CompressionUtil.CalculateCompressionRatio(originalSize, compressedSize);

                logger?.LogInformation($"[SaveResultsToCache]: Cache compression: {originalSize:N0} bytes -> {compressedSize:N0} bytes ({compressionRatio:F1}%)");

                var cacheDataDict = new Dictionary<string, string>
                {
                    { searchCacheKey, compressedValue }
                };

                try
                {
                    await cache.BatchAdd(cacheDataDict, 20 * 60, logger); // 20 minutos
                    logger?.LogInformation($"[SaveResultsToCache]: Cache salvo com sucesso: {filteredResults.Count} providers, {filteredResults.Values.Sum(f => f.Flights.Count)} voos no total. Providers com resultados: {string.Join(", ", providersWithResults)}");
                }
                catch (Exception ex)
                {
                    logger?.LogError(ex, "[SaveResultsToCache]: Erro ao salvar cache: {0}", ex.Message);
                    // Não falha o AsyncStream se o cache falhar
                }
            }

            // COMENTADO: Lógica de filtro por paradas - mantido para uso futuro se necessário
            // /// <summary>
            // /// Determina o número de paradas de um voo
            // /// </summary>
            // private static int GetNumberOfStops(Models.GtwAereo.Flight flight)
            // {
            //     if (flight?.Segments == null || !flight.Segments.Any())
            //         return 0;

            //     // Conta o número total de paradas em todos os segmentos
            //     var totalStops = flight.Segments.Sum(segment => segment.NumberOfStops);
            //     return totalStops;
            // }

            // /// <summary>
            // /// Retorna a categoria baseada no número de paradas (3 grupos: Direto, 1 Parada, 2+ Paradas)
            // /// </summary>
            // private static int GetCategoryByStops(int numberOfStops)
            // {
            //     return numberOfStops switch
            //     {
            //         0 => 0, // Direto
            //         1 => 1,  // 1 Parada
            //         _ => 2   // 2 ou mais paradas
            //     };
            // }

            // /// <summary>
            // /// Retorna o nome da categoria baseado no número de paradas
            // /// </summary>
            // private static string GetCategoryName(int category)
            // {
            //     return category switch
            //     {
            //         0 => "Direto",
            //         1 => "1 Parada",
            //         2 => "2+ Paradas",
            //         _ => "Desconhecido"
            //     };
            // }

            // /// <summary>
            // /// Retorna quantos voos pegar de cada categoria
            // /// </summary>
            // private static int GetTopCountByCategory(int category)
            // {
            //     return category switch
            //     {
            //         0 => 50, // Direto: 20 voos mais baratos
            //         1 => 50,  // 1 parada: 15 voos mais baratos
            //         2 => 50,  // 2+ paradas: 10 voos mais baratos
            //         _ => 5    // Fallback
            //     };
            // }
            // COMENTADO: Lógica de filtro por paradas - mantido para uso futuro se necessário
        }

        private static ReadOnlyMemory<char> serializeData(object searchResponse, IContractResolver jsonSerializerContractResolver)
        {
            if (searchResponse == null) return new ReadOnlyMemory<char>();
            var settings = new JsonSerializerSettings { Formatting = Formatting.None, ContractResolver = jsonSerializerContractResolver };
            return JsonConvert.SerializeObject(searchResponse, settings).AsMemory();
        }
        async private static Task sendData(StreamWriter sw, ReadOnlyMemory<char> serializedData)
        {
            if (serializedData.Length == 0) return;
            try
            {
                await sw.WriteLineAsync(serializedData);
                await sw.FlushAsync();
            }
            catch (Exception e)
            {
                // Log any exception so we can treat it later
                Debug.WriteLine("EE Exception: " + e.Message);
            }
        }

        private static void SetProvider(FlightsResponse response, string providerFallback)
        {
            foreach (var flight in response?.Flights)
            {
                flight.Segments?.ForEach(s =>
                {
                    var rt = flight?.Segments?.FirstOrDefault()?.RateToken;
                    var provider = providerFallback;
                    if (!string.IsNullOrWhiteSpace(rt))
                    {
                        var rtDoc = SearchParser.ParseRateToken(rt);
                        provider = rtDoc.Attribute("cmi")?.Value ?? provider;
                        if (!rtDoc.Attributes().Any(a => a.Name == "cmi"))
                        {
                            Debugger.Break();
                        }
                    }
                    s.Provider = provider;
                });
            }
        }

        private static void makeCaseCorrection(FlightsResponse response)
        {
            if (response.Flights.Count == 0)
                return;

            foreach (var flight in response.Flights)
            {
                flight.ValidatingBy.Name = flight.ValidatingBy.Name.ToTitleCase();
                foreach (var segment in flight.Segments)
                    foreach (var leg in segment.Legs)
                    {
                        leg.ManagedBy.Name = leg.ManagedBy.Name.ToTitleCase();
                        leg.OperatedBy.Name = leg.OperatedBy.Name.ToTitleCase();
                    }
            }

            if (response.Meta.PriceMatrix.Columns != null)
                foreach (var column in response.Meta.PriceMatrix.Columns)
                    foreach (var airCompany in column.AirCompanies)
                        airCompany.Name = airCompany.Name.ToTitleCase();
        }

        private static FlightsResponse mergeResults(FlightsResponse flight1, FlightsResponse flight2)
        {
            var flight = new FlightsResponse
            {
                // Merge the fare groups into the result object
                Flights = flight1.Flights.Concat(flight2.Flights).OrderBy(o => o.FareGroup.PriceWithTax).ToList(),
                // From this line on, all the logic concerns to calculate the new Meta object from the data available
                // from the previous meta objects. This is faster (from an algorithm complexity point of view) than 
                // querying all the time the flights to get these informations.
                Meta = new Meta()
                {
                    // The CountFlights and CountPriceGroups are just the plain sum of their input objects
                    CountFlights = flight1.Meta.CountFlights + flight2.Meta.CountFlights,
                    CountPriceGroups = flight1.Meta.CountPriceGroups + flight2.Meta.CountPriceGroups,
                    Price = new Price()
                    {
                        Currency = flight1.Meta.Price.Currency,
                        MinWithoutTax = Math.Min(flight1.Meta.Price.MinWithoutTax, flight2.Meta.Price.MinWithoutTax),
                        MaxWithoutTax = Math.Max(flight1.Meta.Price.MaxWithoutTax, flight2.Meta.Price.MaxWithoutTax),
                        MinWithTax = Math.Min(flight1.Meta.Price.MinWithTax, flight2.Meta.Price.MinWithTax),
                        MaxWithTax = Math.Max(flight1.Meta.Price.MaxWithTax, flight2.Meta.Price.MaxWithTax)
                    },
                    Airports = new List<Airports>(flight1.Meta.Airports),
                    // The initial value for the price matrix is the information from the first object
                    PriceMatrix = new Models.GtwAereo.PriceMatrix()
                    {
                        Currency = flight1.Meta.PriceMatrix.Currency,
                        Columns = flight1.Meta.PriceMatrix.Columns
                    },
                    Routes = new List<Routes>()
                }
            };

            foreach (var airport in flight2.Meta.Airports)
                if (!flight.Meta.Airports.Exists(p => p.Iata == airport.Iata))
                    flight.Meta.Airports.Add(airport);

            foreach (var element in flight2.Meta.PriceMatrix.Columns)
                foreach (var company in element.AirCompanies)
                {
                    var originalAirlineInformation = flight.Meta.PriceMatrix.Columns.Where(p => p.AirCompanies.Exists(q => q.Iata == company.Iata)).FirstOrDefault();

                    // Check if the airline company IATA code already exists in the response array
                    if (originalAirlineInformation == null)
                        flight.Meta.PriceMatrix.Columns.Add(element);
                    else
                        foreach (var row in element.Rows) // Check each row in the newer reference for the airline to verify if it is present in the original array
                        {
                            var originalPriceRow = originalAirlineInformation.Rows.Where(p => p.NumberOfStops == row.NumberOfStops).FirstOrDefault();

                            // Perform the check by verifying if the number of stops exist in the rows property
                            if (originalPriceRow == null)
                                originalAirlineInformation.Rows.Add(row);
                            else
                            {
                                // The price for the number of stops already exists. Check if the new price is smaller than the previously recorded one
                                originalPriceRow.Price = Math.Min(originalPriceRow.Price, row.Price);
                                originalPriceRow.PriceWithoutTax = Math.Min(originalPriceRow.PriceWithoutTax, row.PriceWithoutTax);
                            }
                        }
                }

            // Set all the flags of BestPrice to false so that we can later set only those which need to true
            flight.Meta.PriceMatrix.Columns.ForEach(p => p.Rows.ForEach(q => q.BestPrice = false));

            var listNumberOfStops = flight.Meta.PriceMatrix.Columns.SelectMany(p => p.Rows.Select(q => q.NumberOfStops)).Distinct().ToList();
            foreach (int numberOfStops in listNumberOfStops)
            {
                var lowestPrice = flight.Meta.PriceMatrix.Columns.SelectMany(p => p.Rows.Where(q => q.NumberOfStops == numberOfStops)).OrderBy(o => o.Price).FirstOrDefault();
                lowestPrice.BestPrice = true;
            }

            for (int i = 0; i < flight1.Meta.Routes.Count; i++)
            {
                var route1 = flight1.Meta.Routes[i];
                var route2 = flight2.Meta.Routes[i];

                var route = new Routes
                {
                    Airports = new List<Airports>(route1.Airports),
                    FlightDuration = new FlightDuration()
                    {
                        Minimum = Math.Min(route1.FlightDuration.Minimum, route2.FlightDuration.Minimum),
                        Maximum = Math.Max(route1.FlightDuration.Maximum, route2.FlightDuration.Maximum)
                    }
                };

                foreach (var airport in route2.Airports)
                    if (!route.Airports.Exists(p => p.Iata == airport.Iata))
                        route.Airports.Add(airport);

                flight.Meta.Routes.Add(route);
            }

            return flight;
        }

        private static Models.GtwAereo.SearchResponse parseToSearchResponse(FlightsResponse flightsResponse, FlightsResponse mergedResponse, int processedSources, int expectedSources, ILogger logger, string searchCacheKey = null)
        {
            var response = new Models.GtwAereo.SearchResponse
            {
                PriceGroups = SearchParser.ParseToPriceGroups(flightsResponse, logger, searchCacheKey),
                PriceMatrix = MatrixParser.ParseToPriceMatrix(mergedResponse ?? flightsResponse),
                FiltersData = SearchParser.ParseToFiltersData(mergedResponse ?? flightsResponse),
                CompletedWork = 100 * processedSources / expectedSources
            };
            return response;
        }


    }
}