using Backend.Flights.CA.Model;
using Backend.Flights.CA.Services.Config;
using Backend.Flights.CA.UseCase.Config;
using Backend.Flights.CA.UseCase.ContentProvider;
using System;
using System.Collections.Generic;

namespace Backend.Flights.CA.UseCase.Search
{
    public class GetSourcesList
    {
        private readonly ContentProviderFilterSources _contentProviderFilterSources;
        private readonly Random _random = new Random();

        private string[] _sources = Array.Empty<string>();

        public GetSourcesList(IWatchConfig config, ContentProviderFilterSources contentProviderFilterSources)
        {
            _contentProviderFilterSources = contentProviderFilterSources;

            config.ConfigChanged += ConfigOnConfigChanged;
            ParseSources(config.GetConfig(IConfigService.GATEWAY_SOURCES).Result);
        }

        private void ConfigOnConfigChanged(object sender, ConfigItemChangedEventArgs e)
        {
            if (e.Name == IConfigService.GATEWAY_SOURCES)
            {
                ParseSources(e.Value);
            }
        }

        private void ParseSources(string sources)
        {
            _sources = sources.Split(',');
        }

        public IEnumerable<string> Execute(Model.Context.BrandContext brandContext)
        {
            var contentProviderFx = brandContext.ContentProvider?.Use ?? 0;

            var useContentProvider =
                !string.IsNullOrWhiteSpace(brandContext.ContentProvider?.Url) &&
                contentProviderFx > 0 &&
                (
                    brandContext.ContentProvider.Use >= 1 ||
                    _random.Next() <= contentProviderFx
                );

            return useContentProvider ? _contentProviderFilterSources.Execute(_sources) : _sources;
        }
    }
}