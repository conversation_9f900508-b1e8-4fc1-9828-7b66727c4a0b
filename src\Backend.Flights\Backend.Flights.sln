﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.4.33110.190
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Backend.Flights", "Backend.Flights\Backend.Flights.csproj", "{0F5CBAA4-25E0-485F-93E8-E06E9A1692D0}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{577AD39F-94C9-4498-9A49-DA28F6E505FD}"
	ProjectSection(SolutionItems) = preProject
		..\..\pipe.yaml = ..\..\pipe.yaml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "kubernetes", "kubernetes", "{BC14E09D-17E9-4AB3-9A57-07C3003920A1}"
	ProjectSection(SolutionItems) = preProject
		..\..\kubernetes\configmaps-prod.yaml = ..\..\kubernetes\configmaps-prod.yaml
		..\..\kubernetes\configmaps-qa.yaml = ..\..\kubernetes\configmaps-qa.yaml
		..\..\kubernetes\configmaps-ti.yaml = ..\..\kubernetes\configmaps-ti.yaml
		..\..\kubernetes\namespace.yaml = ..\..\kubernetes\namespace.yaml
		..\..\kubernetes\sub-backend-flights-deploy.yaml = ..\..\kubernetes\sub-backend-flights-deploy.yaml
		..\..\kubernetes\sub-backend-flights-hpa.yaml = ..\..\kubernetes\sub-backend-flights-hpa.yaml
		..\..\kubernetes\sub-backend-flights-ingress.yaml = ..\..\kubernetes\sub-backend-flights-ingress.yaml
		..\..\kubernetes\sub-backend-flights-service.yaml = ..\..\kubernetes\sub-backend-flights-service.yaml
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{0F5CBAA4-25E0-485F-93E8-E06E9A1692D0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0F5CBAA4-25E0-485F-93E8-E06E9A1692D0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0F5CBAA4-25E0-485F-93E8-E06E9A1692D0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0F5CBAA4-25E0-485F-93E8-E06E9A1692D0}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{BC14E09D-17E9-4AB3-9A57-07C3003920A1} = {577AD39F-94C9-4498-9A49-DA28F6E505FD}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {0EDDB9EF-147B-4DF0-AD30-AC56264185EA}
	EndGlobalSection
EndGlobal
