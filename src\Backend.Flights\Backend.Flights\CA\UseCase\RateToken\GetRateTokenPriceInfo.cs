﻿using Backend.Flights.CA.Services.Json;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;

namespace Backend.Flights.CA.UseCase.RateToken
{
    public class GetRateTokenPriceInfo
    {
        private readonly IJsonService _json;
        private readonly GetRateTokenAttributes _getRateTokenAttributes;
        private readonly ILogger<GetRateTokenPriceInfo> _logger;

        public GetRateTokenPriceInfo(IJsonService json, GetRateTokenAttributes getRateTokenAttributes, ILogger<GetRateTokenPriceInfo> logger)
        {
            _json = json;
            _getRateTokenAttributes = getRateTokenAttributes;
            _logger = logger;
        }

        public (decimal costPrice, decimal finalPrice)? FromOfd(string ofd)
        {
            try
            {
                var info = _json.Deserialize<Model.RateToken.OfferToken>(ofd);

                return info?.FarePaxTokens?
                    .Where(t => t.FaixaEtaria == "ADT")
                    .Select(t => { return (costPrice: t.CostPrice, finalPrice: t.FinalPrice); })
                    .FirstOrDefault();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Rate Token Price Info Error");
                return null;
            }
        }

        public (decimal costPrice, decimal finalPrice)? FromToken(string token)
        {
            string ofd;
            try
            {
                ofd = _getRateTokenAttributes.Execute(token, "ofd")["ofd"];
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Rate Token Price Info Error on token parser");
                return null;
            }
            return FromOfd(ofd);
        }
    }
}
