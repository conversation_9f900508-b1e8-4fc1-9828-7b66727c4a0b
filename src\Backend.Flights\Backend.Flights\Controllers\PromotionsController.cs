﻿using Backend.Flights.CA.Model.Cart;
using Backend.Flights.CA.Model.Promotions;
using Backend.Flights.CA.UseCase.BrandContext;
using Backend.Flights.CA.UseCase.Promotions;
using Backend.Flights.Services;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace Backend.Flights.Controllers
{
    public class GetPromotionsBody
    {
        public CartItem[] SelectedItems { get; set; }
        public PromotionsRequestFlightItem[] FlightsIds { get; set; }
        public string GtwUserToken { get; set; }
    }

    [ApiController, ApiVersion("1.0"), Route("api/[controller]")]
    public class PromotionsController : Controller
    {
        private readonly ILogger<PromotionsController> _logger;
        private readonly RequestPromotionsFromCachedFareGroup _requestPromotions;

        public PromotionsController(
            ILogger<PromotionsController> logger,
            RequestPromotionsFromCachedFareGroup requestPromotions)
        {
            _logger = logger;
            _requestPromotions = requestPromotions;
        }

        [EnableCors("AllowAnyOrigin"), HttpPost]
        public async Task<object> GetPromotion([FromServices] IGetCurrentBrandContext getCurrentBrandContext, GetPromotionsBody request)
        {
            try
            {
                string gtwUserToken;
                if (request.FlightsIds.Length == 0 ||
                    (
                        !Request.Cookies.TryGetValue(IContextBrand.COOKIE_ACCESS_TOKEN, out gtwUserToken) &&
                        string.IsNullOrWhiteSpace(request.GtwUserToken)
                    ))
                {
                    var resultError = Json(new { error = true, message = "Invalid arguments!" });
                    resultError.StatusCode = 400;
                    return resultError;
                }

                gtwUserToken ??= request.GtwUserToken;
                var contextBrand = await getCurrentBrandContext.Execute(gtwUserToken);
                var result = await _requestPromotions.Execute(
                    contextBrand, gtwUserToken, request.FlightsIds, request.SelectedItems);

                return Json(result ?? Array.Empty<PromotionsCachedFareGroupResponse>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Fail on promotions search. Endpoint called: {Endpoint}, Request body: {@Request}", HttpContext.Request.Path, JsonConvert.SerializeObject(request));

                var resultError = Json(new
                {
                    error = true,
                    message = "Fail on promotions search",
#if DEBUG
                    detaill = ex.ToString()
#endif
                });
                resultError.StatusCode = 500;
                return resultError;
            }

        }
    }

    [ApiController, ApiVersion("2.0"), Route("api/[controller]")]
    public class PromotionsV4Controller : Controller
    {
        private readonly ILogger<PromotionsController> _logger;
        private readonly RequestPromotionsFromCachedFareGroup _requestPromotions;

        public PromotionsV4Controller(
            ILogger<PromotionsController> logger,
            RequestPromotionsFromCachedFareGroup requestPromotions)
        {
            _logger = logger;
            _requestPromotions = requestPromotions;
        }

        [EnableCors("AllowAnyOrigin"), HttpPost]
        public async Task<object> GetPromotion(
            [FromServices] IGetCurrentBrandContext getCurrentBrandContext,
            GetPromotionsBody request)
        {
            string gtwUserToken;
            if (request.FlightsIds.Length == 0 ||
                (
                    !Request.Cookies.TryGetValue(IContextBrand.COOKIE_ACCESS_TOKEN, out gtwUserToken) &&
                    string.IsNullOrWhiteSpace(request.GtwUserToken)
                ))
            {
                var resultError = Json(new { error = true, message = "Invalid arguments!" });
                resultError.StatusCode = 400;
                return resultError;
            }

            try
            {
                gtwUserToken ??= request.GtwUserToken;
                var contextBrand = await getCurrentBrandContext.Execute(gtwUserToken);
                var result = await _requestPromotions.Execute(
                    contextBrand, gtwUserToken, request.FlightsIds, request.SelectedItems);

                return Json(result ?? Array.Empty<PromotionsCachedFareGroupResponse>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Fail on promotions search. Endpoint called: {Endpoint}, Request body: {@Request}", HttpContext.Request.Path, JsonConvert.SerializeObject(request));
                var resultError = Json(new
                {
                    error = true,
                    message = "Fail on promotions search",
#if DEBUG
                    detaill = ex.ToString()
#endif
                });
                resultError.StatusCode = 500;
                return resultError;
            }

        }
    }
}