﻿using Backend.Flights.CA.Services.Config;
using Backend.Flights.CA.UseCase.Config;
using System;

namespace Backend.Flights.CA.UseCase.FeatureFlag
{
    public class GetFeatureFlag
    {
        private (string flags, DateTime lastUpdate) _featureFlag;

        public GetFeatureFlag(IWatchConfig config)
        {
            config.ConfigChanged += Config_ConfigChanged;

            ProcessConfig(config.GetConfig(IConfigService.FEATURE_FLAG).Result);
        }

        private void Config_ConfigChanged(object sender, Model.ConfigItemChangedEventArgs e)
        {
            if (e.Name == IConfigService.FEATURE_FLAG)
            {
                ProcessConfig(e.Value);
            }
        }

        private void ProcessConfig(string config)
        {
            _featureFlag = (config, DateTime.Now);
        }

        public (string flags, DateTime lastUpdate) Execute()
        {
            return _featureFlag;
        }
    }
}
