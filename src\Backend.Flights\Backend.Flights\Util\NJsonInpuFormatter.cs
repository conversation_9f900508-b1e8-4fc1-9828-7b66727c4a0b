using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Formatters;
using Microsoft.Extensions.Logging;
using Microsoft.Net.Http.Headers;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace Backend.Flights.Util
{
    public class NJsonInputFormatter : TextInputFormatter
    {
        private readonly JsonSerializerSettings _options;
        private readonly ILogger<NJsonInputFormatter> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ConfigurationManager _config;

        public NJsonInputFormatter(JsonSerializerSettings options, ILogger<NJsonInputFormatter> logger, IHttpContextAccessor httpContextAccessor, ConfigurationManager config)
        {
            _options = options;
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
            _config = config;
            SetConfig();
        }

        public NJsonInputFormatter(ILogger<NJsonInputFormatter> logger)
        {
            _logger = logger;
            SetConfig();
        }

        void SetConfig()
        {
            SupportedMediaTypes.Add(MediaTypeHeaderValue.Parse("application/json"));
            SupportedMediaTypes.Add(MediaTypeHeaderValue.Parse("text/json"));
            SupportedMediaTypes.Add(MediaTypeHeaderValue.Parse("application/*+json"));
            SupportedEncodings.Add(new UTF8Encoding(false, true));
            SupportedEncodings.Add(new UnicodeEncoding(false, true, true));
        }

        public override bool CanRead(InputFormatterContext context)
        {
            return base.CanRead(context);
        }

        // public override async Task<InputFormatterResult> ReadRequestBodyAsync(InputFormatterContext context, Encoding encoding)
        // {
        //     using (TextReader body = new StreamReader(context.HttpContext.Request.Body))
        //     {
        //         var res = JsonConvert.DeserializeObject(await body.ReadToEndAsync(), context.ModelType, _options);

        //         return await InputFormatterResult.SuccessAsync(res);
        //     }
        // }

        public override async Task<InputFormatterResult> ReadRequestBodyAsync(InputFormatterContext context, Encoding encoding)
        {
            string bodyContent = string.Empty;
            // [log-disabled] var logHelper = new LogHelper(_logger, _config);

            try
            {
                using (TextReader body = new StreamReader(context.HttpContext.Request.Body, encoding))
                {
                    bodyContent = await body.ReadToEndAsync();
                    // [log-disabled] _logger.LogInformation("Successfully read request body. Endpoint: {Endpoint}. BodyContent: {BodyContent}", context.HttpContext.Request.Path, bodyContent);

                    var res = JsonConvert.DeserializeObject(bodyContent, context.ModelType, _options);
                    // [log-disabled] _logger.LogInformation("Successfully deserialized request body to type {ModelType}.", context.ModelType.Name);

                    // [log-disabled] logHelper.LogRequestInfo(context.HttpContext, res); // O CurlLoggingMiddleware já está logando as informações da requisição

                    return await InputFormatterResult.SuccessAsync(res);
                }
            }
            catch (JsonException jsonEx)
            {
                LogError(context, jsonEx, bodyContent);
                return await InputFormatterResult.FailureAsync();
            }
            catch (Exception ex)
            {
                LogError(context, ex, bodyContent);
                return await InputFormatterResult.FailureAsync();
            }
        }

        private void LogError(InputFormatterContext context, Exception ex, string bodyContent)
        {
            var request = context.HttpContext.Request;
            var headers = JsonConvert.SerializeObject(request.Headers);
            var queryString = request.QueryString.HasValue ? request.QueryString.Value : "No query string";
            var body = bodyContent is object ? bodyContent.ToString() : bodyContent;

            var metadata = $"Endpoint: {request.Path}\nQueryString: {queryString}\nHeaders: {headers}\nRequest body: {body}";

            using (_logger.BeginScope(new Dictionary<string, object>
            {
                ["short_message"] = "error",
                ["metadata"] = metadata,
                ["exception"] = ex
            }))
            {
                _logger.LogInformation("[NJsonInputFormatter] - Error reading request body");
            }
        }
    }
}