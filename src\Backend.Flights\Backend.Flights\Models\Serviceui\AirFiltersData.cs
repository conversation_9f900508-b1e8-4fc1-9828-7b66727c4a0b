﻿using System.Collections.Generic;

namespace Backend.Flights.Models.Serviceui
{
    public class AirFiltersData
    {
        public List<FiltersAirCompanies> AirCompanies { get; set; }
        public List<FiltersAirports> AirportsInbound { get; set; }
        public List<FiltersAirports> AirportsOutbound { get; set; }
        public List<FiltersStops> NumberOfStops { get; set; }
        public decimal MinPrice { get; set; }
        public decimal MaxPrice { get; set; }
        public int MinInboundDuration { get; set; }
        public int MinOutboundDuration { get; set; }
        public int MaxInboundDuration { get; set; }
        public int MaxOutboundDuration { get; set; }
    }

    public class FiltersAirCompanies
    {
        public string CiaCode { get; set; }
        public string CiaName { get; set; }
        public decimal MinPrice { get; set; }
    }

    public class FiltersAirports
    {
        public string Description { get; set; }
        public string Iata { get; set; }
        public decimal MinPrice { get; set; }
    }

    public class FiltersStops
    {
        public decimal MinPrice { get; set; }
        public int Stops { get; set; }
    }
}
