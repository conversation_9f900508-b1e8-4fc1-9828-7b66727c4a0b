﻿using System.Collections.Generic;

namespace Backend.Flights.Models.Markdown
{
    public class FullResponseData
    {
        public CredipaxResponse.Order CredipaxOrder { get; set; }
        public ImportaLoc.Booking ImportaLocBooking { get; set; }
        public string Error { get; set; }
    }
    public class CredipaxResponse
    {
        public string Status { get; set; }
        public string Message { get; set; }
        public IEnumerable<Order> Orders { get; set; }

        public class Pax
        {
            public int id { get; set; }
        }

        public class Credit
        {
            public int credipax { get; set; }
            public double balance { get; set; }
            public double creditValue { get; set; }
            public int contractorCode { get; set; }
            public int contractorId { get; set; }
            public IEnumerable<Pax> pax { get; set; }
            public double entry { get; set; }
            public double priceWithTax { get; set; }
        }
        public class Cia
        {
            public string name { get; set; }
            public string iata { get; set; }
        }
        public class Leg
        {
            public string departure { get; set; }
            public string arrival { get; set; }
            public string departureDate { get; set; }
            public string arrivalDate { get; set; }
            public Cia operatedBy { get; set; }
            public int flightNumber { get; set; }
        }
        public class Segment
        {
            public string departure { get; set; }
            public string arrival { get; set; }
            public string departureDate { get; set; }
            public string arrivalDate { get; set; }
            public IEnumerable<Leg> legs { get; set; }
        }
        public class Flight
        {
            public IEnumerable<Segment> segments { get; set; }
        }
        public class Order
        {
            public string pnr { get; set; }
            public string provider { get; set; }
            public int order { get; set; }
            public int branch { get; set; }
            public string packageGroup { get; set; }
            public double discPromo { get; set; }
            public double discEntry { get; set; }
            public IEnumerable<Credit> credit { get; set; }
            public IEnumerable<Flight> flights { get; set; }
            public string mkp { get; set; }
            public string var { get; set; }
            public string bag { get; set; }
            public string txAd { get; set; }
            public string qtPax { get; set; }
        }
    }
}