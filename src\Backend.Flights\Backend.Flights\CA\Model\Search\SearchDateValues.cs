using Newtonsoft.Json;
using System;
using System.ComponentModel.DataAnnotations;

namespace Backend.Flights.CA.Model.Search
{
    public class SearchDateValues
    {
        [Required]
        public int Day { get; set; }

        [Required]
        public int Month { get; set; }

        [Required]
        public int Year { get; set; }

        [JsonIgnore]
        public DateTime AsDate => new DateTime(Year, Month, Day);
    }
}