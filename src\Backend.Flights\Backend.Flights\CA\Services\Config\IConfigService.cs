using System.Threading.Tasks;

namespace Backend.Flights.CA.Services.Config
{
    public interface IConfigService
    {
        public const string PROJECT_NAME = "sub-backend-flights";
        public const string OPPORTUNITIES_URL = PROJECT_NAME + "/opportunities.url";
        public const string CORP_LOGIN_URL = PROJECT_NAME + "/corpLogin.url";
        public const string MONGO_CONNECTION_DATA = PROJECT_NAME + "/mongo.connectionData";
        public const string FEATURE_FLAG = PROJECT_NAME + "/featureFlag";
        public const string LOCATION_URL = PROJECT_NAME + "/locations.url";
        public const string GATEWAY_SOURCES = PROJECT_NAME + "/gateway.sources";
        public const string ID_CONFIG = PROJECT_NAME + "/idConfig";
        public const string IMPORTALOC_VALIDATE_URL = PROJECT_NAME + "/importalocValidateUrl";
        public const string IMPORTALOC_SYSTUR_VALIDATE = PROJECT_NAME + "/importalocSysturValidator";
        public const string IMPORTALOC_SYSTUR_PAYMENT_VALIDATE = PROJECT_NAME + "/importalocSysturPaymentValidator";
        public const string IMPORTALOC_VALID_STATUS = PROJECT_NAME + "/importalocValidStatus";
        public const string BRAND_CONTEXTS = "platform/brand-contexts/flights";
        public const string IMPORTALOC_CONFIG = PROJECT_NAME + "/importaloc.config";
        Task<string> GetConfig(string configName);
    }
}