﻿using System.Collections.Generic;

namespace Backend.Flights.Models.Search
{
    public class PriceMatrix
    {
        public List<PriceMatrixAirCompany> AirCompanies { get; set; }
    }

    public class PriceMatrixAirCompany
    {
        public List<PriceMatrixCell> Cells { get; set; }
        public string AirCompany { get; set; }
        public decimal BestPriceAirCompany { get; set; }
        public string CiaCode { get; set; }
        public string OriginSource { get; set; }
        [MustSkipPropertyIfContractResolverIsFlagged]
        public string SearchKey { get; set; }
    }

    public class PriceMatrixCell
    {
        public NumberOfStops Type { get; set; }
        public decimal Price { get; set; }
        public int BestInstallments { get; set; }
        public decimal Tax { get; set; }
        public decimal Fee { get; set; }
        public decimal Fare { get; set; }
        public string OriginAirport { get; set; }
        public string DestinationAirport { get; set; }
    }

    public enum NumberOfStops
    {
        NonStop,
        OneStop,
        TwoOrMore
    }
}
