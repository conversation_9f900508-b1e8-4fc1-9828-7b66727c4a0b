using System.Text.RegularExpressions;

namespace Backend.Flights.Services
{
    public static class HandBaggageWeightService
    {
        private static readonly Regex handBaggageRegex = new Regex(@"\d+(?=\s*kg)", RegexOptions.Compiled | RegexOptions.IgnoreCase);
        public static int parse(string description)
        {
            if (!string.IsNullOrEmpty(description))
            {
                var match = handBaggageRegex.Match(description);
                if (match.Success)
                {
                    return int.Parse(match.Value);
                }
            }
            return 0;
        }
    }
}