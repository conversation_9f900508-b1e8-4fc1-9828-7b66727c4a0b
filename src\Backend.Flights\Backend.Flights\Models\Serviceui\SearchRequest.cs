﻿using System.Collections.Generic;

namespace Backend.Flights.Models.Serviceui
{
    public class SearchRequest
    {
        public SearchRequestEnvelope req { get; set; }
    }

    public class SearchRequestEnvelope
    {
        public int CompanyId { get; set; }
        public SearchRQ TravelEngineSearchRQ { get; set; }
        public List<FilterContext> Filters { get; set; }
    }

    public class SearchRQ
    {
        public string UserBrowser { get; set; }
        public string Culture { get; set; }
        public SearchData SearchData { get; set; }
    }
}
