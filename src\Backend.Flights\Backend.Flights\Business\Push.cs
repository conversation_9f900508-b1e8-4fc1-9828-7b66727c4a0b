﻿using Backend.Flights.Models.Push;
using Backend.Flights.Models.Search;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using System.Net;

namespace Backend.Flights.Business
{
    public class Push
    {
        private readonly PushRequest pushFareRequest = null;
        private readonly string fullUrl = string.Empty;

        public Push(string pushFareHost, string endpoint)
        {
            if (pushFareHost[pushFareHost.Length - 1] != '/')
                fullUrl = pushFareHost + '/' + endpoint;
            else
                fullUrl = pushFareHost + endpoint;

            pushFareRequest = new PushRequest();
        }

        public void SetRequestData(SearchRequest searchRequest)
        {
            pushFareRequest.id_parceiro = 8297;
            var city = searchRequest.CityPairs[0];
            pushFareRequest.origem = city.OriginIata;
            pushFareRequest.destino = city.DestinationIata;
            pushFareRequest.data_ida = formatDate(city.DepartureDate);
            pushFareRequest.data_volta = formatDate(searchRequest.CityPairs[1].DepartureDate);
            pushFareRequest.moeda = "BRL";
            pushFareRequest.executiva = searchRequest.Cabin == "exe";

            string formatDate(DateValues dt) => dt.Year.ToString() + dt.Month.ToString("00") + dt.Day.ToString("00");
        }

        public void SetResponseData(string ciaBestPrice, decimal displayFareBestPrice, Models.Search.PriceMatrix priceMatrix, string origin, string destination)
        {
            pushFareRequest.cia_ida = ciaBestPrice;
            pushFareRequest.cia_volta = ciaBestPrice;
            pushFareRequest.tarifa = displayFareBestPrice.ToString("F", CultureInfo.InvariantCulture);
            pushFareRequest.priceMatrix = parsePriceMatrix(priceMatrix, origin, destination);
        }

        public class PushReturn
        {
            public int StatusCode { get; set; }
            public string Payload { get; set; }
            public string Response { get; set; }
        }
        private static readonly HttpClient client = new HttpClient(new HttpClientHandler { AutomaticDecompression = DecompressionMethods.GZip});
        public async Task<PushReturn> AsyncPush()
        {
            var serPushFareReq = JsonConvert.SerializeObject(pushFareRequest);
            try
            {
                // Create the dictionary that will hold the data to be forwarded to the clients' endpoints
                var dictionary = new Dictionary<string, string>
                {
                    { "headers", JsonConvert.SerializeObject(new Dictionary<string, string> { { "Content-Type", "application/json" } }) },
                    { "body", serPushFareReq }
                };

                var req = new HttpRequestMessage(HttpMethod.Post, fullUrl)
                {
                    // To send to the push fare service, we need a 'payload' field
                    // Encode the dictionary to be send to the push fare service
                    Content = new FormUrlEncodedContent(new Dictionary<string, string> { { "payload", JsonConvert.SerializeObject(dictionary) } })
                };
                var response = await client.SendAsync(req);
                // Return the status code
                return new PushReturn { StatusCode = (int)response.StatusCode, Response = await response.Content.ReadAsStringAsync(), Payload = serPushFareReq };
            }
            catch (Exception ex)
            {
                return new PushReturn { StatusCode = -1, Response = "Error: " + ex.Message, Payload = serPushFareReq };
            }
        }

        private static List<Models.Push.PriceMatrix> parsePriceMatrix(Models.Search.PriceMatrix getStatusMatrix, string origin, string destination)
            => (from ac in getStatusMatrix.AirCompanies
                select new Models.Push.PriceMatrix
                {
                    AirCompany = ac.CiaCode,
                    SearchKey = ac.SearchKey,
                    OriginSource = ac.OriginSource,
                    BestPrice = ac.BestPriceAirCompany.ToString("F", CultureInfo.InvariantCulture),
                    NonStopPrice = buildStopPrice(ac, NumberOfStops.NonStop, origin, destination),
                    OneStopPrice = buildStopPrice(ac, NumberOfStops.OneStop, origin, destination),
                    MultiStopPrice = buildStopPrice(ac, NumberOfStops.TwoOrMore, origin, destination)
                }).ToList();

        private static StopPrice buildStopPrice(PriceMatrixAirCompany ac, NumberOfStops stop, string origin, string destination)
        {
            var priceCell = ac.Cells.FirstOrDefault(p => p.Type == stop);

            if (priceCell == null)
                return new StopPrice();
            return new StopPrice
            {
                Fare = priceCell.Fare,
                Tax = priceCell.Tax,
                Fee = priceCell.Fee,
                Total = priceCell.Price,
                Currency = "BRL",
                OriginAirport = priceCell.Price == 0 ? "" : origin,
                DestinationAirport = priceCell.Price == 0 ? "" : destination
            };
        }
    }
}