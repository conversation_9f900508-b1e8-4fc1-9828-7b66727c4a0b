using Backend.Flights.CA.Model.Cart;
using Backend.Flights.CA.Model.Opportunities;
using Backend.Flights.CA.Services.Config;
using Backend.Flights.CA.Services.Http;
using Backend.Flights.CA.Services.Json;
using Backend.Flights.CA.UseCase.Config;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Backend.Flights.CA.UseCase.Opportunities
{
    public class GetOpportunities : IGetOpportunities
    {
        private const string PROFILE_NAME = "air-avail";
        private readonly IWatchConfig _config;
        private readonly IJsonService _jsonService;
        private readonly IHttpClient _httpClient;

        public GetOpportunities(IWatchConfig config, IJsonService jsonService, IHttpClient httpClient)
        {
            _config = config;
            _jsonService = jsonService;
            _httpClient = httpClient;
        }

        public async Task<OpportunitiesResponse> Execute(OpportunitiesRequestItem[] availableItems,
                                                         CartItem[] selectedItems,
                                                         string userToken,
                                                         OpportunitiesRequest.RequestParams requestParams = null,
                                                         bool isProfitSplit = false)
        {
            if (isProfitSplit == false && availableItems == null || availableItems?.Length == 0 || string.IsNullOrWhiteSpace(userToken))
            {
                return null;
            }

            if (isProfitSplit && (selectedItems == null || selectedItems?.Length <= 0 || requestParams == null))
            {
                return null;
            }

            var header = new Dictionary<string, string>()
            {
                {"content-type", "application/json"},
                {"gtw-sec-user-token", userToken},
                {"gtw-transaction-id", Helper.NewRandomString()}
            };

            var data = _jsonService.Serialize(new OpportunitiesRequest()
            {
                Params = requestParams ?? new OpportunitiesRequest.RequestParams()
                {
                    ShowSteps = false,
                    ShowPayloads = false,
                    ProfileName = PROFILE_NAME
                },
                AvailableItems = availableItems,
                SelectedItems = selectedItems
            });

            var url = await _config.GetConfig(IConfigService.OPPORTUNITIES_URL);
            var (json, error, httpResponse) = await _httpClient.PostString(url, data, header);

            if (error != null || string.IsNullOrWhiteSpace(json))
            {
                return null;
            }

            var dRes = _jsonService.Deserialize(json);

            var res = new OpportunitiesResponse();
            var a = (IEnumerable<dynamic>)dRes?.availableItems;
            var s = (IEnumerable<dynamic>)dRes?.selectedItems;

            res.AvailableItems = a?.Select(ParseItem).ToArray();
            res.SelectedItems = s?.Select(ParseItem).ToArray();

            return res;
        }

        private static OpportunitiesResponseItem ParseItem(dynamic item)
            => new OpportunitiesResponseItem((int)item.rph, (string)item.rateToken, item, (int?)item?.routeRPH);
    }
}