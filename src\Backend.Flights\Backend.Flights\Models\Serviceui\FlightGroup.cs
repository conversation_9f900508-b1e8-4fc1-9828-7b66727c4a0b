﻿using System.Collections.Generic;

namespace Backend.Flights.Models.Serviceui
{
    public class FlightGroup
    {
        public List<Flight> Flights { get; set; }
        public FareOption FareOption { get; set; }
        public List<FareOption> FareOptions { get; set; }
        public string Origin { get; set; }
        public string Destination { get; set; }
        public string IssuerCia { get; set; }
        public string IssuerCiaName { get; set; }
        public string OriginProvider { get; set; }
        public int TotalMinuteDuration { get; set; }
        public string Hash { get; set; }
        public string SellKey { get; set; }
        public string CorrelationId { get; set; }
        public string SimpleHash { get; set; }
        public string SourceType { get; set; }
    }

    public class Flight
    {
        public string ArrivalAirport { get; set; }
        public string ArrivalAirportLong { get; set; }
        public string DepartureAirport { get; set; }
        public string DepartureAirportLong { get; set; }
        public DateTimeValues ArrivalDateTimeValues { get; set; }
        public DateTimeValues DepartureDateTimeValues { get; set; }
        public string CiaCode { get; set; }
        public string CiaName { get; set; }
        public int CabinType { get; set; }
        public bool? Bagagem { get; set; }
        public BaggageInfo BaggageInfo { get; set; }
        public int NumberOfStops { get; set; }
        public string FlightNumber { get; set; }
        public int MinutesDuration { get; set; }
        public string SellKey { get; set; }
        public string Hash { get; set; }
        public string SimpleHash { get; set; }
        public string OperatedBy { get; set; }
        public string FareBasis { get; set; }
        public string ClassOfService { get; set; }
        public string FamiliaTarifa { get; set; }
        public string OperatedByName { get; set; }
        public string Origin { get; set; }
        public string OriginCity { get; set; }
        public string OriginCountry { get; set; }
        public string OriginLong { get; set; }
        public string Destination { get; set; }
        public string DestinationCity { get; set; }
        public string DestinationCountry { get; set; }
        public string DestinationLong { get; set; }

    }

    public class FareOption
    {
        public decimal FareAmount { get; set; }
        public string PaxType { get; set; }
        public Pricing Pricing { get; set; }
    }

    public class Pricing
    {
        public decimal Markup { get; set; }
    }

    public class DateTimeValues
    {
        public int Day { get; set; }
        public int DayOfWeek { get; set; }
        public int Hour { get; set; }
        public int Millisecond { get; set; }
        public int Minutes { get; set; }
        public int Month { get; set; }
        public int Seconds { get; set; }
        public int Year { get; set; }
    }

    public class BaggageInfo
    {
        public int HandBaggageQuantity { get; set; }
        public int CheckedBaggageQuantity { get; set; }
        public int HandBaggageWeight { get; set; }
        public int CheckedBaggageWeight { get; set; }
    }
}
