﻿using Backend.Flights.Models.GtwAereo;
using Backend.Flights.Models.Search;
using Backend.Flights.Util;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Xml.Linq;
using static Backend.Flights.Business.UpsellAndCheckoutData;
using static Backend.Flights.Models.Serviceui.SearchDetailsResponseError;
using Flight = Backend.Flights.Models.Search.Flight;
using Leg = Backend.Flights.Models.Search.Leg;
using System.Text.RegularExpressions;

namespace Backend.Flights.Business.Parsers
{
    public class SearchParser
    {
        public static FiltersData ParseToFiltersData(FlightsResponse flightsResponse)
        {
            var filtersData = new FiltersData();
            if (flightsResponse == null || flightsResponse.Meta == null || flightsResponse.Meta.Price == null)
                return filtersData;

            filtersData.Price = new FiltersPrice()
            {
                MaxPrice = flightsResponse.Meta.Price.MaxWithTax,
                MinPrice = flightsResponse.Meta.Price.MinWithTax
            };

            filtersData.Duration = new List<FiltersDuration>();

            if (flightsResponse.Meta.Routes != null)
            {
                foreach (var route in flightsResponse.Meta.Routes)
                {
                    if (route.FlightDuration != null)
                    {
                        filtersData.Duration.Add(new FiltersDuration
                        {
                            MinDuration = route.FlightDuration.Minimum,
                            MaxDuration = route.FlightDuration.Maximum
                        });
                    }
                }
            }

            filtersData.AirportsInbound = new List<FiltersAirports>();
            filtersData.AirportsOutbound = new List<FiltersAirports>();

            if (flightsResponse.Meta.Routes?.Count() == 2)
            {
                List<Airports> inbound = flightsResponse.Meta.Routes[1].Airports.Where(p => p.Stop == false).ToList();
                foreach (Airports airport in inbound)
                {
                    filtersData.AirportsInbound.Add(new FiltersAirports() { Description = airport.Name, Iata = airport.Iata });
                }
            }

            // Somente ida
            if (flightsResponse.Meta.Routes?.Count() > 0 && flightsResponse.Meta.Routes?.Count() <= 2)
            {
                if (flightsResponse.Meta.Routes.Count == 1)
                {
                    List<Airports> inbound = new List<Airports>();
                    var inboudAirPorts = flightsResponse.Flights.SelectMany(x => x.Segments.Select(y => y.Arrival)).Distinct();

                    foreach (var i in inboudAirPorts)
                    {
                        var airport = flightsResponse.Meta.Airports?.FirstOrDefault(x => x.Iata == i);
                        if (airport != null)
                        {
                            filtersData.AirportsInbound.Add(new FiltersAirports() { Description = airport.Name, Iata = airport.Iata });
                        }
                    }
                }

                List<Airports> outbound = flightsResponse.Meta.Routes[0].Airports.Where(p => p.Stop == false).ToList();
                foreach (Airports airport in outbound)
                {
                    filtersData.AirportsOutbound.Add(new FiltersAirports() { Description = airport.Name, Iata = airport.Iata });
                }
            }

            filtersData.AirCompanies = ParseToAirCompanies(flightsResponse);
            filtersData.NumberOfStops = ParseToNumberOfStops(flightsResponse);
            return filtersData;
        }

        private static List<TaxRavValues> GetValuesRAV(Tax tax)
        {
            List<TaxRavValues> taxValues = new List<TaxRavValues>();
            if (tax?.Values != null)
            {
                tax.Values.ForEach(value =>
                {
                    TaxRavValues taxValue = new TaxRavValues
                    {
                        Amount = value?.Amount ?? 0,
                        Currency = value?.Currency
                    };
                    taxValues.Add(taxValue);
                });
            }
            return taxValues;
        }

        private static List<TaxRavDetail> GetTaxDetailRAV(Tax tax)
        {
            List<TaxRavDetail> taxDetails = new List<TaxRavDetail>();
            if (tax?.Details != null)
            {
                tax.Details.ForEach(detail =>
                {
                    TaxRavDetail taxDetail = new TaxRavDetail
                    {
                        Amount = detail?.Amount ?? 0,
                        Code = detail?.Code,
                        Description = detail?.Description
                    };
                    taxDetails.Add(taxDetail);
                });
            }
            return taxDetails;
        }

        private static TaxRav ConvertTaxInTaxRAV(Tax tax)
        {
            TaxRav taxRav = new TaxRav
            {
                Code = tax?.Code,
                Description = tax?.Description,
                Values = GetValuesRAV(tax),
                Details = GetTaxDetailRAV(tax)
            };
            return taxRav;
        }

        private static TaxRav GetTaxRAV(Models.GtwAereo.Flight flight)
        {
            if (flight?.FareGroup?.Fares == null || flight.FareGroup.Fares.Count == 0)
            {
                return null;
            }

            var taxRavList = flight.FareGroup.Fares
                ?.SelectMany(fare => fare?.Taxes ?? Enumerable.Empty<Tax>())
                ?.Where(taxe => taxe?.Code?.ToUpper() == "RAV")
                ?.Select(ConvertTaxInTaxRAV)
                ?.ToList();

            return taxRavList?.FirstOrDefault();
        }

        private static string ConvertStringPTC(Models.GtwAereo.Flight flight)
        {
            if (flight?.FareGroup?.Fares == null || flight.FareGroup.Fares.Count == 0)
            {
                return string.Empty;
            }

            var ptcList = flight.FareGroup.Fares
                .Where(f => !string.IsNullOrWhiteSpace(f.PassengersType))
                .Select(f => $"{f.PassengersType} - {f.PriceWithTax}");

            return string.Join(" | ", ptcList);
        }


        internal static List<Models.Search.PriceGroup> ParseToPriceGroups(FlightsResponse res, ILogger logger, string searchCacheKey = null)
        {
            if (res == null || res.Flights.Count == 0)
                return new List<Models.Search.PriceGroup>(0);

            var airPorts = res.Meta.Airports ?? res.Meta.Routes
                .SelectMany(r => r.Airports)
                .GroupBy(a => a.Iata)
                .Select(a => a.First())
                .ToList();

            var dictAirports = airPorts.ToDictionary(x => x.Iata);

            return (from flight in res.Flights
                    let adultFare = flight.FareGroup.Fares.First(fare => fare.PassengersType == "ADT")
                    let childFare = flight.FareGroup.Fares.FirstOrDefault(fare => fare.PassengersType == "CHD")
                    let infantFare = flight.FareGroup.Fares.FirstOrDefault(fare => fare.PassengersType == "INF")
                    let taxFee = calcTaxAndFee(flight.FareGroup.Fares.ToArray())
                    let firstSegment = flight.Segments.First()
                    let highlight = flight.highlight
                    select new
                    {
                        f = flight,
                        flight.UID, //this is just a unique identifier, valid to one specific search, in order to create links between pricegroups to generate upsell data.
                        Segments = ParseToSegments(flight, dictAirports, logger),
                        Baggages = ParseToReducedBaggage(flight.FareGroup.Baggages),
                        DebugInformation = new DebugInformation
                        {
                            AddAggrement = new AddAggrement
                            {
                                AccountCode = ParseAccountCode(firstSegment.RateToken),
                                PTC = ConvertStringPTC(flight)
                            },
                            OfficeId = ParseOfficeID(firstSegment.RateToken),
                            NetValue = ParseNetValue(firstSegment.RateToken),
                            Markup = ParseMarkup(firstSegment.RateToken),
                            FareBasis = firstSegment.Legs.First().FareBasis,
                            FareType = firstSegment.FareType,
                            PricingId = ParsePricingID(firstSegment.RateToken),
                            PackageGroup = firstSegment.PackageGroup,
                            Rav = GetTaxRAV(flight)
                        },
                        Fare = adultFare.PriceWithoutTax,
                        ChildFare = childFare?.PriceWithoutTax ?? 0,
                        InfantFare = infantFare?.PriceWithoutTax ?? 0,
                        PriceDetails = buildPriceDetail(adultFare, childFare, infantFare),
                        Tax = taxFee.tax * adultFare.PassengersCount,
                        Fee = taxFee.fee * adultFare.PassengersCount,
                        DisplayFare = adultFare.PriceWithTax,
                        Total = flight.FareGroup.PriceWithTax,
                        Installments = 1,
                        InstallmentOptions = BuildInstallmentOptions(flight.Installments),
                        Highlight = highlight,
                        res
                    })
                .Where(p =>
                {
                    var isEqualSegments = p.Segments.Count == p.f.Segments.Count;
                    if (!isEqualSegments)
                    {
                        logger.LogError("[ParseToPriceGroups] - Bad flight -> UID: {flightUID}, Segments count mismatch: Expected {expectedCount}, Actual {actualCount}, RateToken: {rateToken}, Source: {source}",
                            p.UID,
                            p.Segments.Count,
                            p.f.Segments.Count,
                            p.res.Flights[0].Segments[0].RateToken,
                            ParseRateToken(p.res.Flights[0].Segments[0].RateToken).Attribute("cmi").Value
                        );
                    }
                    return isEqualSegments;
                })
                .Select(p => new Models.Search.PriceGroup()
                {
                    UID = p.UID,
                    SearchCacheKey = searchCacheKey, // Inclui a chave do cache
                    Segments = p.Segments,
                    Baggages = p.Baggages,
                    DebugInformation = p.DebugInformation,
                    Fare = p.Fare,
                    ChildFare = p.ChildFare,
                    InfantFare = p.InfantFare,
                    PriceDetails = p.PriceDetails,
                    Tax = p.Tax,
                    Fee = p.Fee,
                    DisplayFare = p.DisplayFare,
                    Total = p.Total,
                    Installments = p.Installments,
                    InstallmentOptions = p.InstallmentOptions,
                    Highlight = p.Highlight,
                })
                .ToList();
        }

        private static List<Models.Search.InstallmentOption> BuildInstallmentOptions(Installments installments)
        {
            var installmentOptions = installments?.InstallmentOptions;
            if (installmentOptions == null || !installmentOptions.Any())
            {
                return new List<Models.Search.InstallmentOption>();
            }

            return installmentOptions.Select(io => new Models.Search.InstallmentOption
            {
                CardType = io.CardType,
                Options = io.Options.Select(o => new Models.Search.Option
                {
                    FirstInstallment = o.FirstInstallment,
                    Installment = o.Installment,
                    Quantity = o.Quantity,
                    HasInterest = o.HasInterest,
                    CardPlan = o.CardPlan,
                    InstallmentId = o.InstallmentId
                }).ToList()
            }).ToList();
        }

        private static readonly string[] feeCodes = new string[4] { "DU", "RAV", "CFEE", "TB" };
        private static (decimal tax, decimal fee) calcTaxAndFee(params Fare[] fares)
            => (fares.Sum(fare => fare.Taxes.HasCount() ? fare.Taxes.Where(tax => !feeCodes.Contains(tax.Code)).Sum(tax => tax.Values.Sum(v => v.Amount)) : 0),
                fares.Sum(fare => fare.Taxes.HasCount() ? fare.Taxes.Where(tax => feeCodes.Contains(tax.Code)).Sum(tax => tax.Values.Sum(v => v.Amount)) : 0));
        private static PriceDetailByPassengerType buildPriceDetail(Fare adultFare, Fare childFare, Fare infantFare)
        {
            var adult = calcTaxAndFee(adultFare);
            var child = childFare == null ? (0, 0) : calcTaxAndFee(childFare);
            var infant = infantFare == null ? (0, 0) : calcTaxAndFee(infantFare);

            return new PriceDetailByPassengerType
            {
                Adult = new PriceDetail { Count = adultFare.PassengersCount, Tax = adult.tax, Fee = adult.fee },
                Child = new PriceDetail { Count = childFare?.PassengersCount ?? 0, Tax = child.tax, Fee = child.fee },
                Infant = new PriceDetail { Count = infantFare?.PassengersCount ?? 0, Tax = infant.tax, Fee = infant.fee },
            };
        }

        public static string ParseCiaName(string ciaName)
        {
            if (ciaName.Contains("Airlines"))
                ciaName = ciaName.Replace("Airlines", "");

            return ciaName;
        }

        private static List<FiltersStops> ParseToNumberOfStops(FlightsResponse flightsResponse)
        {
            List<FiltersStops> filtersStops = new List<FiltersStops>();

            flightsResponse.Meta?.PriceMatrix?.Columns?.ForEach(column =>
            {
                column.Rows.ForEach(r =>
                {
                    if (!filtersStops.Any(s => s.Stops == r.NumberOfStops))
                    {
                        FiltersStops filters = new FiltersStops()
                        {
                            MinPrice = r.Price,
                            Stops = r.NumberOfStops
                        };
                        filtersStops.Add(filters);
                    }
                });
            });

            filtersStops = filtersStops.OrderBy(f => f.Stops).ToList();
            return filtersStops;
        }

        private static List<FiltersAirCompanies> ParseToAirCompanies(FlightsResponse flightsResponse)
        {
            List<FiltersAirCompanies> filtersAirs = new List<FiltersAirCompanies>();
            flightsResponse.Meta?.PriceMatrix?.Columns?.ForEach(colum =>
            {
                FiltersAirCompanies filtersAirCompanies = new FiltersAirCompanies()
                {
                    CiaCode = colum.AirCompanies != null ? colum.AirCompanies[0].Iata : "",
                    CiaName = colum.AirCompanies != null ? ParseCiaName(colum.AirCompanies[0].Name) : "",
                    MinPrice = colum.Rows.Min(c => c.Price)
                };
                filtersAirs.Add(filtersAirCompanies);
            });
            return filtersAirs;
        }

        private static bool isRefundable(List<Segment> segments)
        {
            return segments.All(segment =>
            {
                return segment?.FareProfile?.Services?
                    .Find(service => service.Type != null && service.Type.Equals("REFUNDABLE"))
                    ?.IsIncluded ?? false;
            });
        }

        private static List<Segments> ParseToSegments(Models.GtwAereo.Flight flight, Dictionary<string, Airports> dictAirports, ILogger logger)
        {
            var segments = new List<Segments>();

            var refundable = isRefundable(flight.Segments);

            foreach (var s in flight.Segments)
            {
                var flights = ParseToFlights(s.FareProfile, s.Legs, dictAirports, logger);
                if (flights.Count < s.Legs.Count)
                {
                    return new List<Segments>();
                }

                segments.Add(new Segments
                {
                    Flights = ParseToFlights(s.FareProfile, s.Legs, dictAirports, logger),
                    SegmentIndex = s.RouteRPH,
                    Origin = s.Departure,
                    Destination = s.Arrival,
                    IssuerCia = flight.ValidatingBy.Iata,
                    IssuerCiaName = ParseCiaName(flight.ValidatingBy.Name),
                    SellKey = s.RateToken,
                    TotalMinuteDuration = s.Duration,
                    OriginProvider = s.Provider,
                    NumberOfStops = s.NumberOfStops,
                    Refundable = refundable,
                    FareType = s.FareType,
                    Legs = s.Legs.Select((l) => new Leg()
                    {
                        FareClass = l.FareClass
                    }).ToList(),
                    OperationalId = s.OperationalId
                });
            }

            return segments;
        }

        private static decimal ParseMarkup(string token) => decimal.Parse(ParseRateToken(token).Attribute("mkp").Value, NumberFormatInfo.InvariantInfo);
        private static string ParseAccountCode(string token) => ParseRateToken(token).Attribute("rtc")?.Value;
        private static string ParsePricingID(string token) => ParseRateToken(token).Attribute("mki").Value;
        private static string ParseOfficeID(string token) => ParseRateToken(token).Attribute("oii")?.Value;
        private static decimal ParseNetValue(string token) => decimal.Parse(ParseRateToken(token).Attribute("pot")?.Value, NumberFormatInfo.InvariantInfo);
        public static XElement ParseRateToken(string token)
        {
            try
            {
                var decode64 = Base64UrlTextEncoder.Decode(token);
                var decodeStr = System.Text.Encoding.UTF8.GetString(decode64);
                return XDocument.Parse(decodeStr).Element("rateToken");
            }
            catch (Exception e)
            {
                Console.WriteLine($"[ParseRateToken] Failed to parse rate token. Token: {token}\nException: {e}");
                throw;
            }
        }

        private static List<Models.Search.Flight> ParseToFlights(FareProfile fareProfile, List<Models.GtwAereo.Leg> legs, Dictionary<string, Airports> dictAirports, ILogger logger)
        {
            var flights = new List<Models.Search.Flight>();

            foreach (var leg in legs)
            {
                try
                {
                    flights.Add(new Models.Search.Flight
                    {
                        ArrivalAirport = leg.Arrival,
                        ArrivalAirportDescription = dictAirports.ContainsKey(leg.Arrival) && !string.IsNullOrEmpty(dictAirports[leg.Arrival].Name) && !string.IsNullOrEmpty(dictAirports[leg.Arrival].Location?.Name)
                            ? $"{dictAirports[leg.Arrival].Name}, {dictAirports[leg.Arrival].Location.Name}"
                            : "-",
                        ArrivalDateTime = leg.ArrivalDate,
                        DepartureAirport = leg.Departure,
                        DepartureAirportDescription = dictAirports.ContainsKey(leg.Departure) && !string.IsNullOrEmpty(dictAirports[leg.Departure].Name) && !string.IsNullOrEmpty(dictAirports[leg.Departure].Location?.Name)
                            ? $"{dictAirports[leg.Departure].Name}, {dictAirports[leg.Departure].Location.Name}"
                            : "-",
                        DepartureDateTime = leg.DepartureDate,
                        CiaCode = leg.OperatedBy?.Iata,
                        CiaName = leg.OperatedBy?.Name,
                        Cabin = (leg.SeatClass != null) ? leg.SeatClass.Description : "",
                        FlightNumber = leg.FlightNumber,
                        BaggageInfo = new BaggageInfo
                        {
                            HandBaggageQuantity =
                                fareProfile.Services?.Any(s => s.Type == "HAND_LUGGAGE" && !s.IsIncluded) == true ? 0 : 1,
                            HandBaggageWeight = fareProfile.Services?.Any(s => s.Type == "HAND_LUGGAGE" && !s.IsIncluded) == true
                                ? 0
                                : Services.HandBaggageWeightService.parse(fareProfile.Services?
                                    .FirstOrDefault(s => s.Type == "HAND_LUGGAGE")?.Description),
                            CheckedBaggageQuantity = decimal.ToInt32(fareProfile.Baggage?.Quantity ?? 0),
                            CheckedBaggageWeight = decimal.ToInt32(fareProfile.Baggage?.Weight ?? 0)
                        },
                        Stops = ParseToStops(leg.Stops),
                        MinutesDuration = leg.Duration,
                        NumberOfStops = leg.NumberOfStops,
                        SeatsLeft = leg.SeatsLeft
                    });
                }
                catch (Exception e)
                {
                    string fareProfileJson = fareProfile != null ? JsonConvert.SerializeObject(fareProfile) : "fareProfile is null";
                    string legJson = leg != null ? JsonConvert.SerializeObject(leg) : "leg is null";
                    string errorMessage = $"Erro ao converter voo. FareProfile: {fareProfileJson}, Leg: {legJson}, Mensagem de erro: {e.Message}";

                    logger.LogError(e, errorMessage);
                    return new List<Flight>();
                }
            }

            return flights;
        }

        private static List<Models.Search.Stop> ParseToStops(List<Models.GtwAereo.Stop> stops)
        {
            var parsedStops = new List<Models.Search.Stop>();

            if (stops != null)
            {
                foreach (var stop in stops)
                {
                    parsedStops.Add(new Models.Search.Stop
                    {
                        Airport = stop.Airport,
                        DepartureDate = stop.DepartureDate,
                        ArrivalDate = stop.ArrivalDate
                    });
                }
            }

            return parsedStops;
        }
        public static Models.Serviceui.SearchResponseError ParseRequestUpsellOrCheckoutDTOToError(RequestUpsellOrCheckoutDTO request, Exception ex)
        {

            MatchCollection words = Regex.Matches(ex.Message, @"\[(.*?)\]");

            var operationError = (words.Count > 0 && words[0].Groups.Count > 0) ? words[0].Groups[0].Value : null;

            var operation = operationError != null && operationError.Contains("Pricing") ? "hasAvail" : operationError;

            return new Models.Serviceui.SearchResponseError
            {
                ServiceError = new Models.Serviceui.SearchDetailsResponseError
                {
                    Operation = operation,
                    ErrorMessage = ex.ToString(),
                    FriendlyMessage = ex.Message,
                    Source = ex.Source,
                    OfferUpsell = request.OfferUpsell,
                    Provider = request.Provider,
                    CanDirectToNewCheckout = request.CanDirectToNewCheckout,
                    CanDirectToPreCheckout = request.CanDirectToPreCheckout,
                    PriceGroupId = request.PriceGroupId,
                    SearchId = request.SearchId,
                    ClientId = request.ClientId,
                    OperationalId = request.OperationalId,
                    Flights = request.Flights,
                    FlightId = new FlightExtraDataResponseError
                    {
                        Flights = request.FlightId.Flights,
                        OperationalId = request.FlightId.OperationalId,
                    },
                    CacheIds = request.CacheIds.Select(c => new SegmentAndFlightIdxResponseError
                    {
                        SegmentIndex = c.SegmentIndex,
                        ScheduleIndex = c.ScheduleIndex,
                        SegmentHour = c.SegmentHour
                    }).ToArray(),
                },
            };
        }

        public static Models.Search.BaggageByPTC ParseToReducedBaggage(Models.GtwAereo.BaggageByPTC baggage)
        {
            if (baggage == null || !baggage.HasBaggage())
                return null;

            return new Models.Search.BaggageByPTC
            {
                ADT = baggage.ADT?.Select(ParseToReducedBaggageType).ToList(),
                CHD = baggage.CHD?.Select(ParseToReducedBaggageType).ToList(),
                INF = baggage.INF?.Select(ParseToReducedBaggageType).ToList()
            };
        }

        private static Models.Search.BaggageType ParseToReducedBaggageType(Models.GtwAereo.BaggageType baggageType)
        {
            if (baggageType == null)
                return null;

            return new Models.Search.BaggageType
            {
                Type = baggageType.Type,
                Count = baggageType.Count ?? 0,
                Weight = baggageType.Weight,
                Dimensions = ParseToReducedDimensions(baggageType.Dimensions),
                LinearSize = baggageType.LinearSize
            };
        }

        private static Models.Search.BaggageDimensions ParseToReducedDimensions(Models.GtwAereo.BaggageDimensions dimensions)
        {
            if (dimensions == null)
                return null;

            return new Models.Search.BaggageDimensions
            {
                Length = dimensions.Length ?? 0,
                Width = dimensions.Width ?? 0,
                Depth = dimensions.Depth ?? 0
            };
        }

    }
}
