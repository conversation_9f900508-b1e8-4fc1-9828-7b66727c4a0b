﻿using Backend.Flights.Models;
using Backend.Flights.Models.Matriz3Dias;
using Backend.Flights.Models.Search;
using Backend.Flights.Util;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Backend.Flights.Services
{
    public class AirGtwService : IAirGtw
    {
        private readonly IConsulWatch _consul;
        private readonly ILogger _logger;

        public AirGtwService(IConsulWatch consul, ILogger<IAirGtw> logger)
        {
            _consul = consul;
            _logger = logger;
        }

        public async Task<MatrixResponse> CalendarShop(CalendarShopRequest request, IContextBrand contextBrand, int days)
        {
            // preparar chamada http para servidor do gtw
            // realizar a chamada
            // parsear o resultado
            // retornar o resultado

            // TODO: Refatorar para setar os headers nessa service 
            var wc = new WebClient();
            contextBrand.SetAuthHeaders(wc);

            var url = BuildCalendarShopUrl(request, contextBrand, days);

            var sw = Stopwatch.StartNew();
            var response = await wc.DownloadStringTaskAsync(url);
            sw.Stop();

            var gtwTimeMessage = sw.Elapsed;

            sw = Stopwatch.StartNew();
            var result = JsonConvert.DeserializeObject<MatrixResponse>(response);
            sw.Stop();

            // [log-disabled] _logger.LogInformation("AirGtw Request Time: {gtwTimeMessage}", gtwTimeMessage);
            // [log-disabled] _logger.LogInformation("Deserialization Time: {deserializationTimeMessage}", sw.Elapsed);

            return result;
        }

        public static string getAges(SearchRequest request)
        {
            var isInvalid = request.Adults == 0 && request.Children == 0 && request.Infants == 0;

            var m = new[] {
                new { Age = 30, Total = isInvalid ? 1 : request.Adults },
                new { Age = 10, Total = request?.Children ?? 0 },
                new { Age = 1, Total = request?.Infants ?? 0 }
            };

            var sb = new StringBuilder();
            foreach (var item in m)
            {
                if (item.Total == 0) continue;

                if (sb.Length > 0)
                    sb.Append(",");
                sb.Insert(0, $"{item.Age},", item.Total).Remove(sb.Length - 1, 1);
            }
            return sb.ToString();
        }
        public static string getCityPairs(List<CityPair> cityPairs)
        {
            if (cityPairs.Count == 0)
                return string.Empty;

            var sb = new StringBuilder();
            foreach (var pair in cityPairs)
                sb.AppendFormat("{0},{1},{2}-{3:D2}-{4:D2}+", pair.OriginIata, pair.DestinationIata, pair.DepartureDate.Year, pair.DepartureDate.Month, pair.DepartureDate.Day);
            return sb.Remove(sb.Length - 1, 1).ToString();
        }

        public static string CreateBasicFilterQueryString(BrandContext contextBrand, SearchRequest request)
        {
            var sb = new StringBuilder();
            sb.Append("ages=");
            sb.Append(HttpUtility.UrlEncode(GtwAereo.getAges(request)));
            sb.Append("&routes=");
            sb.Append(HttpUtility.UrlEncode(GtwAereo.getCityPairs(request.CityPairs)));

            sb.Append("&packageGroup=");
            if (contextBrand.GatewayHeaders.AgentSign == IContextBrand.AGENT_SIGN_LOJAS)
            {
                if (!string.IsNullOrWhiteSpace(request.PackageGroup) &&
                    !"undefined".Equals(request.PackageGroup?.ToLower() ?? string.Empty))
                {
                    sb.Append(HttpUtility.UrlEncode(request.PackageGroup!.ToUpper()));
                }
                else
                {
                    sb.Append("STANDALONE");
                }
            }
            else
            {
                sb.Append(request.Package == true ? "PACKAGE" : "VHI,VHIPLUS,STANDALONE");
            }

            sb.Append("&businessClass=");
            sb.Append(request.Cabin.IsNotEmptyAndEquals("exe") ? "YES" : "ALSO");

            return sb.ToString();
        }

        private Uri BuildCalendarShopUrl(CalendarShopRequest request, IContextBrand contextBrand, int days)
        {
            var sb = new StringBuilder(contextBrand.Context.GatewayUrlV1);
            sb.Append("/flights/calendarshop?source=ALL&");
            // O CalendarShop não está aceitando source além do SAB e ALL 
            // sb.Append(await _consul.WatchKey(STR_KEY_GATEWAY_SOURCES));
            sb.Append(CreateBasicFilterQueryString(contextBrand.Context, request));
            sb.Append("&numberOfDays=");
            sb.Append(days);
            sb.Append("&currency=BRL");

            return new Uri(sb.ToString());
        }
    }
}