﻿using Backend.Flights.Business;
using Backend.Flights.Models;
using Backend.Flights.Models.Price;
using Backend.Flights.Models.Search;
using Backend.Flights.Util;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using Backend.Flights.CA.Services.Http;
using Backend.Flights.CA.Services.Json;

namespace Backend.Flights.Services
{
    public class GtwAereo
    {
        public bool UseContentProvider { get; private set; }

        private readonly ILogger _logger;
        private readonly string _url;
        private readonly string _searchFlightsUrl;
        private readonly string _searchFlightApiKey;
        private readonly bool _searchFlightsEnabled;
        private readonly BrandContext brandCtx;
        private readonly ContentProviderService _contentProviderService;
        private readonly IHttpClient _httpClient;
        private readonly IJsonService _json;

        public GtwAereo(BrandContext brandCtx, ConfigurationManager _config, ILogger logger, bool enableContentProvider, IHttpClient httpClient, IJsonService json)
        {
            _logger = logger;
            _searchFlightsUrl = _config.AppSetting["SearchFlights.URL"] + "/bff/flights?";
            _searchFlightApiKey = _config.AppSetting["SearchFlights.APIKEY"];
            _searchFlightsEnabled = _config.AppSetting["SearchFlights.Enabled"] == "1";
            _contentProviderService = ContentProviderService.Instance;
            UseContentProvider = enableContentProvider && _contentProviderService.ShouldUseContentProvider(brandCtx);
            _url = brandCtx.GatewayUrl + "/flights?";
            this.brandCtx = brandCtx;
            _httpClient = httpClient;
            _json = json;
        }

        private Task<string> getGatewayData(string url, string provider = null, SearchRequest request = null,
            Dictionary<string, string> additionalHeaders = null, bool rethrow = false)
            => getGatewayDataWithProvider(url, provider, request, additionalHeaders, rethrow);

        private async Task<string> getGatewayDataWithProvider(string url, string provider, SearchRequest request = null, Dictionary<string, string> additionalHeaders = null, bool rethrow = false)
        {
            var gwCorrelationId = string.Empty;
            var time = Stopwatch.StartNew();

            var reqPayload = new HttpRequestMessage(HttpMethod.Get, url);
            brandCtx.GatewayHeaders.SetHeaders(reqPayload.Headers);
            if (additionalHeaders != null)
                foreach (var k in additionalHeaders)
                    reqPayload.Headers.Add(k.Key, k.Value);

            try
            {
                var headers = HttpRequestExtensions.GetHeadersHttpRequestMessage(reqPayload);
                var (responsePayload, httpError, httpResponse) = await _httpClient.GetString(url, headers, timeout: 35);

                time.Stop();

                if (request != null)
                    gwCorrelationId = httpResponse.Headers.FirstOrDefault(x => x.Key == "GW_CORRELATION_ID").Value?.FirstOrDefault() ?? string.Empty;

                // [log-disabled] logSearch(provider, request, gwCorrelationId, time.ElapsedMilliseconds, (httpResponse.IsSuccessStatusCode ? "success" : "error"), url, reqPayload.Headers.ToDictionary(x => x.Key, x => x.Value));

                return responsePayload ?? httpError.Body;
            }
            catch (Exception e)
            {
                time.Stop();

                logSearch(provider, request, gwCorrelationId, time.ElapsedMilliseconds, e.Message, url, reqPayload.Headers.ToDictionary(x => x.Key, x => x.Value));

                if (rethrow)
                    throw;
                return JsonConvert.SerializeObject(new { error = new { code = "00-000-000", message = e.Message, providerError = "GATEWAY" } });
            }
        }

        private static bool IsGoogleFlightRequest(string url) => url.Contains("VHI - Google Flights");
        private void logSearch(string provider, SearchRequest request, string gwCorrelationId, long duration, string message, string url, Dictionary<string, IEnumerable<string>> headers)
        {
            var dict = new Dictionary<string, object>
            {
                ["duration"] = duration,
                ["short_message"] = message != null && message.Equals("success") ? "success" : "error",
                ["CorrelationId"] = gwCorrelationId,
                ["provider"] = provider,
                ["branch-id"] = brandCtx.GatewayHeaders.BranchId,
                ["agent-sign"] = brandCtx.GatewayHeaders.AgentSign
            };
            if (request == null)
                dict.Add("operation", "GTWPRICING");
            else
            {
                if (!message.Equals("success"))
                {
                    dict.Add("Exception", message);
                }
                dict.Add("origin", request.CityPairs[0].OriginIata);
                dict.Add("destination", request.CityPairs.Count < 3 ? request.CityPairs[0].DestinationIata : request.CityPairs[request.CityPairs.Count() - 1].OriginIata);
                dict.Add("operation", IsGoogleFlightRequest(url) ? "GOOGLEFLIGHTS" : "SEARCH");
                dict.Add("plataform", Validations.IsMobile(request.UserAgent) ? "Mobile" : "Desktop");
                dict.Add("product", request.Package.IsNotEmptyAndTrue() ? "AIRHOTEL" : "AIR");
                dict.Add("pax_combination", string.Concat(request.Adults, "-", request.Children, "-", request.Infants));
                dict.Add("outbound_date", request.CityPairs[0].DepartureDate.ToDateTime());
                dict.Add("inbound_date", request.CityPairs[request.CityPairs.Count() - 1].DepartureDate.ToDateTime());
                dict.Add("flight_class", request.Cabin);
                dict.Add("advp", request.CityPairs[0].DepartureDate.ToDateTime().Subtract(DateTime.Today).TotalDays);
                dict.Add("travel_type", Availability.GetSearchType(request.CityPairs, brandCtx.isCvc()).ToString());
                dict.Add("travel_route", request.CityPairs.Count < 3 ? $"{getParentIata(request.CityPairs[0].OriginIata)}-{getParentIata(request.CityPairs[0].DestinationIata)}" : string.Join('-', request.CityPairs.Select(cp => getParentIata(cp.OriginIata))));
                dict.Add("searchId", request.SearchId);
                dict.Add("clientId", request.ClientId);
            }

            using (_logger.BeginScope(dict))
            {
                _logger.LogInformation(JsonConvert.SerializeObject(new { message, url, headers = headers.ToDictionary(x => x.Key, x => string.Join(" ", x.Value)) }));
            }
        }

        public async Task<string> Flights(SearchRequest request, string provider, Dictionary<string, string> additionalHeaders = null)
        {
            try
            {
                var cp = UseContentProvider && provider == ContentProviderService.SOURCE_GDS;

                var url = cp ?
                    _contentProviderService.GetRequestUrl(brandCtx) :
                    _url;


                if (cp == false && this._searchFlightsEnabled)
                {
                    additionalHeaders ??= new Dictionary<string, string> { };
                    url = _searchFlightsUrl;
                    additionalHeaders.Add("apikey", _searchFlightApiKey);
                }

                additionalHeaders ??= new Dictionary<string, string> { };
                additionalHeaders.Add("searchId", request.SearchId);

                url += buildQueryString(request, cp ? "AMD,SAB,GLL" : provider, brandCtx);
                return await getGatewayDataWithProvider(url, provider, request, additionalHeaders);
            }
            catch (Exception e)
            {
                throw new Exception($"Erro ao consultar Gwaereo [Flights]: {e.GetErrorLocation()} {e.Message}");
            }
        }

        public async Task<string> Pricing(PriceRequest request)
        {
            try
            {
                var tokenString = string.Join(",", request.Tokens.Select(Base64ToBase64UrlSafe));
                var url = string.Concat(brandCtx.GatewayUrl, "/flights/", tokenString, "?preferences=", request.Preferences);
                return await getGatewayData(url, rethrow: true);
            }
            catch (Exception e)
            {
                throw new Exception($"Erro ao consultar Gwaereo [Pricing - hasAvail]: {e.Message}");
            }
        }


        public async Task<string> GoogleFlights(GoogleFlightsRequest request, string provider)
        {
            try
            {
                var url = _url + buildQueryStringForGoogleFlights(request, provider);
                var response = await getGatewayData(url, provider, request);

                return response;
            }
            catch (Exception e)
            {
                throw new Exception($"Erro ao consultar Gwaereo [GoogleFlights]: {e.Message}");
            }
        }

        public async Task<dynamic> BookingImport(string source, string locatorCode, string packageGroup)
        {
            var url = brandCtx.GatewayUrl + @$"/flights/bookings/{source.ToUpper()}/{locatorCode.ToUpper()}?packageGroup={HttpUtility.UrlEncode(packageGroup.ToUpper())}";

            var json = await getGatewayData(url, rethrow: true);

            return JsonConvert.DeserializeObject(json);
        }

        public object BookingImport2CartProduct(dynamic imported, ConfigurationManager config, string loc, string source)
        {
            var booking = imported.airBookings[0].booking;
            var priceGroup = booking.airs[0].priceGroup;

            var firstSegment = priceGroup.segments.First;
            var lastSegment = priceGroup.segments.Last;
            var firstLeg = firstSegment.legs.First;
            var lastLeg = lastSegment.legs.Last;

            var departureLocation = CheckoutData.PreCheckout.createLocation((string)firstLeg.departure, config, _logger);
            var arrivalLocation = CheckoutData.PreCheckout.createLocation((string)lastLeg.arrival, config, _logger);

            var cityPairs = ((JArray)priceGroup.segments).Select((dynamic s) => new CityPair()
            {
                OriginIata = s.departure,
                DestinationIata = s.arrival
            }).ToList();
            var isMultiDestinations = Availability.GetSearchType(cityPairs, brandCtx.isCvc()) ==
                                      Availability.SearchType.Multidestination;

            int countPax(string type)
            {
                var paxArr = (JArray)booking.paxs;
                return paxArr.Count((dynamic i) => i.passengerType == type);
            }

            bool isRefundable()
            {
                return ((JArray)priceGroup.segments).All((dynamic segment) =>
                        ((JArray)segment.fareProfile.services)
                        .FirstOrDefault((dynamic service) => service.Type != null && service.type == "REFUNDABLE")
                        ?.isIncluded?.Value ?? false
                    );
            }

            string getLegLocationDescription(string iata)
            {
                var location = new AirLocations(config).LoadIata(iata, _logger);
                return location.Description;
            }

            object ToIataNameAndCode(dynamic iataSource)
            {
                return new
                {
                    name = iataSource.name.Value,
                    iata = iataSource.iata.Value
                };
            }

            object createCheckoutLegs(dynamic segment)
            {
                return ((JArray)segment.legs).Select((dynamic leg) => new
                {
                    departure = leg.departure.Value,
                    departureAirport = getLegLocationDescription(leg.departure.Value),
                    departureDate = leg.departureDate.Value,
                    arrival = leg.arrival.Value,
                    arrivalAirport = getLegLocationDescription(leg.arrival.Value),
                    arrivalDate = leg.arrivalDate.Value,
                    operatedBy = ToIataNameAndCode(leg.operatedBy),
                    seatClass = leg.seatClass.description?.Value ?? leg.seatClass.code?.Value,
                    duration = leg.duration.Value,
                    flightNumber = leg.flightNumber.Value?.ToString(),
                    managedBy = ToIataNameAndCode(leg.managedBy),
                });
            }

            object createBaggageInfo(dynamic segment)
            {
                return new
                {
                    hand = ((JArray)segment.fareProfile.services)
                        .Any((dynamic s) => s.type == "HAND_LUGGAGE" && !s.isIncluded.Value) ? 0 : 1,
                    @checked = decimal.ToInt32(segment.fareProfile.baggage?.quantity?.Value ?? 0),
                    handWeight = ((JArray)segment.fareProfile.services)
                        .Any((dynamic s) => s.type == "HAND_LUGGAGE" && !s.isIncluded.Value) ? 0 :
                            HandBaggageWeightService.parse(
                                ((JArray)segment.fareProfile.services)
                                .FirstOrDefault((dynamic s) => s.type == "HAND_LUGGAGE")?.description?.ToString()),
                    checkWeight = decimal.ToInt32(segment.fareProfile.baggage?.weight?.Value ?? 0)
                };
            }

            object createGroup(dynamic segment)
            {
                return new
                {
                    legs = createCheckoutLegs(segment),
                    baggage = createBaggageInfo(segment),
                    totalMinuteDuration = segment.duration.Value,
                    numberOfStops = segment.numberOfStops.Value
                };
            }

            return new
            {
                type = "flight",
                data = new
                {
                    importLoc = true,
                    player = source,
                    statusLoc = (string)booking?.status,
                    loc,
                    departureLocation = departureLocation,
                    arrivalLocation = arrivalLocation,
                    productStartDate = firstLeg.departureDate.ToString("yyyy-MM-ddThh:mm:ss"),
                    productEndDate = lastLeg.departureDate.ToString("yyyy-MM-ddThh:mm:ss"),
                    packageGroup = (string)firstSegment.packageGroup,
                    paxGroup = new
                    {
                        adult = countPax("ADT"),
                        children = countPax("CHD"),
                        babies = countPax("INF")
                    },
                    fareGroup = new
                    {
                        currency = (string)priceGroup.fareGroup.currency,
                        priceWithTax = (double)priceGroup.fareGroup.priceWithTax,
                        priceWithoutTax = (double)priceGroup.fareGroup.priceWithoutTax,
                        refundable = isRefundable(),
                        rateTokens = ((JArray)priceGroup.segments).Select((dynamic segment) => new
                        {
                            routeRPH = segment.routeRPH.Value,
                            packageGroup = segment.packageGroup.Value,
                            rateToken = segment.rateToken.Value
                        }),
                    },
                    isMulti = isMultiDestinations,
                    segments = ((JArray)priceGroup.segments).Select(createGroup),
                }
            };
        }

        private static string buildQueryStringForGoogleFlights(GoogleFlightsRequest request, string provider)
            => $"airCompanies={request.AirCompany}&ages={getAges(request)}&preferences=persistLog,ignoreCachedUnavailableFlights,loadInstallments:true,language:pt_BR,currency:BRL&source={provider}&routes={getCityPairs(request.CityPairs)}&packageGroup=VHI - Google Flights&businessClass={(request.Cabin.IsNotEmptyAndEquals("exe") ? "YES" : "ALSO")}";

        // private static string buildQueryString(SearchRequest request, string provider, BrandContext contextBrand)
        //     => $"{AirGtwService.CreateBasicFilterQueryString(contextBrand, request)}&preferences=persistLog,ignoreCachedUnavailableFlights,loadInstallments:true,language:pt_BR,currency:BRL&source={provider}";

        private static string buildQueryString(SearchRequest request, string provider, BrandContext contextBrand)
        {
            // Constrói a query string base com os filtros e parâmetros principais
            var queryString = $"{AirGtwService.CreateBasicFilterQueryString(contextBrand, request)}" +
                              $"&preferences=persistLog,ignoreCachedUnavailableFlights,loadInstallments:true,language:pt_BR,currency:BRL" +
                              $"&source={provider}";

            // Adiciona o filtro de `AirCompanies` à query string, se estiver presente
            if (request.AirCompanies?.Any(ac => !string.IsNullOrWhiteSpace(ac)) == true)
            {
                queryString += $"&airCompanies={string.Join(",", request.AirCompanies)}";
            }

            return queryString;
        }

        private static string Base64ToBase64UrlSafe(string base64)
        {
            return base64
                .Replace('+', '-')
                .Replace('/', '_');
        }

        public static string getAges(SearchRequest request)
        {
            var isInvalid = request.Adults == 0 && request.Children == 0 && request.Infants == 0;

            var m = new[] {
                new { Age = 1, Total = request?.Infants ?? 0 },
                new { Age = 10, Total = request?.Children ?? 0 },
                new { Age = 30, Total = isInvalid ? 1 : request.Adults }
            };

            var sb = new StringBuilder();
            foreach (var item in m)
            {
                if (item.Total == 0) continue;

                if (sb.Length > 0)
                    sb.Append(",");
                sb.Insert(0, $"{item.Age},", item.Total).Remove(sb.Length - 1, 1);
            }
            return sb.ToString();
        }
        public static string getCityPairs(List<CityPair> cityPairs)
        {
            if (cityPairs.Count == 0)
                return string.Empty;

            var sb = new StringBuilder();
            foreach (var pair in cityPairs)
                sb.AppendFormat("{0},{1},{2}-{3:D2}-{4:D2}+", pair.OriginIata, pair.DestinationIata, pair.DepartureDate.Year, pair.DepartureDate.Month, pair.DepartureDate.Day);
            return sb.Remove(sb.Length - 1, 1).ToString();
        }
        public static string getParentIata(string iata)
        {
            switch (iata)
            {
                case "GRU":
                case "VCP":
                case "CGH":
                    return "SAO";
                case "SDU":
                case "GIG":
                    return "RIO";
                case "CNF":
                    return "BHZ";
                case "JFK":
                case "LGA":
                    return "NYC";
                case "FLL":
                    return "MIA";
                default:
                    return iata;
            }
        }
    }
}