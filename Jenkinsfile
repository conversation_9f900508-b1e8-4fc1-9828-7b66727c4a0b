@Library('cvc-<PERSON><PERSON><PERSON>-lib')

final def projectConfig = this.readJSON(text: """
{
    "name": "sub-backend-flights",
    "git": {
        "repositoryUrl": "******************:shopping-flights/sub-backend-flights.git"
    },
    "technology": {
        "name": "dotnet",
        "version": "7.0",
        "buildCommands": {
            "buildApp": "dotnet build src/Backend.Flights/Backend.Flights.sln"
        },
        "sonar": {
            "reportPaths": {
                "vstest": "src/*/*.trx",
                "opencover": "src/*/*.trx"
            },
            "solutionPath": "src/Backend.Flights/Backend.Flights.sln"
        }
    },
    "docker": {
        "dockerfilePath": "Dockerfile"
    },
    "kubernetes": {
        "namespace": "sub-flights"
    }
}
""")

final commons = cvcCorpPipeline.getCommons(this, projectConfig)

pipeline {
    agent any
    stages {
        stage('Deploy TI') {
            steps {
                script {
                    deploy(this, 'TI', projectConfig, commons) {
                        
                    }
                }
            }
        }
        stage('Deploy QA') {
            steps {
                script {
                    deploy(this, 'QA', projectConfig, commons) {

                    }
                }
            }
        }
        stage('Deploy PROD') {
            steps {
                script {
                    deploy(this, 'PROD', projectConfig, commons) {

                    }
                }
            }
        }
    }
}
