using Backend.Flights.CA.Model;
using Backend.Flights.CA.Services.Config;
using Backend.Flights.Util;
using Newtonsoft.Json;
using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Backend.Flights.CA.UseCase.Config
{
    public class WatchConfig : IWatchConfig
    {
        private readonly IConfigService _config;

        private readonly ConcurrentDictionary<string, string> _cache = new ConcurrentDictionary<string, string>();

        public event EventHandler<ConfigItemChangedEventArgs> ConfigChanged;

        public WatchConfig(IConfigService config)
        {
            _config = config;
        }

        public async Task<string> GetConfig(string configName)
        {
            string value;

            if (_cache.TryGetValue(configName, out value))
            {
                return value;
            }

            value = await _config.GetConfig(configName);

            $"[WatchConfig] Load config: {configName} = {value}".LogInfo();

            _cache.TryAdd(configName, value);

            return value;
        }

        public async Task<T> GetConfig<T>(string configName)
        {
            string configString = await GetConfig(configName);

            // Teste apontando para produção
            //string configString = """{"useNewService":true,"endpoints":{"checklist":{"url":"http://gs.cvc.com.br/","path":"CVCWSCheckListAereo","fixedQueryParams":{"gatewayOrigin":"VCRA","codigoEmpresa":"2","importacao":"S"}},"credipax":{"url":"http://systur.cvc.com.br/pls/systur","path":"pkg_aer_validacao.prc_consulta_ficha_json"}}}""";

            if (string.IsNullOrWhiteSpace(configString))
                return default;

            try
            {
                T config = JsonConvert.DeserializeObject<T>(configString);
                return config;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"[GetConfig]: Erro ao desserializar a configuração '{configName}' para o tipo {typeof(T).FullName}.", ex);
            }
        }

        public async Task DetectChanges(CancellationToken cancellationToken)
        {
            var keys = _cache.Keys.ToArray();
            foreach (var key in keys)
            {
                if (cancellationToken.IsCancellationRequested)
                {
                    return;
                }

                var val = await _config.GetConfig(key);

                if (cancellationToken.IsCancellationRequested)
                {
                    return;
                }

                // ReSharper disable once InvertIf
                if (
                    val != null &&
                    _cache.TryGetValue(key, out var curVal) &&
                    !val.Equals(curVal)
                )
                {
                    _cache.TryUpdate(key, val, curVal);
                    OnConfigChanged(key, val);
                }
            }
        }

        public async Task WatchChange(int updateIntervalMs, CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(updateIntervalMs, cancellationToken);

                await DetectChanges(cancellationToken);
            }
        }

        private void OnConfigChanged(string name, string value)
        {
            ConfigChanged?.Invoke(this, new ConfigItemChangedEventArgs()
            {
                Name = name,
                Value = value
            });
        }
    }
}