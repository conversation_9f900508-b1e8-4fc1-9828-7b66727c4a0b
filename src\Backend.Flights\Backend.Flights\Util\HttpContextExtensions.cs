using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Http;

namespace Backend.Flights.Util
{
    public static class HttpContextExtensions
    {
        public static string GetCookieHeaderValues(this HttpContext context, string headerName)
        {
            if (!context.Request.Headers.TryGetValue("Cookie", out var cookieHeaders))
                return string.Empty;

            var values = new List<string>();

            foreach (var cookie in cookieHeaders)
            {
                var cookieParts = cookie.Split(';');
                foreach (var part in cookieParts)
                {
                    var keyValue = part.Trim().Split('=');
                    if (keyValue.Length == 2 && string.Equals(keyValue[0].Trim(), headerName, StringComparison.OrdinalIgnoreCase))
                    {
                        values.Add(keyValue[1].Trim());
                    }
                }
            }

            return values.Count > 0 ? string.Join(", ", values) : string.Empty;
        }

        public static string GetHeaderValues(this HttpContext context, string headerName)
        {
            if (!context.Request.Headers.TryGetValue(headerName, out var headerValue))
                return string.Empty;

            return headerValue;
        }
    }
}