﻿using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Backend.Flights.CA.UseCase.BrandContext
{
    public class GetBrandContextById : IGetBrandContextById
    {
        private readonly Config.IWatchConfig _config;
        private readonly Services.Json.IJsonService _json;
        private readonly ILogger _logger;

        private readonly SemaphoreSlim _localLock = new SemaphoreSlim(1);

        private ConcurrentDictionary<string, Model.Context.BrandContext> _contexts;

        public GetBrandContextById(ILogger<GetBrandContextById> logger, Config.IWatchConfig config, Services.Json.IJsonService json)
        {
            _config = config;
            _json = json;
            _logger = logger;
        }

        private void ParseBrands(string data, string branchId = null)
        {
            try
            {
                var brands = _json.Deserialize<Dictionary<string, Model.Context.BrandContext>>(data);
                var toDelete = _contexts.Keys.Where(k => !brands.Keys.Contains(k)).ToArray();
                foreach (var b in brands)
                {
                    if(b.Key == "lojas_wl" && branchId != null)
                    {
                        b.Value.GatewayHeaders["Gtw-Branch-Id"] = branchId;
                    }
                    _contexts.AddOrUpdate(b.Key, b.Value, (_0, _1) => b.Value);
                }
                foreach (var d in toDelete)
                {
                    _contexts.TryRemove(d, out var _);
                }
            }
            catch (Exception ex)
            {
                _logger.LogCritical(ex, "Fail on parse brands, json: {json}", data);
            }
        }

        private async Task<ConcurrentDictionary<string, Model.Context.BrandContext>> GetBrands(string branchId = null)
        {
            if (_contexts == null)
            {
                await _localLock.WaitAsync();
                try
                {
                    if (_contexts == null)
                    {
                        _contexts = new ConcurrentDictionary<string, Model.Context.BrandContext>();
                        _config.ConfigChanged += (_, d) =>
                        {
                            if (d.Name == Services.Config.IConfigService.BRAND_CONTEXTS)
                            {
                                ParseBrands(d.Value, branchId);
                            }
                        };

                        var ctx = await _config.GetConfig(Services.Config.IConfigService.BRAND_CONTEXTS);
                        ParseBrands(ctx, branchId);
                    }
                }
                finally
                {
                    _localLock.Release();
                }
            }

            return _contexts;
        }

        public async Task<Model.Context.BrandContext> Execute(string id, string branchId = null)
        {
            var brds = await GetBrands(branchId);
                   
            var brandCtx = brds.GetValueOrDefault(id);

            brandCtx.GatewayHeaders["Accept-Encoding"] = "gzip";

            return brandCtx;
        }
    }
}
