<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net7.0</TargetFramework>
		<AspNetCoreHostingModel>OutOfProcess</AspNetCoreHostingModel>
		<AspNetCoreModuleName>AspNetCoreModule</AspNetCoreModuleName>
        <InvariantGlobalization>true</InvariantGlobalization>
	</PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <DocumentationFile>C:\Projects\sub-backend-flights\src\Backend.Flights\Backend.Flights\Backend.Flights.xml</DocumentationFile>
    <NoWarn>1701;1702;1591</NoWarn>
    <DefineConstants>DEBUG;TRACE;LOCAL</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(RunConfiguration)' == 'Prod' " />
  <ItemGroup>
    <Compile Remove="Controllers\v1\**" />
    <Compile Remove="Controllers\v2\**" />
    <Content Remove="Controllers\v1\**" />
    <Content Remove="Controllers\v2\**" />
    <EmbeddedResource Remove="Controllers\v1\**" />
    <EmbeddedResource Remove="Controllers\v2\**" />
    <None Remove="Controllers\v1\**" />
    <None Remove="Controllers\v2\**" />
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Controllers\ValuesController.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.RouteAnalyzer" Version="0.5.3" />
    <PackageReference Include="Consul" Version="1.6.10.7" />
    <PackageReference Include="Microsoft.IO.RecyclableMemoryStream" Version="2.2.1" />
    <PackageReference Include="Nanoid" Version="2.1.0" />
    <PackageReference Include="protobuf-net" Version="3.1.25" />
    <PackageReference Include="Gelf.Extensions.Logging" Version="2.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.1" />
    <PackageReference Include="MongoDB.Driver" Version="2.19.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.2" />
    <PackageReference Include="StackExchange.Redis" Version="2.6.80" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.4.0" />
  </ItemGroup>

  <ProjectExtensions>
    <VisualStudio>
      <UserProperties appsettings_1ti_1json__JSONSchema="" />
    </VisualStudio>
  </ProjectExtensions>

</Project>