using Backend.Flights.CA.Services.Http;
using Backend.Flights.CA.Services.UserToken;
using System;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Backend.Flights.CA.UseCase.BrandContext
{
    public class GetCurrentBrandContext : IGetCurrentBrandContext
    {
        private readonly Regex _jwtValidate = new Regex(@"^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+$");

        private readonly IHttpRequestService _httpRequest;
        private readonly IUserTokenService _userToken;
        private readonly IGetBrandContextById _getBrandContextById;

        public GetCurrentBrandContext(
            IHttpRequestService httpRequest,
            IUserTokenService userToken,
            IGetBrandContextById getBrandContextById
            )
        {
            _httpRequest = httpRequest;
            _userToken = userToken;
            _getBrandContextById = getBrandContextById;
        }

        public string GetBaseBrand()
        {
            var brandurl = _httpRequest.GetHeader(IGetCurrentBrandContext.CONTEXT_HTTP_HEADER);

            return string.IsNullOrEmpty(brandurl) ? _httpRequest.GetHost() : brandurl;
        }

        public string GetGtwPricing()
        {
            return _httpRequest.GetHeader(IGetCurrentBrandContext.CONTEXT_GTW_PRICING_HEADER);
        }

        public string GetUserToken()
        {
            var tkn = _httpRequest.GetCookie(IUserTokenService.COOKIE_ACCESS_TOKEN);

            return _jwtValidate.IsMatch(tkn ?? string.Empty) ? tkn : null;
        }

        public async Task<Model.Context.BrandContext> Execute(string userTokenFallBack = null, bool isImportaLoc = false)
        {
            var userToken = GetUserToken() ?? userTokenFallBack;
            var userInfo = _userToken.GetUserInfo(userToken);
            var isVendor = userInfo?.UserType?.Equals(IUserTokenService.USER_TYPE_VENDOR, System.StringComparison.InvariantCultureIgnoreCase) == true;
            var gtwPricing = GetGtwPricing();
            var hasGtwPricing = !string.IsNullOrEmpty(gtwPricing) && string.Equals(gtwPricing, "PF", StringComparison.OrdinalIgnoreCase);


            if (isVendor && userInfo.BranchId.HasValue)
            {
                var valid = await _userToken.ValidateUser(userToken);
                if (valid)
                {
                    var newContext = await _getBrandContextById.Execute(IUserTokenService.USER_CTX_LOJA, userInfo.BranchId.Value.ToString());
                    if (newContext == null)
                    {
                        return null;
                    }

                    newContext.UserToken = userToken;
                    newContext.BranchId = userInfo.BranchId.Value;
                    newContext.User = userInfo;

                    if (hasGtwPricing)
                        newContext.GatewayHeaders["Gtw-Pricing"] = GetGtwPricing();
                    else
                        newContext.GatewayHeaders?.Remove("Gtw-Pricing");

                    // Feito isso pois o importa loc não está passando o branchId correto no header em alguns cenários
                    // Por hora vamos testar apenas no importa loc essa alteração
                    if (isImportaLoc)
                    {
                        newContext.GatewayHeaders["Gtw-Branch-Id"] = userInfo.BranchId.Value.ToString();
                        newContext.GatewayHeaders["Gtw-Agent-Sign"] = userInfo.AgentSign;
                    }

                    return newContext;
                }
            }

            var brandId = GetBaseBrand();
            Model.Context.BrandContext ctx = null;
            if (userInfo != null)
            {
                ctx = await _getBrandContextById.Execute(brandId, userInfo?.BranchId.Value.ToString());
            }
            else
            {
                ctx = await _getBrandContextById.Execute(brandId);
            }
            return ctx ?? null;
        }
    }
}