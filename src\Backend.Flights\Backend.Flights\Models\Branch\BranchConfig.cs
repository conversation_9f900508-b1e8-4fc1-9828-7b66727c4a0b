using System;
using Backend.Flights.Models.Serviceui;
using Newtonsoft.Json;

namespace Backend.Flights.Models.Branch
{
    public class BranchConfig
    {
        public bool IsV4 { get; }
        
        public Error Error { get; }

        public BranchConfig(bool isV4)
        {
            IsV4 = isV4;
        }

        public BranchConfig(bool isV4, Exception ex, string friendlyMessage)
        {
            IsV4 = isV4;
            Error = new Error
            {
                ErrorMessage = ex.Message,
                FriendlyMessage = friendlyMessage,
                Source = ex.Source
            };

        }
    }
}