﻿using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net.Http;
using System.Threading.Tasks;
using System.Net;
using Backend.Flights.Util;

namespace Backend.Flights.Services
{
    public static class Locations
    {
        private static readonly HttpClient httpClient = new HttpClient(new HttpClientHandler { AutomaticDecompression = DecompressionMethods.GZip });
        public static async Task<Models.Locations.LocationsResponse> GetAsync(ILogger _logger, string baseUrl, string query)
        {
            void log(string msg, long duration, bool success, string longMessage)
            {
                using (_logger.BeginScope(new Dictionary<string, object>
                {
                    ["duration"] = duration,
                    ["short_message"] = msg,
                    ["operation"] = "LOCATION",
                    ["sucess"] = success ? 1 : 0,
                }))
                    _logger.LogInformation(longMessage);
            }

            var sw = Stopwatch.StartNew();
            try
            {
                var response = await httpClient.GetAsync($"{baseUrl}locations?productType=AIR&q={query}");
                var content = await response.Content.ReadAsStringAsync();

                await response.ToCurlCommand();

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError($"Error locations {(int)response.StatusCode}: {response.ReasonPhrase} - {content} - cURL: {await response.ToCurlCommand(true)}");
                    throw new Exception($"Error locations {(int)response.StatusCode}: {response.ReasonPhrase} - {content}");
                }

                var ret = JsonConvert.DeserializeObject<Models.Locations.LocationsResponse>(content);
                if (ret.Locations == null)
                {
                    _logger.LogError($"No locations found: {content} - cURL: {await response.ToCurlCommand(true)}");
                }

                // [log-disabled] log(query, sw.ElapsedMilliseconds, true, string.Empty);
                return ret;
            }
            catch (Exception ex)
            {
                log(ex.Message, sw.ElapsedMilliseconds, false, ex.StackTrace);
                Console.WriteLine(ex.Message);
                throw;
            }
        }
    }
}
