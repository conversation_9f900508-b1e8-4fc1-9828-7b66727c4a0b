using Backend.Flights.CA.Model;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Backend.Flights.CA.UseCase.Config
{
    public interface IWatchConfig
    {
        event EventHandler<ConfigItemChangedEventArgs> ConfigChanged;
        Task<string> GetConfig(string configName);
        Task<T> GetConfig<T>(string configName);
        Task WatchChange(int updateIntervalMs, CancellationToken cancellationToken);
    }
}