﻿using Backend.Flights.CA.Services.Config;
using Backend.Flights.Models;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;


namespace Backend.Flights.Services
{
    public class ContextBrandsService : IContextBrands
    {


        private readonly ILogger _logger;
        public Dictionary<string, BrandContext> BrandContexts { get; set; }

        public ContextBrandsService(IConsulWatch consul, ILogger<ContextBrandsService> logger)
        {
            _logger = logger;

            var val = consul.WatchKey(IConfigService.BRAND_CONTEXTS).Result;
            BrandContexts = JsonConvert.DeserializeObject<Dictionary<string, BrandContext>>(val);
#if DEBUG
            if (!BrandContexts.ContainsKey("localhost"))
            {
                BrandContexts.Add("localhost", BrandContexts["lojas_wl"]); // Context LOJ
                //BrandContexts.Add("localhost", BrandContexts["www.cvc.com.br"]); // Context WEB
            }
#endif

            consul.KeyChanged += (s, a) =>
            {
                if (a.Key != IConfigService.BRAND_CONTEXTS) return;

                try
                {
                    BrandContexts = JsonConvert.DeserializeObject<Dictionary<string, BrandContext>>(a.Value);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Fail on configure BrandContexts");
                }
            };
        }

        public BrandContext GetContext(string host)
        {
            try
            {
                return BrandContexts[host];
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Context brand not found for host {host}. Available contexts: {contexts} {metadata}", host, string.Join(", ", BrandContexts.Keys), "Geralmente ocorre esse erro pois o token do usuário está expirado!");

                throw new Exception($"Context brand not found for host {host}");
            }
        }
    }
}