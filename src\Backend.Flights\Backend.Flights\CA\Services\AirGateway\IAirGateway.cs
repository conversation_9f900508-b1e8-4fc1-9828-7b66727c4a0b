﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Backend.Flights.CA.Services.AirGateway
{
    public interface IAirGateway
    {
        string MakeUrlSearchParams(Model.Context.BrandContext brandContext, Model.Search.SearchRequest request);

        IAsyncEnumerable<(Model.Search.Result.SearchResultError, Model.Search.Result.SearchResultFlight)> Search(
            Model.Context.BrandContext brandContext,
            Model.Search.SearchRequest request,
            string source);

        Task<SafeResult<dynamic, Tuple<int, string, string>>> LocImport(Model.Context.BrandContext brandContext, string loc, string source, string packageGroup);
    }
}
