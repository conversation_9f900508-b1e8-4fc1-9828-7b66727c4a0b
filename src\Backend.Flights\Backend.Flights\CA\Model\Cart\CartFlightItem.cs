using Backend.Flights.Business;
using Newtonsoft.Json;
using ProtoBuf;
using System;

namespace Backend.Flights.CA.Model.Cart
{
    [ProtoContract(SkipConstructor = true), Serializable]
    public class CartFlightItem
    {
        [ProtoMember(1), <PERSON><PERSON><PERSON><PERSON><PERSON>("player")] 
        public string Player { get; set; }

        [ProtoMember(2), <PERSON><PERSON><PERSON>roper<PERSON>("departureLocation")] 
        public Location DepartureLocation { get; set; }

        [ProtoMember(3), <PERSON><PERSON><PERSON><PERSON><PERSON>("arrivalLocation")] 
        public Location ArrivalLocation { get; set; }

        [ProtoMember(4), <PERSON>son<PERSON>roperty("productStartDate")] 
        public DateTime ProductStartDate { get; set; }

        [ProtoMember(5), <PERSON><PERSON><PERSON><PERSON><PERSON>("productEndDate")] 
        public DateTime ProductEndDate { get; set; }

        [ProtoMember(6), <PERSON><PERSON><PERSON><PERSON><PERSON>("packageGroup")] 
        public string PackageGroup { get; set; }

        [ProtoMember(7), <PERSON><PERSON><PERSON><PERSON><PERSON>("paxGroup")] 
        public Pax PaxGroup { get; set; }

        [ProtoMember(8), JsonProperty("parameters")]
        public ParamsInfo Parameters { get; set; }

        [ProtoMember(9), JsonProperty("notify")] 
        public NotifyInfo Notify { get; set; }

        [ProtoMember(10), JsonProperty("fareGroup")] 
        public FareGroupInfo FareGroup { get; set; }

        [ProtoMember(11), JsonProperty("isMulti")] 
        public bool IsMulti { get; set; }

        [ProtoMember(12), JsonProperty("segments")] 
        public Segment[] Segments { get; set; }

        public CartFlightItem(string provider, Location departureLocation, Location arrivalLocation, DateTime productStartDate, DateTime productEndDate, string packageGroup, Pax paxGroup, NotifyInfo notify, FareGroupInfo fareGroup, bool isMulti, Segment[] segments)
        {
            Player = provider;
            DepartureLocation = departureLocation;
            ArrivalLocation = arrivalLocation;
            ProductStartDate = productStartDate;
            ProductEndDate = productEndDate;
            PackageGroup = packageGroup;
            PaxGroup = paxGroup;
            Notify = notify;
            FareGroup = fareGroup;
            IsMulti = isMulti;
            Segments = segments;
        }

        public void SetParamsInfo(UpsellAndCheckoutData.RequestUpsellOrCheckoutDTO request)
        {
            string[] flightNumbers = request.FlightId.Flights ?? (new string[] { });
            string[] OperationalIds = request.FlightId.OperationalId ?? (new string[] { });

            ParamsInfo paramsInfo = new ParamsInfo(flightNumbers, OperationalIds);
            Parameters = paramsInfo;
        }

        [ProtoContract(SkipConstructor = true), Serializable]
        public class Location
        {
            [ProtoMember(1), JsonProperty("iata")] 
            public string IATA { get; set; }

            [ProtoMember(2), JsonProperty("city")] 
            public string City { get; set; }

            [ProtoMember(3), JsonProperty("state")] 
            public string State { get; set; }

            [ProtoMember(4), JsonProperty("country")] 
            public string Country { get; set; }

            public Location(string iata, string city, string state, string country)
            {
                IATA = iata;
                City = city;
                State = state;
                Country = country;
            }
        }

        [ProtoContract(SkipConstructor = true), Serializable]
        public class Pax
        {
            [ProtoMember(1), JsonProperty("adult")] 
            public int Adult { get; set; }

            [ProtoMember(2), JsonProperty("children")] 
            public int Children { get; set; }

            [ProtoMember(3), JsonProperty("babies")] 
            public int Babies { get; set; }

            public Pax(int adult, int children, int babies)
            {
                Adult = adult;
                Children = children;
                Babies = babies;
            }
        }

        [ProtoContract(SkipConstructor = true), Serializable]
        public class ParamsInfo
        {
            [ProtoMember(1), JsonProperty("flights")]
            public string[] Flights { get; set; }

            [ProtoMember(2), JsonProperty("operationalIds")]
            public string[] OperationalIds { get; set; }

            public ParamsInfo(string[] flights, string[] operationalIds)
            {
                Flights = flights;
                OperationalIds = operationalIds;
            }
        }

        [ProtoContract(SkipConstructor = true), Serializable]
        public class NotifyInfo
        {
            [ProtoMember(1), JsonProperty("costPrice")] 
            public decimal? CostPrice { get; set; }

            [ProtoMember(2), JsonProperty("finalPrice")] 
            public decimal? FinalPrice { get; set; }

            public NotifyInfo(decimal? costPrice, decimal? finalPrice)
            {
                CostPrice = costPrice;
                FinalPrice = finalPrice;
            }
        }

        [ProtoContract(SkipConstructor = true), Serializable]
        public class RateTokenInfo
        {
            [ProtoMember(1), JsonProperty("routeRPH")] 
            public int RouteRPH { get; set; }

            [ProtoMember(2), JsonProperty("packageGroup")] 
            public string PackageGroup { get; set; }

            [ProtoMember(3), JsonProperty("rateToken")] 
            public string RateToken { get; set; }

            public RateTokenInfo(int routeRPH, string packageGroup, string rateToken)
            {
                RouteRPH = routeRPH;
                PackageGroup = packageGroup;
                RateToken = rateToken;
            }
        }

        [ProtoContract(SkipConstructor = true), Serializable]
        public class FareGroupInfo
        {
            [ProtoMember(1), JsonProperty("currency")] 
            public string Currency { get; set; }

            [ProtoMember(2), JsonProperty("priceWithTax")] 
            public decimal PriceWithTax { get; set; }

            [ProtoMember(3), JsonProperty("priceWithoutTax")] 
            public decimal PriceWithoutTax { get; set; }

            [ProtoMember(4), JsonProperty("discount")] 
            public decimal? Discount { get; set; }

            [ProtoMember(5), JsonProperty("discountPercentage")] 
            public decimal? DiscountPercentage { get; set; }

            [ProtoMember(6), JsonProperty("refundable")] 
            public bool Refundable { get; set; }

            [ProtoMember(7), JsonProperty("rateTokens")] 
            public RateTokenInfo[] RateTokens { get; set; }

            public FareGroupInfo(string currency, decimal priceWithTax, decimal priceWithoutTax, decimal? discount, decimal? discountPercentage, bool refundable, RateTokenInfo[] rateTokens)
            {
                Currency = currency;
                PriceWithTax = priceWithTax;
                PriceWithoutTax = priceWithoutTax;
                Discount = discount;
                DiscountPercentage = discountPercentage;
                Refundable = refundable;
                RateTokens = rateTokens;
            }
        }

        [ProtoContract(SkipConstructor = true), Serializable]
        public class AirCompany
        {
            [ProtoMember(1), JsonProperty("Name")] 
            public string Name { get; set; }

            [ProtoMember(2), JsonProperty("Iata")] 
            public string Iata { get; set; }

            public AirCompany(string name, string iata)
            {
                Name = name;
                Iata = iata;
            }
        }

        [ProtoContract(SkipConstructor = true), Serializable]
        public class Leg
        {
            [ProtoMember(1), JsonProperty("departure")] 
            public string Departure { get; set; }

            [ProtoMember(2), JsonProperty("departureAirport")] 
            public string DepartureAirport { get; set; }

            [ProtoMember(3), JsonProperty("departureDate")] 
            public DateTime DepartureDate { get; set; }

            [ProtoMember(4), JsonProperty("arrival")] 
            public string Arrival { get; set; }

            [ProtoMember(5), JsonProperty("arrivalAirport")] 
            public string ArrivalAirport { get; set; }

            [ProtoMember(6), JsonProperty("arrivalDate")] 
            public DateTime ArrivalDate { get; set; }

            [ProtoMember(7), JsonProperty("operatedBy")] 
            public AirCompany OperatedBy { get; set; }

            [ProtoMember(8), JsonProperty("operatedByIATA")] 
            public string OperatedByIATA { get; set; }

            [ProtoMember(9), JsonProperty("seatClass")] 
            public string SeatClass { get; set; }

            [ProtoMember(10), JsonProperty("duration")] 
            public int Duration { get; set; }

            [ProtoMember(11), JsonProperty("flightNumber")] 
            public string FlightNumber { get; set; }

            [ProtoMember(12), JsonProperty("managedBy")] 
            public AirCompany ManagedBy { get; set; }

            public Leg(string departure, string departureAirport, DateTime departureDate, string arrival, string arrivalAirport, DateTime arrivalDate, AirCompany operatedBy, string operatedByIATA, string seatClass, int duration, string flightNumber, AirCompany managedBy)
            {
                Departure = departure;
                DepartureAirport = departureAirport;
                DepartureDate = departureDate;
                Arrival = arrival;
                ArrivalAirport = arrivalAirport;
                ArrivalDate = arrivalDate;
                OperatedBy = operatedBy;
                OperatedByIATA = operatedByIATA;
                SeatClass = seatClass;
                Duration = duration;
                FlightNumber = flightNumber;
                ManagedBy = managedBy;
            }
        }

        [ProtoContract(SkipConstructor = true), Serializable]
        public class Bagage
        {
            [ProtoMember(1), JsonProperty("hand")] 
            public int Hand { get; set; }

            [ProtoMember(2), JsonProperty("checked")] 
            public int Checked { get; set; }

            [ProtoMember(3), JsonProperty("handWeight")] 
            public int HandWeight { get; set; }

            [ProtoMember(4), JsonProperty("checkWeight")] 
            public int CheckWeight { get; set; }

            public Bagage(int hand, int @checked, int handWeight, int checkWeight)
            {
                Hand = hand;
                Checked = @checked;
                HandWeight = handWeight;
                CheckWeight = checkWeight;
            }
        }

        [ProtoContract(SkipConstructor = true), Serializable]
        public class Segment
        {
            [ProtoMember(1), JsonProperty("legs")] 
            public Leg[] Legs { get; set; }

            [ProtoMember(2), JsonProperty("baggage")] 
            public Bagage Baggage { get; set; }

            [ProtoMember(3), JsonProperty("totalMinuteDuration")] 
            public int TotalMinuteDuration { get; set; }

            [ProtoMember(4), JsonProperty("numberOfStops")] 
            public int NumberOfStops { get; set; }

            public Segment(Leg[] legs, Bagage baggage, int totalMinuteDuration, int numberOfStops)
            {
                Legs = legs;
                Baggage = baggage;
                TotalMinuteDuration = totalMinuteDuration;
                NumberOfStops = numberOfStops;
            }
        }
    }
}