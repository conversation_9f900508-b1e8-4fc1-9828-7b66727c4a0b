using Backend.Flights.CA.Model.Cart;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace Backend.Flights.CA.Model.Promotions
{
    public class PromotionsResponse
    {
        [JsonProperty("availableItems")]
        public List<PromotionItemResponse> AvailableItems { get; set; }

        [JsonProperty("selectedItems")]
        public List<PromotionItemResponse> SelectedItems { get; set; }
    }

    public class PromotionItemResponse : CartItem
    {
        [JsonProperty("keyRateToken")]
        public string KeyRateToken { get; set; }

        [JsonProperty("promotion")]
        public PromotionInfo Promotion { get; set; }

        [JsonProperty("statements")]
        public List<Statement> Statements { get; set; }


        public PromotionItemResponse(int rph, string rateToken, string keyRateToken, PromotionInfo promotion, List<Statement> statements, int? routeRPH = null)
        : base(rph, rateToken, routeRPH)
        {
            KeyRateToken = keyRateToken;
            Promotion = promotion;
            Statements = statements;
        }
    }

    public class PromotionInfo
    {
        [JsonProperty("priceWithTax")]
        public decimal PriceWithTax { get; set; }

        [JsonProperty("priceWithoutTax")]
        public decimal PriceWithoutTax { get; set; }

        [JsonProperty("pricePerDayWithTax")]
        public decimal PricePerDayWithTax { get; set; }

        [JsonProperty("pricePerDayWithoutTax")]
        public decimal PricePerDayWithoutTax { get; set; }

        [JsonProperty("percentage")]
        public decimal Percentage { get; set; }

        [JsonProperty("discountApplied")]
        public decimal DiscountApplied { get; set; }

        [JsonProperty("promoIds")]
        public List<PromoId> PromoIds { get; set; }

        [JsonProperty("hasCombo")]
        public bool? HasCombo { get; set; }

        public class PromoId
        {
            [JsonProperty("id")]
            public string Id { get; set; }
        }
    }

    public class Statement
    {
        [JsonProperty("code")]
        public string Code { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("label")]
        public string Label { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("discount")]
        public decimal Discount { get; set; }
    }
}