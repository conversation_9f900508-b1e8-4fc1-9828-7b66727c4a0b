namespace Backend.Flights.Models.Search
{
    public class Option
    {
        [ReducedName("fi")]
        public double FirstInstallment { get; set; }
        [ReducedName("i")]
        public double Installment { get; set; }
        [ReducedName("qt")]
        public int Quantity { get; set; }
        [ReducedName("hi")]
        public bool HasInterest { get; set; }
        [ReducedName("cp")]
        public string CardPlan { get; set; }
        [ReducedName("iid")]
        public int InstallmentId { get; set; }
    }
}