﻿using Backend.Flights.CA.Model.Cart;
using Backend.Flights.CA.Model.Opportunities;
using Backend.Flights.CA.UseCase.Promotions;
using Backend.Flights.CA.UseCase.RateToken;
using Backend.Flights.Models.GtwAereo;
using Backend.Flights.Models.Price;
using Backend.Flights.Models.Promotions;
using Backend.Flights.Util;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using BrandContext = Backend.Flights.Models.BrandContext;
using Backend.Flights.CA.Services.Http;
using Backend.Flights.CA.Services.Json;

namespace Backend.Flights.Business
{
    public class CheckoutData
    {
        const string CHECKOUTPREFERENCES = "persistLog,ignoreCachedUnavailableFlights,loadInstallments:true,language:pt_BR,currency:BRL";

        public class RequestData
        {
            public string Provider { get; set; }
            public bool CanDirectToNewCheckout { get; set; }
            public string SearchId { get; set; }
            public string ClientId { get; set; }
            public bool CanDirectToPreCheckout { get; set; }
            public string[] OperationalId { get; set; }
            public string[] Flights { get; set; }
        }

        private readonly ConfigurationManager config;
        private readonly ILogger logger;
        private readonly BrandContext brandCtx;
        private readonly CA.Model.Context.BrandContext _caBrandContext;
        private readonly RequestData request;
        private readonly IRequestPromotions _promotion;
        private readonly List<PromotionItem> _selectedItems;
        private readonly string _gtwUserToken;
        private readonly CA.UseCase.RateToken.GetRateTokenPriceInfo _getRateTokenPriceInfo;
        private readonly IHttpClient _httpClient;
        private readonly IJsonService _json;

        public CheckoutData(RequestData request, ConfigurationManager config, ILogger logger, BrandContext brandCtx,
            IRequestPromotions promotion, List<PromotionItem> selectedItems, string gtwUserToken,
            CA.Model.Context.BrandContext caBrandContext, CA.UseCase.RateToken.GetRateTokenPriceInfo getRateTokenPriceInfo,
            IHttpClient httpClient, IJsonService json)
        {
            this.config = config;
            this.logger = logger;
            this.brandCtx = brandCtx;
            this.request = request;
            _promotion = promotion;
            _selectedItems = selectedItems;
            _gtwUserToken = gtwUserToken;
            _caBrandContext = caBrandContext;
            _getRateTokenPriceInfo = getRateTokenPriceInfo;
            _httpClient = httpClient;
            _json = json;
        }

        async public Task<(long ms, PriceResponse response)[]> CheckAvailability(params string[][] tokens)
        {
            async Task<(long ms, PriceResponse response)> queryGtw(string[] tk)
            {
                var sw = Stopwatch.StartNew();
                var pr = new PriceRequest() { Preferences = CHECKOUTPREFERENCES, Provider = this.request.Provider, Tokens = tk.ToList() };
                var response = await new Pricing(config, brandCtx, logger, _httpClient, _json).Verify(pr);
                sw.Stop();
                return (sw.ElapsedMilliseconds, response);
            }

            var tasks = new Task<(long ms, PriceResponse response)>[tokens.Length];
            for (var i = 0; i < tokens.Length; i++)
                tasks[i] = queryGtw(tokens[i]);

            await Task.WhenAll(tasks);

            return tasks.Select(x => x.Result).ToArray();
        }
        public async Task<object> BuildCheckoutResponse(string searchId, string clientId, long ms, PriceResponse response, object extraParamsToCart = null, UpsellAndCheckoutData.FlightChange FlightChange = null)
        {
            object result;
            if (response.Error != null)
            {
                var error = response.Error;
                if (response.Error.Code != null)
                {
                    error = new Error
                    {
                        Operation = "hasAvail",
                        Code = response.Error.Code,
                        CorrelationId = response.Error.CorrelationId,
                        Message = response.Error.Message
                    };
                }

                result = new { error };
            }
            else
            {
                var sw = Stopwatch.StartNew();
                Pricing.FillPriceGroupInfo(config, response, this.request.Provider, logger);
                await ApplyPromotions(response);
                await UpdateRateTokenProfitSplit(response);
                //TODO: Temporariamente, multidestinos não deve ir para a checkout nova.
                var isMultiDestinations = Availability.GetSearchType(response.PriceGroup.Segments.Select(x => new Models.Search.CityPair { OriginIata = x.Departure, DestinationIata = x.Arrival }).ToList(), brandCtx.isCvc()) == Availability.SearchType.Multidestination;
                var checkoutDataBuilder = selectCheckoutBuilder(isMultiDestinations);

                result = new { CheckoutData = checkoutDataBuilder.Build(response.PriceGroup, extraParamsToCart, FlightChange) };

                sw.Stop();
                ms += sw.ElapsedMilliseconds;
            }
            Pricing.LogPricing(logger, brandCtx.GatewayHeaders.AgentSign, brandCtx.GatewayHeaders.BranchId, response, this.request.Provider, ms, false, brandCtx.isCvc(), searchId, clientId);
            return result;
        }

        private ICheckoutObjectBuilder selectCheckoutBuilder(bool isMultiDestinations)
        {
            ICheckoutObjectBuilder checkoutDataBuilder;
            var agentSign = this.brandCtx.GatewayHeaders.AgentSign;
            if (agentSign == "WEB" || agentSign == "SUB" || agentSign == "MOB" || agentSign == "LOJ")
                checkoutDataBuilder = new PreCheckout(config, logger, brandCtx, isMultiDestinations, _getRateTokenPriceInfo);
            else if (this.request.CanDirectToNewCheckout && !isMultiDestinations)
                checkoutDataBuilder = new NewCheckout(brandCtx);
            else
                checkoutDataBuilder = new OldCheckout();

            return checkoutDataBuilder;
        }

        private async Task ApplyPromotions(PriceResponse response)
        {
            if (_promotion != null && _caBrandContext.PromotionsActive == true && !string.IsNullOrWhiteSpace(_gtwUserToken))
            {

                var promo = await _promotion.Execute(_caBrandContext,
                    _gtwUserToken,
                    new[]
                    {
                        new OpportunitiesRequestItem(
                            0,
                            response.PriceGroup.Segments.First().RateToken,
                            new OpportunitiesRequestItem.OpportunitiesRequestItemParam(request.OperationalId, request.Flights))
                    },
                    (_selectedItems ?? new List<PromotionItem>()).Select((i) => new CartItem(i.RPH, i.RateToken)).ToArray()
                    );

                if (promo?.AvailableItems == null)
                {
                    return;
                }

                foreach (var p in promo.AvailableItems.Where(p => !string.IsNullOrEmpty(p.RateToken)))
                {
                    response.PriceGroup.Segments[p.RPH].RateToken = p.RateToken;
                    //var discount = p?.Promotion?.DiscountApplied ?? 0;
                    //if (discount > 0)
                    //{
                    //    response.PriceGroup.FareGroup.Discount = p?.Promotion?.DiscountApplied;
                    //    response.PriceGroup.FareGroup.DiscountPercentage = p?.Promotion?.Percentage;
                    //}
                    //response.PriceGroup.FareGroup.PriceWithTax -= discount;
                    //response.PriceGroup.FareGroup.PriceWithoutTax -= discount;
                }
            }
        }

        // Atualiza o RateToken com o Profit Split
        private async Task UpdateRateTokenProfitSplit(PriceResponse priceResponse)
        {
            try
            {
                if (_caBrandContext.IsProfitSplit())
                {
                    Console.WriteLine("[UpdateRateTokenProfitSplit] - Atualizando RateToken com Profit Split");
                    var defaultGroup = 1;

                    // Mapeamento dos segmentos para OpportunitiesRequestItem
                    var selectedItems = priceResponse.PriceGroup.Segments.Select((segment, index) =>
                    new CartItem
                    {
                        RPH = index,
                        RateToken = segment.RateToken,
                        Group = defaultGroup.ToString(),
                        RouteRPH = segment.RouteRPH
                    }
                    ).ToArray();

                    var requestParams = new OpportunitiesRequest.RequestParams
                    {
                        ProfileName = "shopping-cart",
                        ShowSteps = false,
                        ShowPayloads = false,
                        ProfitSplit = new OpportunitiesRequest.ProfitSplit
                        {
                            Products = new List<OpportunitiesRequest.ProfitSplitProduct>
                            {
                                new OpportunitiesRequest.ProfitSplitProduct
                                {
                                    Type = "AIR",
                                    Groups = new List<int> { defaultGroup }
                                }
                            }
                        }
                    };

                    var promotionsResponse = await _promotion.Execute(_caBrandContext, _gtwUserToken, null, selectedItems, requestParams, true);

                    if (promotionsResponse != null && promotionsResponse.SelectedItems.Any())
                    {
                        foreach (var segment in priceResponse.PriceGroup.Segments)
                        {
                            var rateToken = promotionsResponse.SelectedItems.FirstOrDefault(x => x.RouteRPH == segment.RouteRPH)?.RateToken;
                            if (rateToken != null)
                            {
                                segment.RateToken = rateToken;
                            }
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                logger.LogInformation($"[UpdateRateTokenProfitSplit] - Erro ao atualizar RateToken com Profit Split: {ex.Message}");
            }
        }


        private interface ICheckoutObjectBuilder { public object Build(PriceGroup priceGroup, object extraParamsToCart = null, UpsellAndCheckoutData.FlightChange flightChange = null); }
        private class OldCheckout : ICheckoutObjectBuilder
        {
            public object Build(PriceGroup priceGroup, object extraParamsToCart = null, UpsellAndCheckoutData.FlightChange flightChange = null)
            {
                var jsonSettings = new JsonSerializerSettings() { ContractResolver = new DefaultContractResolver { NamingStrategy = new CamelCaseNamingStrategy() } };
                var checkoutObject = JsonConvert.SerializeObject(new { preferences = CHECKOUTPREFERENCES, pricingResponse = priceGroup }, jsonSettings);
                return Convert.ToBase64String(Encoding.UTF8.GetBytes(checkoutObject));
            }
        }
        private class NewCheckout : ICheckoutObjectBuilder
        {
            private static readonly Dictionary<string, string> dictPassengerTypes = new Dictionary<string, string> { { "ADT", "adult" }, { "CHD", "child" }, { "INF", "baby" } };
            private readonly BrandContext brandCtx;

            public NewCheckout(BrandContext brandCtx) => this.brandCtx = brandCtx;

            public object Build(PriceGroup priceGroup, object extraParamsToCart = null, UpsellAndCheckoutData.FlightChange flightChange = null)
            {
                var fares = priceGroup.FareGroup.Fares;
                var paxs = fares.SelectMany(x => Enumerable.Repeat(dictPassengerTypes[x.PassengersType], x.PassengersCount)).ToList();
                var grpPax = (from x in paxs
                              group x by x into g
                              select new { type = g.Key, total = g.Count() }).ToDictionary(x => x.type, x => x.total);
                int getPaxTotal(string type) => grpPax.ContainsKey(type) ? grpPax[type] : 0;

                var product = new
                {
                    index = 0,
                    type = "flight",
                    rateTokens = from segment in priceGroup.Segments
                                 select new { routeRPH = segment.RouteRPH, packageGroup = segment.PackageGroup, rateToken = segment.RateToken },
                    ages = from pax in paxs select new { type = pax },
                    ageGroupsCount = new { adults = getPaxTotal("adult"), children = getPaxTotal("child"), babies = getPaxTotal("baby") }
                };
                var headers = brandCtx.GatewayHeaders;
                return new
                {
                    channel = headers.AgentSign,
                    branchId = int.Parse(headers.BranchId),
                    markup = priceGroup.FareGroup.Markup,
                    products = new[] { product }
                };
            }
        }

        internal class PreCheckout : ICheckoutObjectBuilder
        {
            private static readonly Dictionary<string, string> dictPassengerTypes = new Dictionary<string, string> { { "ADT", "adult" }, { "CHD", "child" }, { "INF", "baby" } };
            private readonly ConfigurationManager config;
            private readonly ILogger logger;
            private readonly BrandContext brandCtx;
            private readonly bool isMulti;
            private readonly GetRateTokenPriceInfo _getRateTokenPriceInfo;

            public PreCheckout(ConfigurationManager config, ILogger logger, BrandContext brandCtx, bool isMulti, GetRateTokenPriceInfo getRateTokenPriceInfo)
            {
                this.config = config;
                this.logger = logger;
                this.brandCtx = brandCtx;
                this.isMulti = isMulti;
                _getRateTokenPriceInfo = getRateTokenPriceInfo;
            }

            public object Build(PriceGroup priceGroup, object extraParamsToCart = null, UpsellAndCheckoutData.FlightChange flightChange = null)
            {
                Task<object> buildAsync = Task.Run(async () => await BuildAsync(priceGroup, extraParamsToCart, flightChange));
                buildAsync.Wait();
                return buildAsync.Result;
            }

            private Task<object> BuildAsync(PriceGroup priceGroup, object extraParamsToCart = null, UpsellAndCheckoutData.FlightChange flightChange = null)
            {
                var fares = priceGroup.FareGroup.Fares;
                var fareAdt = fares.FirstOrDefault(f => f.PassengersType == "ADT");
                var finalPrice = fareAdt?.PriceWithTax;
                var paxs = fares
                    .SelectMany(x => Enumerable.Repeat(dictPassengerTypes[x.PassengersType], x.PassengersCount))
                    .ToList();
                var grpPax = (from x in paxs
                              group x by x
                    into g
                              select new { type = g.Key, total = g.Count() }).ToDictionary(x => x.type, x => x.total);
                int getPaxTotal(string type) => grpPax.ContainsKey(type) ? grpPax[type] : 0;

                var items = new List<object>();

                var numberOfSegments = 0;
                priceGroup.Segments.ForEach(seg => { numberOfSegments = Math.Max(numberOfSegments, seg.RouteRPH); });
                numberOfSegments++;

                var departureLocation = createLocation(priceGroup.Segments.First().Legs.First().Departure);
                var arrivalLocation = createLocation(priceGroup.Segments.First().Legs.Last().Arrival);

                if (isMulti)
                {
                    arrivalLocation = createLocation(priceGroup.Segments.Last().Legs.Last().Arrival);
                }

                var tokenPriceInfo = _getRateTokenPriceInfo.FromToken(priceGroup.Segments.First().RateToken);

                object product = new
                {
                    type = "flight",
                    data = new
                    {
                        flightChange,
                        player = priceGroup.Segments.FirstOrDefault()?.Provider,
                        departureLocation = departureLocation,
                        arrivalLocation = arrivalLocation,
                        productStartDate = priceGroup.Segments.First().Legs.First().DepartureDate,
                        productEndDate = priceGroup.Segments.Last().Legs.First().DepartureDate,
                        packageGroup = priceGroup.Segments.First().PackageGroup,
                        paxGroup = new
                        {
                            adult = getPaxTotal("adult"),
                            children = getPaxTotal("child"),
                            babies = getPaxTotal("baby")
                        },
                        parameters = extraParamsToCart,
                        notify = new
                        {
                            costPrice = tokenPriceInfo?.costPrice,
                            finalPrice,
                        },
                        fareGroup = new
                        {
                            currency = priceGroup.FareGroup.Currency,
                            priceWithTax = priceGroup.FareGroup.PriceWithTax,
                            priceWithoutTax = priceGroup.FareGroup.PriceWithoutTax,
                            discount = priceGroup.FareGroup.Discount,
                            discountPercentage = priceGroup.FareGroup.DiscountPercentage,
                            refundable = isRefundable(priceGroup.Segments),
                            rateTokens = from segment in priceGroup.Segments
                                         select new
                                         {
                                             routeRPH = segment.RouteRPH,
                                             packageGroup = segment.PackageGroup,
                                             rateToken = segment.RateToken
                                         },
                        },
                        isMulti = isMulti,
                        segments = from s in priceGroup.Segments select createGroup(s),
                    }
                };

                return Task.FromResult(product);

                /*
                
                Lógica de adição de produto no carrinho redirecionada para o front
                
                items.Add(product);
                object body = new
                {
                    items = items
                };
                 
                JsonSerializerSettings options = new JsonSerializerSettings()
                {
                    NullValueHandling = NullValueHandling.Ignore,
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                };

                var json = JsonConvert.SerializeObject(body, options);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                if (brandCtx.isCvc())
                {
                    content.Headers.Add("gtw-sec-user-token", config.AppSetting["CVC_SEC_USER_TOKEN"]);
                    content.Headers.Add("gtw-business-unit", "CVC");
                }
                else
                {
                    content.Headers.Add("gtw-sec-user-token", config.AppSetting["SUB_SEC_USER_TOKEN"]);
                    content.Headers.Add("gtw-business-unit", "SUB");
                }

                HttpResponseMessage response = await httpClient.PostAsync(config.AppSetting["Cart.Url"], content);
                response.EnsureSuccessStatusCode();
                string responseBody = await response.Content.ReadAsStringAsync();
                var responseJson = JsonConvert.DeserializeObject<CartResponse>(responseBody);
                
                return new
                {
                    id = responseJson._id
                };
                */
            }

            private object createCheckoutLegs(Segment segment)
            {
                var checkoutLegs = new List<object>();
                segment.Legs.ForEach(leg =>
                {
                    object checkoutLeg = new
                    {
                        departure = leg.Departure,
                        departureAirport = getLegLocationDescription(leg.Departure),
                        departureDate = leg.DepartureDate,
                        arrival = leg.Arrival,
                        arrivalAirport = getLegLocationDescription(leg.Arrival),
                        arrivalDate = leg.ArrivalDate,
                        operatedBy = leg.OperatedBy,
                        operatedByIATA = leg.OperatedBy.Iata,
                        seatClass = leg.SeatClass.Description,
                        duration = leg.Duration,
                        flightNumber = leg.FlightNumber,
                        managedBy = leg.ManagedBy,
                        stops = leg?.Stops?.Select(stop => new
                        {
                            stop.Airport,
                            AirportName = getLegLocationDescription(stop.Airport),
                            stop.ArrivalDate,
                            stop.DepartureDate,
                        })
                    };
                    checkoutLegs.Add(checkoutLeg);
                });
                return checkoutLegs;
            }

            private bool isRefundable(List<Segment> segments)
            {
                return segments.All(segment =>
                {
                    return segment?.FareProfile?.Services?
                        .Find(service => service.Type != null && service.Type.Equals("REFUNDABLE"))
                        ?.IsIncluded ?? false;
                });
            }

            private object createGroup(Segment segment)
            {
                return new
                {
                    legs = createCheckoutLegs(segment),
                    baggage = CreateBaggageInfo(segment),
                    baggagesNew = CreateBaggageByPTCInfo(segment.Baggages),
                    totalMinuteDuration = segment.Duration,
                    numberOfStops = segment.NumberOfStops,
                    refundable = segment.Refundable
                };
            }

            private object CreateBaggageInfo(Segment segment)
            {
                try
                {
                    var handBaggage = GetHandBaggageCount(segment.FareProfile.Services);
                    var handWeight = GetHandBaggageWeight(segment.FareProfile.Services);
                    var checkedBaggage = GetCheckedBaggageCount(segment.FareProfile.Baggage);
                    var checkedWeight = GetCheckedBaggageWeight(segment.FareProfile.Baggage);

                    return new
                    {
                        hand = handBaggage,
                        @checked = checkedBaggage,
                        handWeight = handWeight,
                        checkWeight = checkedWeight
                    };
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, ex.GetErrorLocation("Error on CreateBaggageInfo"));
                    throw;
                }
            }

            private object CreateBaggageByPTCInfo(Models.GtwAereo.BaggageByPTC baggageByPTC)
            {
                if (baggageByPTC == null)
                    return null;

                return new
                {
                    adt = baggageByPTC.ADT?.Select(b => MapBaggageDetails(b)) ?? Enumerable.Empty<object>(),
                    chd = baggageByPTC.CHD?.Select(b => MapBaggageDetails(b)) ?? Enumerable.Empty<object>(),
                    inf = baggageByPTC.INF?.Select(b => MapBaggageDetails(b)) ?? Enumerable.Empty<object>()
                };
            }

            private object MapBaggageDetails(Models.GtwAereo.BaggageType baggage)
            {
                if (baggage == null)
                    return null;

                return new
                {
                    type = baggage.Type,
                    count = baggage.Count,
                    weight = baggage.Weight,
                    dimensions = baggage.Dimensions == null ? null : new
                    {
                        length = baggage.Dimensions.Length,
                        width = baggage.Dimensions.Width,
                        depth = baggage.Dimensions.Depth
                    },
                    linearSize = baggage.LinearSize
                };
            }


            private int GetHandBaggageCount(IEnumerable<FareProfileService> services)
            {
                if (services == null)
                    return 0;
                return services.Any(s => s.Type == "HAND_LUGGAGE" && s.IsIncluded) ? 1 : 0;
            }

            private int GetCheckedBaggageCount(Baggage baggage)
            {
                return decimal.ToInt32(baggage?.Quantity ?? 0);
            }

            private int GetHandBaggageWeight(IEnumerable<FareProfileService> services)
            {
                if (services == null)
                    return 0;

                var handLuggageService = services.FirstOrDefault(s => s.Type == "HAND_LUGGAGE");

                if (handLuggageService == null || !handLuggageService.IsIncluded)
                    return 0;

                return Services.HandBaggageWeightService.parse(handLuggageService.Description);
            }

            private int GetCheckedBaggageWeight(Baggage baggage)
            {
                return decimal.ToInt32(baggage?.Weight ?? 0);
            }

            public static object createLocation(string iata, ConfigurationManager config, ILogger logger)
            {
                var location = new AirLocations(config).LoadIata(iata, logger);
                return new
                {
                    IATA = location.Iata,
                    city = location.City,
                    state = location.State,
                    country = location.Country
                };
            }

            private object createLocation(string iata)
            {
                return createLocation(iata, config, logger);
            }

            private string getLegLocationDescription(string iata)
            {
                var location = new AirLocations(config).LoadIata(iata, logger);
                return location.Description;
            }
        }

        public class CartResponse
        {
            public string _id { get; set; }
        }


    }
}