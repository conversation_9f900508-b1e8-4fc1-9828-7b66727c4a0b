﻿using System;
using System.Collections.Generic;

namespace Backend.Flights.Models.GtwAereo
{
    public class Flight
    {
        public Flight()
        {
            //this is just a unique identifier, valid to one specific search, in order to create links between pricegroups to generate upsell data.
            // Só gera novo UID se não existir um (para evitar regeneração durante deserialização)
            if (string.IsNullOrEmpty(this.UID))
            {
                this.UID = Convert.ToBase64String(Guid.NewGuid().ToByteArray()).Substring(0, 22);
            }
        }

        public string UID { get; set; } // Mudou de private set para set para permitir deserialização
        public AirCompany ValidatingBy { get; set; }
        public FareGroup FareGroup { get; set; }
        public List<Segment> Segments { get; set; }

        public Installments Installments { get; set; }

        public string highlight { get; set; }
    }
}
