apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  name: sub-backend-flights-hpa
  namespace: sub-flights
  labels:
    app: sub-backend-flights
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: sub-backend-flights-deploy
  minReplicas: 40
  maxReplicas: 80
  metrics:
  - type: Resource
    resource:
      name: cpu
      targetAverageUtilization: 120
  - type: Resource
    resource:
      name: memory
      targetAverageUtilization: 300
