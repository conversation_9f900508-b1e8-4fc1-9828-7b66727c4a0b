﻿using Backend.Flights.CA.Services.Cache;
using Backend.Flights.CA.UseCase.AirLocation;
using Backend.Flights.CA.UseCase.Checkout;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Backend.Flights.CA.UseCase.Upsell
{
    public class GetUpsellOrCheckout
    {
        public enum Error
        {
            CacheNotFound,
            BadSegment
        }

        private readonly ICacheService _cacheService;
        private readonly DesserializeCheckoutCache _desserializeCheckoutCache;
        private readonly SearchLocationByIata _searchLocationByIata;

        public GetUpsellOrCheckout(ICacheService cacheService, DesserializeCheckoutCache desserializeCheckoutCache, SearchLocationByIata searchLocationByIata)
        {
            _cacheService = cacheService;
            _desserializeCheckoutCache = desserializeCheckoutCache;
            _searchLocationByIata = searchLocationByIata;
        }

        public async Task<(Error? error, Model.Cache.CartCheckout.UpsellOption[] upsells, Model.Cart.CartFlightItem cartItem)> Execute(string id, int[][] segments, bool offerUpsell)
        {
            var flightCache = await _cacheService.GetItem($"f/{id}");
            if (flightCache == null)
            {
                return (Error.CacheNotFound, null, null);
            }

            var cacheData = _desserializeCheckoutCache.Execute(flightCache);

            if (offerUpsell && cacheData.Upsell?.Length > 0)
            {
                return (null, cacheData.Upsell, null);
            }

            var selectedSegments = segments.Select(segmentIndexes => {              
                var tokensFound = cacheData.Options.Where(s=> {
                    if(segmentIndexes[2] > 0)
                    {
                        var key = $"{segmentIndexes[0]}-{segmentIndexes[2]}";
                        return key.Equals($"{s.Key.Substring(0,1)}-{s.Key.Substring(4)}");
                    }
                    else
                    {
                        var key = $"{segmentIndexes[0]}-{segmentIndexes[1]}";
                        return key.Equals($"{s.Key.Substring(0,1)}-{s.Key.Substring(2,1)}");
                    }
                }).ToList();

                return tokensFound.Count > 0 ? cacheData.Options.GetValueOrDefault(tokensFound.Select(s=> s.Key).ToArray().First()) : null;
                
                })
                .ToArray();

            if (selectedSegments.Length < 1 || selectedSegments.Any(segment => segment == null))
            {
                return (Error.BadSegment, null, null);
            }

            var computedSegments = await Task.WhenAll(cacheData.CartItem.Segments.Select(async (segment, idx) =>
            {
                segment.Legs = await Task.WhenAll(selectedSegments[idx].Segment.Legs.Select(async leg =>
                {
                    var airports = await Task.WhenAll(
                        _searchLocationByIata.Execute(leg.Departure),
                        _searchLocationByIata.Execute(leg.Arrival)
                    );

                    leg.DepartureAirport = airports[0].Description;
                    leg.ArrivalAirport = airports[1].Description;
                    leg.OperatedByIATA = leg.OperatedBy.Iata;
                    return leg;
                }));
                return segment;
            }).ToArray());

            cacheData.CartItem.FareGroup.RateTokens = selectedSegments.Select((segment, idx) =>
                new Model.Cart.CartFlightItem.RateTokenInfo(segments[idx][0], segment.PackageGroup, segment.Token)
            ).ToArray();
            cacheData.CartItem.Segments = computedSegments;

            return (null, null, cacheData.CartItem);
        }
    }
}
