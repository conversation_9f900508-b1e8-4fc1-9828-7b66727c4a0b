﻿using Newtonsoft.Json;
using ProtoBuf;
using System;
using System.Linq;

namespace Backend.Flights.CA.Model.Search.Result
{
    public class SearchResultFlight
    {
        private static readonly string[] feeCodes = new string[4] { "DU", "RAV", "CFEE", "TB" };

        public ResultAirCompany ValidatingBy;
        public ResultFareGroup FareGroup;
        public ResultSegment[] Segments;
        public string provider;

        public bool IsSameFlights(SearchResultFlight otherFlight)
        {
            return Segments.Length == otherFlight.Segments.Length &&
                Segments.Select((item, idx) => (item, idx)).All(segmentInfo =>
                {
                    var (segment, segmentIdx) = segmentInfo;
                    var otherSegment = otherFlight.Segments[segmentIdx];

                    return segment.Legs.Length == otherSegment.Legs.Length &&
                        segment.Legs.Select((item, idx) => (item, idx)).All(legInfo =>
                        {
                            var (leg, legIndex) = legInfo;
                            var otherLeg = otherSegment.Legs[legIndex];
                            return leg.ManagedBy.Iata == otherLeg.ManagedBy.Iata &&
                                leg.FlightNumber == otherLeg.FlightNumber;
                        });
                });
        }

        public bool HasDifferentServices(SearchResultFlight possibleUpsell)
        {
            return Segments[0].FareProfile.HasDifferentServices(possibleUpsell.Segments[0].FareProfile);
        }

        public bool IsUpssel(SearchResultFlight possibleUpsell)
        {
            return FareGroup.PriceWithTax < possibleUpsell.FareGroup.PriceWithTax && HasDifferentServices(possibleUpsell);

        }

        public (decimal tax, decimal fee) GetTaxAndFee()
        {
            decimal tax = 0, fee = 0;

            FareGroup.Fares.Where(f => f.Taxes?.Count() > 0)
                .SelectMany(f => f.Taxes.Select(t => (isFee: feeCodes.Contains(t.Code), value: t.Values.Sum(v => v.Amount))))
                .ToList().ForEach((t) =>
                {
                    if (t.isFee)
                    {
                        fee += t.value;
                    }
                    else
                    {
                        tax += t.value;
                    }
                });

            return (tax, fee);
        }

        public class ResultAirCompany
        {
            public string Name { get; set; }
            public string Iata { get; set; }
        }

        public class ResultTaxValues
        {
            public string Currency { get; set; }
            public decimal Amount { get; set; }
        }

        public class ResultTaxDetails
        {
            public string Code { get; set; }
            public string Description { get; set; }
            public decimal Amount { get; set; }
        }

        public class ResultTax
        {
            public string Code { get; set; }
            public string Description { get; set; }
            public ResultTaxValues[] Values { get; set; }
            public ResultTaxDetails[] Details { get; set; }
        }

        public class ResultOriginalPriceInfo
        {
            public string Currency { get; set; }
            public decimal ExchangeRate { get; set; }
            public decimal BaseFare { get; set; }
        }

        public class ResultFare
        {
            public string PassengersType;
            public int PassengersCount;
            public decimal PriceWithTax;
            public decimal PriceWithoutTax;
            public ResultTax[] Taxes;
            public ResultOriginalPriceInfo OriginalPriceInfo;

            public (decimal tax, decimal fee) GetTaxAndFee()
            {
                decimal tax = 0, fee = 0;

                if (Taxes == null)
                    return (tax, fee);

                Taxes
                    .Select(t => (isFee: feeCodes.Contains(t.Code), value: t.Values.Sum(v => v.Amount)))
                    .ToList().ForEach((t) =>
                    {
                        if (t.isFee)
                        {
                            fee += t.value;
                        }
                        else
                        {
                            tax += t.value;
                        }
                    });

                return (tax, fee);
            }
        }

        public class ResultFareGroup
        {
            public string ReCharging { get; set; }
            public string Currency { get; set; }
            public decimal? Discount { get; set; }
            public decimal? DiscountPercentage { get; set; }
            public decimal PriceWithTax { get; set; }
            public decimal PriceWithoutTax { get; set; }
            public decimal Markup { get; set; }
            public ResultFare[] Fares { get; set; }
            public ResultOriginalPriceInfo OriginalPriceInfo { get; set; }
        }

        [ProtoContract(SkipConstructor = true), Serializable]
        public class ResultBaggage
        {
            [ProtoMember(1), JsonProperty("type")]
            public string Type { get; set; }

            [ProtoMember(2), JsonProperty("isIncluded")]
            public bool IsIncluded { get; set; }

            [ProtoMember(3), JsonProperty("quantity")]
            public decimal Quantity { get; set; }

            [ProtoMember(4), JsonProperty("uom")]
            public string Uom { get; set; }

            [ProtoMember(5), JsonProperty("weight")]
            public decimal Weight { get; set; }
        }

        [ProtoContract(SkipConstructor = true), Serializable]
        public class ResultFareProfileService
        {
            [ProtoMember(1), JsonProperty("type")]
            public string Type { get; set; }

            [ProtoMember(2), JsonProperty("isIncluded")]
            public bool IsIncluded { get; set; }

            [ProtoMember(3), JsonProperty("description")]
            public string Description { get; set; }
        }

        [ProtoContract(SkipConstructor = true), Serializable]
        public class ResultFareProfile
        {
            [ProtoMember(1), JsonProperty("baggage")]
            public ResultBaggage Baggage { get; set; }

            [ProtoMember(2), JsonProperty("services")]
            public ResultFareProfileService[] Services { get; set; }

            [ProtoMember(3), JsonProperty("fareFamily")]
            public string FareFamily { get; set; }

            public bool HasDifferentServices(ResultFareProfile other)
            {
                return FareFamily != other.FareFamily || Baggage != other.Baggage || Services.Length != other.Services.Length || !Enumerable.SequenceEqual(Services, other.Services);
            }
        }

        public class ResultStop
        {
            public string Airport { get; set; }
            public DateTime DepartureDate { get; set; }
            public DateTime ArrivalDate { get; set; }
        }

        public class ResultSeatClass
        {
            public string Code { get; set; }
            public string Description { get; set; }
        }

        public class ResultLeg
        {
            public ResultAirCompany ManagedBy { get; set; }
            public ResultAirCompany OperatedBy { get; set; }
            public string FlightNumber { get; set; }
            public string Departure { get; set; }
            public string Arrival { get; set; }
            public string DepartureDescription { get; set; }
            public string ArrivalDescription { get; set; }
            public int DepartureCode { get; set; }
            public int ArrivalCode { get; set; }
            public int NumberOfStops { get; set; }
            public ResultStop[] Stops { get; set; }
            public DateTime DepartureDate { get; set; }
            public DateTime ArrivalDate { get; set; }
            public int Duration { get; set; }
            public string AircraftCode { get; set; }
            public string FareBasis { get; set; }
            public string FareClass { get; set; }
            public ResultSeatClass? SeatClass { get; set; }
            public string SeatsLeft { get; set; }
        }

        public class ResultSegment
        {
            public string Departure { get; set; }
            public string Arrival { get; set; }
            public DateTime DepartureDate { get; set; }
            public DateTime ArrivalDate { get; set; }
            public string RateToken { get; set; }
            public string PackageGroup { get; set; }
            public int RouteRPH { get; set; }
            public int NumberOfStops { get; set; }
            public string FareType { get; set; }
            public int Duration { get; set; }
            public ResultFareProfile FareProfile { get; set; }
            public ResultLeg[] Legs { get; set; }
            public string Provider { get; set; }
            public bool IsInternational { get; set; }
            public bool Refundable { get; set; }
            public string OperationalId { get; set; }
        }
    }
}
