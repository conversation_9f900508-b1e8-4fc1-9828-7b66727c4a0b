﻿using Backend.Flights.CA.Services.Http;
using Backend.Flights.CA.Services.Json;
using Backend.Flights.Models;
using Backend.Flights.Models.Markdown;
using Backend.Flights.Models.Markdown.ImportaLoc;
using Backend.Flights.Util;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace Backend.Flights.Services
{
    public class MarkdownService
    {
        private readonly ILogger _logger;
        private readonly BrandContext brandCtx;
        private readonly IHttpClient _httpClient;
        private readonly IJsonService _json;

        public MarkdownService(ILogger logger, BrandContext brandCtx, IHttpClient httpClient, IJsonService json)
        {
            _logger = logger;
            this.brandCtx = brandCtx;
            _httpClient = httpClient;
            _json = json;
        }

        private async Task<K> callHttp<T, K>(string url, Action<HttpRequestMessage> fnPreCall, Func<T, string, long, HttpResponseMessage, K> fnResult) where T : class
        {
            var time = Stopwatch.StartNew();
            string jsonResp = string.Empty;
            try
            {
                var requestMessage = new HttpRequestMessage(HttpMethod.Get, url);
                fnPreCall(requestMessage);

                requestMessage.Headers.Add("Accept-Encoding", "gzip");

                var headers = HttpRequestExtensions.GetHeadersHttpRequestMessage(requestMessage);
                var (responsePayload, httpError, httpResponse) = await _httpClient.GetString(url, headers, timeout: 35);

                time.Stop();

                if (httpError != null)
                {
                    _logger.LogError("failed to get data from MarkdownService.callHttp: http error: {httpError}. message: {message}. body: {body}",
                        httpError.HttpErrorCode,
                        httpError.Message,
                        httpError.Body);
                }

                var retorno = JsonConvert.DeserializeObject<T>(responsePayload);

                return fnResult(retorno, responsePayload, time.ElapsedMilliseconds, httpResponse);
            }
            catch (Exception e)
            {
                time.Stop();
                return fnResult(null, string.Concat(e.Message, " - ", jsonResp), time.ElapsedMilliseconds, null);
            }
        }

        public async Task<CredipaxResponse> GetCredipax(string cpf, string locator, int reservation)
        {
            var transactionId = Guid.NewGuid().ToString();
            var url = $"{brandCtx.CredipaxData.Url}?p_cpf={string.Join("", cpf.Where(x => char.IsDigit(x)))}&p_cd_filial={brandCtx.GatewayHeaders.BranchId}&p_localizador={locator}&p_cd_reserva={(reservation > 0 ? reservation.ToString() : "")}";

            return await callHttp<CredipaxResponse, CredipaxResponse>(url, req =>
            {
                req.Headers.Add("gtw-sec-user-token", brandCtx.CredipaxData.UserToken);
                req.Headers.Add("gtw-transaction-id", transactionId);
            }, (obj, respTxt, duration, resp) =>
            {
                logGetCredipax(cpf, locator, reservation, duration, respTxt, obj == null ? "error" : resp.IsSuccessStatusCode ? "success" : "error", url, transactionId);
                return obj ?? new CredipaxResponse { Status = "Error", Message = "Localizador não foi encontrado para o cpf informado." };
            });
        }
        private void logGetCredipax(string cpf, string locator, int reservation, long duration, string message, string status, string url, string transactionId)
        {
            _logger.BeginScope(new Dictionary<string, object>
            {
                ["app_name"] = "sub-backend-flights",
                ["duration"] = duration,
                ["operation"] = "GETCREDIPAX",
                ["cpf"] = cpf,
                ["locator"] = locator,
                ["reservation"] = reservation,
                ["short_message"] = status,
                ["url"] = url,
                ["transaction-id"] = transactionId
            });
            _logger.LogInformation(message);
        }

        public async Task<bool> CheckMarkdownAvailability(string locatorCode, string provider)
        {
            var transactionId = Guid.NewGuid().ToString();
            var url = $"{brandCtx.CredipaxData.CheckAvailability.Url}/checkList/?gatewayOrigin=VCRA&codigoEmpresa=1&importacao=R&codigoFilial={brandCtx.GatewayHeaders.BranchId}&localizador={locatorCode}&source={provider}";

            return await callHttp<CheckMarkdownAvailabilityResponse, bool>(url, req =>
            {
                req.Headers.Add("gtw-sec-user-token", brandCtx.CredipaxData.CheckAvailability.UserToken);
                req.Headers.Add("gtw-transaction-id", transactionId);
            }, (obj, respTxt, duration, resp) =>
            {
                logCheckMarkdownAvailability(locatorCode, duration, provider, obj == null ? "error" : "success", url, respTxt, transactionId);

                var isStatusValid = int.TryParse(obj?.status?.ToString() ?? "", out int status);
                return isStatusValid && (status == 0 || status == 1) && obj.checkList.All(x => x.Value);
            });
        }
        private void logCheckMarkdownAvailability(string locatorCode, long duration, string provider, string status, string url, string message, string transactionId)
        {
            _logger.BeginScope(new Dictionary<string, object>
            {
                ["app_name"] = "sub-backend-flights",
                ["duration"] = duration,
                ["locatorCode"] = locatorCode,
                ["operation"] = "CHECKMARKDOWNAVAILABILITY",
                ["provider"] = provider,
                ["short_message"] = status,
                ["url"] = url,
                ["transaction-id"] = transactionId
            });
            _logger.LogInformation(message);
        }

        public async Task<ImportaLocResponse> GetImportaLoc(string locatorCode, string provider)
        {
            var url = $"{brandCtx.GatewayUrl}/flights/bookings/{provider}/{locatorCode}?packageGroup=STANDALONE&preferences=showTicketsInfo";

            return await callHttp<ImportaLocResponse, ImportaLocResponse>(url, req => brandCtx.GatewayHeaders.SetHeaders(req.Headers),
                (obj, respTxt, duration, resp) =>
                {
                    logGetImportaLoc(locatorCode, duration, provider, obj == null ? "error" : "success", url, respTxt);
                    return obj ?? new ImportaLocResponse();
                });
        }
        private void logGetImportaLoc(string locatorCode, long duration, string provider, string status, string url, string message)
        {
            _logger.BeginScope(new Dictionary<string, object>
            {
                ["app_name"] = "sub-backend-flights",
                ["duration"] = duration,
                ["locatorCode"] = locatorCode,
                ["operation"] = "GETIMPORTALOC",
                ["provider"] = provider,
                ["short_message"] = status,
                ["url"] = url,
            });
            _logger.LogInformation(message);
        }

        private Dictionary<string, string> BuildEstimatesHeadersParams(EstimatesRequest request)
        {
            return new Dictionary<string, string>
            {
                { "Gtw-Sec-User-Token", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************.CzYWC8aB-zakIfpbB6V-VQ2yhk_Dak7S0jwE5nW4xaw" },
                { "Gtw-Booking-Token", request.BookingToken },
                { "Old-Gtw-Tokenized-Rate-Tokens", request.OldRateToken },
                { "New-Gtw-Tokenized-Rate-Tokens", request.NewRateToken },
                { "New-Gtw-Pax-Count", request.NewPaxCount },
                { "Accept-Encoding", "gzip" }
            };
        }
        public async Task<EstimatesResponse> GetEstimates(EstimatesRequest request)
        {
            var time = new Stopwatch();
            var url = string.Concat($"https://askl22ffzc.execute-api.sa-east-1.amazonaws.com/b2b_prd/flights/estimate?preferences=language:pt_BR,persistLog=true,ignoreCachedUnavailableFlights,loadInstallments:true,showPlayer=true,currency:BRL,showAssociatedFares");
            try
            {
                var retorno = new EstimatesResponse();

                var reqPayload = new HttpRequestMessage(HttpMethod.Get, url);
                var headersParam = BuildEstimatesHeadersParams(request);
                foreach (var h in headersParam)
                    reqPayload.Headers.Add(h.Key, h.Value);

                var headers = HttpRequestExtensions.GetHeadersHttpRequestMessage(reqPayload);
                var (responsePayload, httpError, httpResponse) = await _httpClient.GetString(url, headers, timeout: 35);

                if (httpError != null)
                {
                    _logger.LogError("failed to get data from MarkdownService.GetEstimates: http error: {httpError}. message: {message}. body: {body}",
                        httpError.HttpErrorCode,
                        httpError.Message,
                        httpError.Body);
                }

                retorno = JsonConvert.DeserializeObject<EstimatesResponse>(responsePayload);

                time.Stop();

                logGetEstimates(request.BookingToken, request.OldRateToken, request.NewRateToken, request.NewPaxCount, time.ElapsedMilliseconds, "sucess", url, "Sucesso ao recalcular Estimates");
                return retorno;
            }
            catch (Exception e)
            {
                time.Stop();
                logGetEstimates(request.BookingToken, request.OldRateToken, request.NewRateToken, request.NewPaxCount, time.ElapsedMilliseconds, "error", url, e.Message);
                return new EstimatesResponse();
            }
        }
        private async void logGetEstimates(string bookingToken, string oldToken, string newToken, string paxCount, long duration, string status, string url, string message)
        {
            await Task.Run(() =>
            {
                _logger.BeginScope(new Dictionary<string, object>
                {
                    ["app_name"] = "sub-backend-flights",
                    ["duration"] = duration,
                    ["bookingToken"] = bookingToken,
                    ["operation"] = "GETESTIMATES",
                    ["oldToken"] = oldToken,
                    ["newToken"] = newToken,
                    ["paxCount"] = paxCount,
                    ["short_message"] = status,
                    ["message"] = message,
                    ["url"] = url,
                });
                _logger.LogInformation(message);
            });
        }
    }
}