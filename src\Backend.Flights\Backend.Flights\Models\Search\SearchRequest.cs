﻿using Newtonsoft.Json;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace Backend.Flights.Models.Search
{
    public class SearchRequest
    {
        public SearchRequest()
        {
            CityPairs = new List<CityPair>();
        }

        [Required]
        public List<CityPair> CityPairs { get; set; }

        [Required]
        public int Adults { get; set; }

        [JsonProperty("Children")]
        public int? Children { get; set; }

        [JsonProperty("Infants")]
        public int? Infants { get; set; }

        public bool DirectFlight { get; set; }

        public string Cabin { get; set; }

        public bool? Baggage { get; set; }

        public bool? Package { get; set; }

        private string _packageGroup;
        public string PackageGroup
        {
            get
            {
                if (string.IsNullOrWhiteSpace(_packageGroup))
                    return _packageGroup;

                return _packageGroup.ToUpperInvariant() switch
                {
                    "PACKAGE" => "PACKAGE,PKGCHARTER",
                    "STANDALONE" => "STANDALONE,CHARTER",
                    _ => _packageGroup
                };
            }
            set
            {
                _packageGroup = value;
            }
        }

        public string UserAgent { get; set; }

        public string SearchId { get; set; }

        public string ClientId { get; set; }

        public string GtwUserToken { get; set; }
        public List<string> Sources { get; set; } // Lista de sources opcionais para filtro
        public List<string> AirCompanies { get; set; } // Lista de cias opcionais para filtro

        public override string ToString()
            => $"Adults: {Adults}, Children: {Children ?? 0}, Infants: {Infants ?? 0}, Segments: {string.Join(", ", CityPairs.Select(x => $"{x.OriginIata}-{x.DestinationIata}"))}";
    }

    public class CityPair
    {
        [Required]
        public string OriginIata { get; set; }

        [Required]
        public string DestinationIata { get; set; }

        [Required]
        public DateValues DepartureDate { get; set; }

        public string FlightNumber { get; set; }

    }

    public struct DateValues
    {
        [Required]
        public int Day { get; set; }

        [Required]
        public int Month { get; set; }

        [Required]
        public int Year { get; set; }

        public override string ToString() => $"{this.Year}-{this.Month.ToString().PadLeft(2, '0')}-{this.Day.ToString().PadLeft(2, '0')}";
    }
}
