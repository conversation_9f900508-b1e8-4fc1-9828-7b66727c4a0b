using Newtonsoft.Json;
using System.Collections.Generic;

namespace Backend.Flights.CA.Model.Promotions
{
    public class PromotionsCachedFareGroupResponse
    {
        [JsonProperty("promotion")]
        public PromotionInfo Promotion { get; set; }
        [JsonProperty("statements")]
        public List<Statement> Statements { get; set; }
        [JsonProperty("flightId")]
        public string FlightId { get; set; }
        [JsonProperty("rateToken")]
        public string RateToken { get; set; }
        [JsonProperty("identificationKey")]
        public string IdentificationKey { get; set; }
        [JsonIgnore]
        public int RPH { get; set; } // Utilizado apenas para criar uma chave única para o objeto e vinculo com a promoção
    }
}