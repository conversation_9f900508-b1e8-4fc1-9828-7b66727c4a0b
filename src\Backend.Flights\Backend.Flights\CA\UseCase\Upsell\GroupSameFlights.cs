﻿using Backend.Flights.CA.Model.Search.Result;
using System.Collections.Generic;
using System.Linq;

namespace Backend.Flights.CA.UseCase.Upsell
{
    public class GroupSameFlights
    {
        private class SortResultByPrice : IComparer<string>
        {
            private readonly Dictionary<string, SearchResultFlight> _recomendations;

            public SortResultByPrice(Dictionary<string, SearchResultFlight> recomendations)
            {
                _recomendations = recomendations;
            }

            public int Compare(string x, string y)
            {
                return _recomendations[x].FareGroup.PriceWithTax.CompareTo(
                    _recomendations[y].FareGroup.PriceWithTax);
            }
        }

        public List<List<string>> Execute(Dictionary<string, SearchResultFlight> recomendations)
        {
            var toValidate = recomendations.Keys.ToList();
            var groups = new List<List<string>>();

            var comparer = new SortResultByPrice(recomendations);

            while (toValidate.Any())
            {
                var curKey = toValidate[0];
                toValidate.Remove(curKey);

                var curFlight = recomendations[curKey];

                var curGroup = toValidate
                    .AsParallel()
                    .Where(keyToValidate => curFlight.IsSameFlights(recomendations[keyToValidate]))
                    .ToList();

                curGroup.ForEach(key => toValidate.Remove(key));

                curGroup.Add(curKey);

                groups.Add(curGroup);
            }

            groups.AsParallel().ForAll(group => group.Sort(comparer));

            return groups;
        }
    }
}
