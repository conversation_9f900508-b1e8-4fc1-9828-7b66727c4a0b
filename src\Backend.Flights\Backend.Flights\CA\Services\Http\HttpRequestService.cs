using Microsoft.AspNetCore.Http;
using System;

namespace Backend.Flights.CA.Services.Http
{
    public class HttpRequestService : IHttpRequestService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public string GetHost() => _httpContextAccessor.HttpContext.Request.Host.Host;

        public HttpRequestService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public string GetHeader(string header)
        {
            var headers = _httpContextAccessor.HttpContext.Request.Headers;

            if (!headers.ContainsKey(header))
            {
                return null;
            }

            var val = string.Join('|', headers[header].ToArray() ?? Array.Empty<string>());

            return string.IsNullOrWhiteSpace(val) ? null : val;
        }

        public string GetCookie(string cookie)
        {
            var cookies = _httpContextAccessor.HttpContext.Request.Cookies;

            if (!cookies.ContainsKey(cookie))
            {
                return null;
            }

            var val = cookies[cookie];

            return string.IsNullOrWhiteSpace(val) ? null : val;
        }
    }
}