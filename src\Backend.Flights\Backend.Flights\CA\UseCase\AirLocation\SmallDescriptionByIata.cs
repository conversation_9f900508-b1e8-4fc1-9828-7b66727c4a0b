using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Backend.Flights.CA.Services.Cache;

namespace Backend.Flights.CA.UseCase.AirLocation
{
    public class SmallDescriptionByIata
    {
        private static readonly Regex _extractSmallDescription = new Regex(
            @"^(?:Aeroporto\s+(?:Internacional\s+)?)?(?<d>.+?,.+?),",
            RegexOptions.Compiled | RegexOptions.Singleline | RegexOptions.CultureInvariant |
            RegexOptions.IgnoreCase);

        private readonly SearchLocationByIata _searchLocationByIata;
        private readonly ILocalCacheService _localCacheService;

        public SmallDescriptionByIata(
            SearchLocationByIata searchLocationByIata,
            ILocalCacheService localCacheService)
        {
            _searchLocationByIata = searchLocationByIata;
            _localCacheService = localCacheService;
        }

        public async Task<string> Execute(string iata)
        {
            var key = $"s/i/{iata}";

            var value = await _localCacheService.Get<string>(key);

            if (value != null)
            {
                return value;
            }

            var data = await _searchLocationByIata.Execute(iata);
            if (data == null)
            {
                return null;
            }

            var m = _extractSmallDescription.Match(data.Description);
            value = m.Success ? m.Groups["d"].Value : data.Description;

            await _localCacheService.Set(key, value);
            return value;
        }
    }
}