﻿using Newtonsoft.Json;

namespace Backend.Flights.CA.Model.RateToken
{
    public class FarePaxToken
    {
        [JsonProperty("a")] public string FaixaEtaria;
        [JsonProperty("c")] public decimal Tarifa;
        [JsonProperty("d")] public decimal TaxaEmbarque;
        [JsonProperty("e")] public decimal Du;
        [JsonProperty("h")] public decimal ObFee;
        [JsonProperty("i")] public decimal Rav;
        [JsonProperty("j")] public decimal TaxaBolsaRc;
        [JsonProperty("k")] public decimal CFee;

        public decimal CostPrice => Tarifa + TaxaEmbarque + Du;
        public decimal FinalPrice => CostPrice + ObFee + Rav + TaxaBolsaRc + CFee;
    }
}