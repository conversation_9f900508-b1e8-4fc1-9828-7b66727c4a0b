using Backend.Flights.CA.Services.ContentProvider;
using System.Collections.Generic;
using System.Linq;

namespace Backend.Flights.CA.UseCase.ContentProvider
{
    public class ContentProviderFilterSources
    {
        private readonly string[] _gds =
        {
            IContentProvider.SOURCE_SABRE,
            IContentProvider.SOURCE_AMADEUS,
            IContentProvider.SOURCE_GALILEU
        };

        public IEnumerable<string> Execute(string[] sources)
        {
            var filtered = sources.Where(s => !_gds.Contains(s)).ToArray();

            return filtered.Length == sources.Length ? sources : filtered.Append(IContentProvider.SOURCE_GDS);
        }
    }
}