using Backend.Flights.Models;
using System;
using System.Collections.Generic;

namespace Backend.Flights.CA.Model.Context
{
    public class BrandContext
    {
        public string UserToken;
        public bool? PromotionsActive = null;
        public User User = null;
        public int? BranchId = null;
        public ContentProviderContext ContentProvider = null;
        public string GatewayUrl = "";
        public Dictionary<string, string> GatewayHeaders = null;

        private const string HEADER_USERNAME = "Gtw-Username";
        private const string USERNAME_CVC = "scvc";
        private const string HEADER_GTW_PRICING = "Gtw-Pricing";

        public bool IsCvc()
        {
            string username;
            var resultGet = GatewayHeaders.TryGetValue(HEADER_USERNAME, out username);
            return resultGet && username == USERNAME_CVC;
        }

        public bool IsProfitSplit()
        {
            return GatewayHeaders.TryGetValue(HEADER_GTW_PRICING, out var pricing) &&
                   string.Equals(pricing, "PF", StringComparison.OrdinalIgnoreCase);
        }
    }
}
