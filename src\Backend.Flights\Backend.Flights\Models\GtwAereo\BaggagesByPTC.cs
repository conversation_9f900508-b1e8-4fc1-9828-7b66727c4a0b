using System.Collections.Generic;
using System.Linq;

namespace Backend.Flights.Models.GtwAereo
{
    public class BaggageByPTC
    {
        public List<BaggageType> ADT { get; set; } // Bagagens para adultos
        public List<BaggageType> CHD { get; set; } // Bagagens para crianças
        public List<BaggageType> INF { get; set; } // Bagagens para bebês

        public bool HasBaggage()
        {
            return (ADT != null && ADT.Any()) || (CHD != null && CHD.Any()) || (INF != null && INF.Any());
        }
    }

    public class BaggageType
    {
        public string Type { get; set; } // "checked" | "carryon" (Despachada e Bagagem de mão)
        public int? Count { get; set; }
        public double? Weight { get; set; }
        public BaggageDimensions? Dimensions { get; set; }
        public double? LinearSize { get; set; }
    }

    public class BaggageDimensions
    {
        public double? Length { get; set; }
        public double? Width { get; set; }
        public double? Depth { get; set; }
    }


}