﻿using Backend.Flights.Models.Search;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace Backend.Flights.Models.GtwAereo
{
    public class SearchResponse
    {
        [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
        public string Error { get; set; }
        public List<Search.PriceGroup> PriceGroups { get; set; }
        [MustSkipPropertyIfContractResolverIsFlagged]
        public Search.PriceMatrix PriceMatrix { get; set; }
        [MustSkipPropertyIfContractResolverIsFlagged]
        public FiltersData FiltersData { get; set; }
        public int CompletedWork { get; set; }
    }
}
