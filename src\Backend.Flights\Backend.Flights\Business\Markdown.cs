﻿using Backend.Flights.CA.Services.Http;
using Backend.Flights.CA.Services.Json;
using Backend.Flights.Models;
using Backend.Flights.Models.Markdown;
using Backend.Flights.Services;
using Microsoft.Extensions.Logging;
using System.Linq;
using System.Threading.Tasks;

namespace Backend.Flights.Business
{
    public class Markdown
    {
        private readonly MarkdownService svc;
        private readonly string[] allowedProviders;
        private readonly BrandContext brandCtx;
        private readonly IHttpClient _httpClient;
        private readonly IJsonService _json;

        public Markdown(ILogger logger, BrandContext brandCtx, IHttpClient _httpClient, IJsonService _json)
        {
            this.brandCtx = brandCtx;
            svc = new MarkdownService(logger, brandCtx, _httpClient, _json);
            allowedProviders = brandCtx.CredipaxData.AllowedProviders;
        }

        public async Task<FullResponseData> GetCredipaxData(string cpf, string locatorCode, int reservation)
        {
            var response = await svc.GetCredipax(cpf, locatorCode, reservation);
            var markdownOrder = response.Orders.Where(x => x.packageGroup != "PACKAGE" && allowedProviders.Contains(x.provider) && x.branch.ToString() == brandCtx.GatewayHeaders.BranchId).FirstOrDefault();

            if (markdownOrder == null)
                return new FullResponseData();

            var locResp = svc.GetImportaLoc(markdownOrder.pnr, markdownOrder.provider);
            var locCheck = svc.CheckMarkdownAvailability(markdownOrder.pnr, markdownOrder.provider);

            await Task.WhenAll(locResp, locCheck);

            if (!locCheck.Result)
                return new FullResponseData { Error = "Esta reserva não pode ser remarcada por este meio. Por favor, entre em contato com nossa Central de Atendimento." };
            return new FullResponseData { CredipaxOrder = markdownOrder, ImportaLocBooking = locResp.Result?.AirBookings?[0]?.Booking };
        }

        public async Task<EstimatesResponse> GetEstimates(EstimatesRequest request) => await svc.GetEstimates(request);
    }
}
