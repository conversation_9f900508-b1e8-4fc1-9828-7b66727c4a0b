using Backend.Flights.CA.Model;
using Backend.Flights.CA.Services.Cache;
using Backend.Flights.CA.Services.Json;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Backend.Flights.CA.UseCase.RateTokenCache
{
    public class GetRateTokenCache : IGetRateTokenCache
    {
        private readonly IJsonService _json;
        private readonly ICacheService _cache;

        public GetRateTokenCache(IJsonService json, ICacheService cache)
        {
            _json = json;
            _cache = cache;
        }

        public async Task<RateTokenFareGroupUpgCache[]> Execute(IEnumerable<string> ids)
        {
            var values = await _cache.GetItems(ids);
            return values
                .Select(v => v == null ? null : _json.Deserialize<RateTokenFareGroupUpgCache>(v))
                .ToArray();
        }
    }
}