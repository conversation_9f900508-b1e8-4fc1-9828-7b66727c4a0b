﻿using Microsoft.AspNetCore.Mvc;
using System;

namespace Backend.Flights.CA
{
    public class SafeResult<R, E> where E : class
    {
        public R Result { get; }
        public E Error { get; }

        public bool IsError => Error != null;

        private SafeResult(R result, E error)
        {
            Result = result;
            Error = error;
        }

        public static SafeResult<R, E> Success(R result)
        {
            return new SafeResult<R, E>(result, null);
        }

        public static SafeResult<R, E> Fail(E error)
        {
            return new SafeResult<R, E>(default, error);
        }
    }
}


