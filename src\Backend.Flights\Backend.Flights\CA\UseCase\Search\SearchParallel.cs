using Backend.Flights.CA.Model.Search;
using Backend.Flights.CA.Model.Search.Result;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Backend.Flights.CA.UseCase.Search
{
    public class SearchParallel
    {
        private readonly GetSourcesList _getSourcesList;
        private readonly Services.Id.IRandomIdService _randomId;
        private readonly SearchSingleSource _searchSingleSource;

        public SearchParallel(GetSourcesList getSourcesList, Services.Id.IRandomIdService randomId, SearchSingleSource searchSingleSource)
        {
            _getSourcesList = getSourcesList;
            _randomId = randomId;
            _searchSingleSource = searchSingleSource;
        }

        private static async Task<(bool ok, IAsyncEnumerator<T> enumerator)> WaitNext<T>(IAsyncEnumerator<T> enumerator)
        {
            var ok = await enumerator.MoveNextAsync();
            return (ok, enumerator);
        }

        public async IAsyncEnumerable<(double completedWork, string error, (string id, SearchResultFlight response)? value)> Execute(
            Model.Context.BrandContext brandContext,
            SearchRequest request
        )
        {
            var sourceList = _getSourcesList.Execute(brandContext);
            var searchId = _randomId.RandomId();

            var searchs = sourceList.Select(source => WaitNext(
                _searchSingleSource.Execute(brandContext, request, source, searchId).GetAsyncEnumerator()
                )).ToList();

            var totalCount = searchs.Count / 100.0;

            while (searchs.Count > 0)
            {
                var doneTask = await Task.WhenAny(searchs);
                searchs.Remove(doneTask);

                var (ok, enumerator) = await doneTask;

                if (ok)
                {
                    var (err, response) = enumerator.Current;
                    searchs.Add(WaitNext(enumerator));
                    yield return (100 - (searchs.Count / totalCount), err, response);
                }
            }
        }
    }
}