﻿using Backend.Flights.CA.Model.Cart;
using Backend.Flights.CA.Model.Search.Result;
using Newtonsoft.Json;
using ProtoBuf;
using System.Collections.Generic;

namespace Backend.Flights.CA.Model.Cache
{
    [ProtoContract(SkipConstructor = true)]
    public class CartCheckout
    {
        [ProtoMember(1)]
        public CartFlightItem CartItem { get; }

        [ProtoMember(2)]
        public Dictionary<string, Option> Options { get; }

        [ProtoMember(3)]
        public UpsellOption[] Upsell { get; }

        public CartCheckout(CartFlightItem cartFlightItem, Dictionary<string, Option> dictionary, UpsellOption[] upsellOptions)
        {
            CartItem = cartFlightItem;
            Options = dictionary;
            Upsell = upsellOptions;
        }

        [ProtoContract(SkipConstructor = true)]
        public class Option
        {
            [ProtoMember(1)]
            public string Token { get; }

            [ProtoMember(2)]
            public string PackageGroup { get; }

            [ProtoMember(3)]
            public CartFlightItem.Segment Segment { get; }

            public Option(string rateToken, string packageGroup, CartFlightItem.Segment segment)
            {
                Token = rateToken;
                PackageGroup = packageGroup;
                Segment = segment;
            }
        }

        [ProtoContract(SkipConstructor = true)]
        public class UpsellOption
        {
            [ProtoMember(1), JsonProperty("uid")]
            public string Uid { get; }

            [ProtoMember(2), JsonProperty("fare")]
            public SearchResultFlight.ResultFareProfile Fare { get; }

            public UpsellOption(string uid, SearchResultFlight.ResultFareProfile fareProfile)
            {
                Uid = uid;
                Fare = fareProfile;
            }
        }
    }
}