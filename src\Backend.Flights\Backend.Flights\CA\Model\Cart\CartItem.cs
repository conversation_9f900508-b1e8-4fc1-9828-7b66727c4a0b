using Newtonsoft.Json;

namespace Backend.Flights.CA.Model.Cart
{
    public class CartItem
    {
        [JsonProperty("rph")]
        public int RPH;
        [JsonProperty("rateToken")]
        public string RateToken;

        [JsonProperty("group")]
        public string Group { get; set; }

        [JsonProperty("routeRPH")]
        public int? RouteRPH { get; set; }

        public CartItem(int rph, string rateToken, int? routeRPH = null)
        {
            this.RPH = rph;
            this.RateToken = rateToken;
            this.RouteRPH = routeRPH;
        }

        public CartItem()
        {
        }
    }
}