using Microsoft.Extensions.Logging;
using System;
using System.Net;
using static Backend.Flights.Services.IConsul;

namespace Backend.Flights.Services
{
    public class CorpLoginService : ICorpLoginService
    {
        private static string CONFIG_CORP_LOGIN_URL = STR_PROJECT_NAME + "/corpLogin.url";

        private readonly ILogger<CorpLoginService> _logger;
        private readonly IConsulWatch _consul;

        public CorpLoginService(ILogger<CorpLoginService> logger, IConsulWatch consul)
        {
            _logger = logger;
            _consul = consul;
            _consul.WatchKey(CONFIG_CORP_LOGIN_URL);
        }

        public bool ValidateToken(string userToken)
        {
            var webClient = new WebClient();
            var url = string.Empty;

            // Feito isso pois antigamente passava o header Authorization, mas em alguns casos está dando erro
            try
            {
                url = _consul.WatchKey(CONFIG_CORP_LOGIN_URL).Result + "?token=" + userToken;
                webClient.DownloadData(_consul.WatchKey(CONFIG_CORP_LOGIN_URL).Result + "?token=" + userToken);
                return true;
            }
            catch (Exception e)
            {
                _logger.LogInformation(e, "Fail on validate user token without Header Authorization {userToken} {url}", userToken, url);
            }

            webClient.Headers.Add(HttpRequestHeader.Authorization, $"Bearer {userToken}");

            try
            {
                url = _consul.WatchKey(CONFIG_CORP_LOGIN_URL).Result + "?token=" + userToken;
                webClient.DownloadData(url);
            }
            catch (Exception e)
            {
                _logger.LogWarning(e, "Fail on validate user token {userToken} {url}", userToken, url);
                return false;
            }

            return true;
        }
    }
}