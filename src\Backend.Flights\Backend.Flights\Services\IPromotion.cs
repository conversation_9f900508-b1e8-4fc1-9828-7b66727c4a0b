﻿using Backend.Flights.Models;
using Backend.Flights.Models.Promotions;
using System.Threading.Tasks;

namespace Backend.Flights.Services
{
    public interface IPromotion
    {
        Task<PromotionResponse> GetPromotions(IContextBrand contextBrand, BffPromotionRequest request, string gtwUserToken);
        Task<PromotionResponse> GetPromotions(BrandContext contextBrand, PromotionRequest request, string gtwUserToken);
    }
}