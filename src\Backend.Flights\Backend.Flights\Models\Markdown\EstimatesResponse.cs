﻿using System.Collections.Generic;

namespace Backend.Flights.Models.Markdown
{


    public class EstimatesResponse
    {
        public Meta meta { get; set; }
        public Detail detail { get; set; }
        public FareGroup fareGroup { get; set; }
        public Credipax credipax { get; set; }
    }

    public class Meta
    {
        public double markup { get; set; }
        public double credipax { get; set; }
        public double fareValue { get; set; }
        public double taxesValue { get; set; }
        public int markdownTaxesValue { get; set; }
        public double priceWithTax { get; set; }
        public double priceWithoutTax { get; set; }
    }

    public class Detail
    {
        public int countPaxCredipax { get; set; }
        public int handbagTaxValue { get; set; }
        public double markupOldToken { get; set; }
        public int countPaxOldTokens { get; set; }
        public double priceNetWithoutTax { get; set; }
        public double priceNetWithTax { get; set; }
        public double newTaxValue { get; set; }
        public string message { get; set; }
    }

    public class Value
    {
        public string currency { get; set; }
        public double amount { get; set; }
    }

    public class Tax
    {
        public string code { get; set; }
        public string description { get; set; }
        public List<Value> values { get; set; }
        public double? percent { get; set; }
    }

    public class OriginalPriceInfo
    {
        public string currency { get; set; }
        public double exchangeRate { get; set; }
        public double baseFare { get; set; }
    }

    public class Fare
    {
        public string passengersType { get; set; }
        public int passengersCount { get; set; }
        public double priceWithTax { get; set; }
        public double priceWithoutTax { get; set; }
        public List<Tax> taxes { get; set; }
        public OriginalPriceInfo originalPriceInfo { get; set; }
    }

    public class OriginalPriceInfo2
    {
        public string currency { get; set; }
        public double exchangeRate { get; set; }
        public double baseFare { get; set; }
    }

    public class FareGroup
    {
        public bool reCharging { get; set; }
        public string currency { get; set; }
        public double priceWithTax { get; set; }
        public double priceWithoutTax { get; set; }
        public List<Fare> fares { get; set; }
        public OriginalPriceInfo2 originalPriceInfo { get; set; }
    }

    public class Pax
    {
        public int id { get; set; }
    }

    public class Credit
    {
        public int credipax { get; set; }
        public double balance { get; set; }
        public double creditValue { get; set; }
        public int contractorCode { get; set; }
        public int contractorId { get; set; }
        public List<Pax> pax { get; set; }
        public int entry { get; set; }
    }

    public class Order
    {
        public string pnr { get; set; }
        public string provider { get; set; }
        public int order { get; set; }
        public int discPromo { get; set; }
        public double discEntry { get; set; }
        public List<Credit> credit { get; set; }
        public string mkp { get; set; }
        public string var { get; set; }
        public string bag { get; set; }
        public string txAd { get; set; }
        public string qtPax { get; set; }
    }

    public class Credipax
    {
        public string status { get; set; }
        public string message { get; set; }
        public List<Order> orders { get; set; }
    }



}
