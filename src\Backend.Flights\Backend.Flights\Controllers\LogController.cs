﻿using Backend.Flights.Models.Search;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace Backend.Flights.Controllers
{
    [ApiController, Route("api/[controller]")]
    public class LogController : ControllerBase
    {
        private readonly ILogger<LogController> logger;
        public LogController(ILogger<LogController> logger)
        {
            this.logger = logger;
        }

        public class LogDTO
        {
            public Dictionary<string, object> Scope { get; set; }
            public string Message { get; set; }
        }
        [Route(""), EnableCors("AllowAnyOrigin"), HttpPost]
        public ActionResult Log(LogDTO dto)
        {
            if (dto != null)
            {
                using (logger.BeginScope(dto.Scope))
                    logger.LogInformation(dto.Message);
            }
            return Ok();
        }
        private ActionResult Log(Dictionary<string, object> scope, string message) => this.Log(new LogDTO { Scope = scope, Message = message });

        public class UpsellDataDTO
        {
            public SearchRequest SearchData { get; set; }
            public string Provider { get; set; }
            public string Cia { get; set; }
            public bool OfferAccepted { get; set; }
            public decimal OriginalValue { get; set; }
            public decimal CheckoutValue { get; set; }
            public decimal[] AllOfferedValues { get; set; }
            public bool abTestOfferUpsell { get; set; }//Excluir após teste A/B
        }
        [Route("[action]"), EnableCors("AllowAnyOrigin"), HttpPost]
        public ActionResult Upsell(UpsellDataDTO upsellData)
            => this.Log(new Dictionary<string, object>
            {
                ["operation"] = "UPSELL",
                ["searchRequest"] = JsonConvert.SerializeObject(upsellData.SearchData),
                ["clientId"] = upsellData.SearchData.ClientId,
                ["searchId"] = upsellData.SearchData.SearchId,
                ["provider"] = upsellData.Provider,
                ["cia"] = upsellData.Cia,
                ["offerAccepted"] = upsellData.OfferAccepted ? 1 : 0,
                ["originalValue"] = upsellData.OriginalValue,
                ["checkoutValue"] = upsellData.CheckoutValue,
                ["allOfferedValues"] = JsonConvert.SerializeObject(upsellData.AllOfferedValues),
                ["abTestOfferUpsell"] = upsellData.abTestOfferUpsell//Excluir após teste A/B
            }, upsellData.SearchData.ToString());

        //Excluir após teste A/B
        public class UpsellOfferDataDTO
        {
            public SearchRequest SearchData { get; set; }
            public int NumberOfOfferings { get; set; }
        }
        [Route("[action]"), EnableCors("AllowAnyOrigin"), HttpPost]
        public ActionResult UpsellOffer(UpsellOfferDataDTO upsellData)
            => this.Log(new Dictionary<string, object>
            {
                ["operation"] = "UPSELLOFFER",
                ["searchRequest"] = JsonConvert.SerializeObject(upsellData.SearchData),
                ["clientId"] = upsellData.SearchData.ClientId,
                ["searchId"] = upsellData.SearchData.SearchId,
                ["numberOfOfferings"] = upsellData.NumberOfOfferings,
            }, upsellData.SearchData.ToString());

        [Route("[action]"), EnableCors("AllowAnyOrigin"), HttpPost]
        public ActionResult ZeroMinutesDuration(Models.Search.Flight zeroMinuteFlight)
            => this.Log(new Dictionary<string, object> { ["operation"] = "USERSAWZEROMINUTEFLIGHT" }, JsonConvert.SerializeObject(zeroMinuteFlight));
    }
}