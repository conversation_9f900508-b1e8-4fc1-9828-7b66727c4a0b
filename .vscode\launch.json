{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "TI",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/src/Backend.Flights/Backend.Flights/bin/Debug/net7.0/Backend.Flights.dll",
            "args": [],
            "cwd": "${workspaceFolder}/src/Backend.Flights/Backend.Flights",
            "stopAtEntry": false,
            // "serverReadyAction": { // Comentado para não abrir o Swagger automaticamente
            //     "action": "openExternally",
            //     "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
            // },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "ti",
                "MONGODB_USERNAME_OCI": "db_flights",
                "MONGODB_PASSWORD_OCI": "joAW2gPg0Se2dsQHzZx0Ny79j",
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            }
        },
        {
            "name": "QA",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/src/Backend.Flights/Backend.Flights/bin/Debug/net7.0/Backend.Flights.dll",
            "args": [],
            "cwd": "${workspaceFolder}/src/Backend.Flights/Backend.Flights",
            "stopAtEntry": false,
            // "serverReadyAction": { // Comentado para não abrir o Swagger automaticamente
            //     "action": "openExternally",
            //     "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
            // },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "qa",
                "MONGODB_USERNAME_OCI": "db_flights",
                "MONGODB_PASSWORD_OCI": "joAW2gPg0Se2dsQHzZx0Ny79j",
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            }
        },
        {
            "name": "PROD",
            "type": "coreclr",
            "request": "launch",
            "preLaunchTask": "build",
            "program": "${workspaceFolder}/src/Backend.Flights/Backend.Flights/bin/Debug/net7.0/Backend.Flights.dll",
            "args": [],
            "cwd": "${workspaceFolder}/src/Backend.Flights/Backend.Flights",
            "stopAtEntry": false,
            // "serverReadyAction": {
            //     "action": "openExternally",
            //     "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
            // },
            "env": {
                "ASPNETCORE_ENVIRONMENT": "prod",
                "MONGODB_USERNAME_OCI": "db_flights", // QA
                "MONGODB_PASSWORD_OCI": "joAW2gPg0Se2dsQHzZx0Ny79j", // QA
            },
            "sourceFileMap": {
                "/Views": "${workspaceFolder}/Views"
            }
        },
        {
            "name": ".NET Core Attach",
            "type": "coreclr",
            "request": "attach"
        }
    ]
}