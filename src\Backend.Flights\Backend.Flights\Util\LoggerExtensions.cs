using System;
using Microsoft.Extensions.Logging;

namespace Backend.Flights.Util
{
    public static class LoggerExtensions
    {
        // Foi criado para ser usado futuramente com a chave LogInformationInfo (ela não foi configurada ainda)
        public static void LogInformationInfo(this ILogger logger, string message, ConfigurationManager config)
        {
            try
            {
                if (config.AppSetting.TryGetValue("LogInformationInfo", out string logEnabled) && logEnabled.ToLower() == "true")
                {
                    logger.LogInformation(message);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error while logging information info: " + ex.Message);
            }
        }

        public static void LogInfo(this string message)
        {
            Console.WriteLine($"[LOG-INFO] {DateTime.Now}: {message}");
        }

        public static void LogError(this string message)
        {
            Console.WriteLine($"[LOG-ERROR] {DateTime.Now}: {message}");
        }

        public static void LogWarning(this string message)
        {
            Console.WriteLine($"[LOG-WARN] {DateTime.Now}: {message}");
        }

        public static void LogDebug(this string message)
        {
            Console.WriteLine($"[LOG-DEBUG] {DateTime.Now}: {message}");
        }
    }
}