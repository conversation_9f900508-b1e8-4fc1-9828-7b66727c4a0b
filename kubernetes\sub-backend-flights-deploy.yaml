apiVersion: apps/v1
kind: Deployment
metadata:
  name: sub-backend-flights-deploy
  namespace: sub-flights
  labels:
    app: sub-backend-flights
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sub-backend-flights
  template:
    metadata:
      labels:
        app: sub-backend-flights
      annotations:
        vault.security.banzaicloud.io/vault-addr: __VAULT_ADDR__
    spec:
      serviceAccountName: sub-backend-flights
      containers:
      - name: sub-backend-flights
        image: ************.dkr.ecr.sa-east-1.amazonaws.com/sub-backend-flights:__TAG__
        imagePullPolicy: Always
        resources:
          requests:
            memory: "300Mi"
            cpu: "100m"
          limits:
            memory: "1500Mi"
            cpu: "500m"
        startupProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 80
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 15
          successThreshold: 1
          timeoutSeconds: 10
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 80
            scheme: HTTP
          initialDelaySeconds: 15
          periodSeconds: 15
          successThreshold: 1
          timeoutSeconds: 10
        livenessProbe:
          failureThreshold: 3
          httpGet:
            path: /health
            port: 80
            scheme: HTTP
          initialDelaySeconds: 15
          periodSeconds: 15
          successThreshold: 1
          timeoutSeconds: 10
        env:
          - name: SEARCH_FLIGHTS_API_KEY
            valueFrom:
              secretKeyRef:
                name: search-flights-consumer2-key
                key: key
          - name: INSTANA_AGENT_HOST
            valueFrom:
              fieldRef:
                fieldPath: status.hostIP
        envFrom:
          - configMapRef:
              name: sub-backend-flights
          - secretRef:
              name: sub-backend-flights
        ports:
        - containerPort: 80
      imagePullSecrets:
      - name: dockerhubkey