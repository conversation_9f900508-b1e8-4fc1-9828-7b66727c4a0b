﻿using Backend.Flights.CA.UseCase.ContentProvider;

namespace Backend.Flights.CA.UseCase.Search
{
    public class GetSearchUrl
    {
        private const string URL_SUFFIX = "/flights?";

        private readonly GetContentProviderUrl _getContentProviderUrl;

        public GetSearchUrl(GetContentProviderUrl getContentProviderUrl)
        {
            _getContentProviderUrl = getContentProviderUrl;
        }

        public string Execute(Model.Context.BrandContext brandContext, string source)
        {
            return _getContentProviderUrl.Execute(brandContext, source) ??
                   brandContext.GatewayUrl + URL_SUFFIX;
        }
    }
}
