﻿using System;
using System.Collections.Generic;
using Backend.Flights.Models.GtwAereo;

namespace Backend.Flights.Models.Markdown.ImportaLoc
{
    public class Pax
    {
        public int Id { get; set; }
        public string PassengerType { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Gender { get; set; }
    }

    public class Payment
    {
        public string Processor { get; set; }
        public string Type { get; set; }
        public string Currency { get; set; }
        public double Value { get; set; }
    }

    public class AirTicket
    {
        public string Code { get; set; }
        public string Status { get; set; }
        public DateTime CreationDate { get; set; }
        public AirCompany ValidatingBy { get; set; }
        public Pax Pax { get; set; }
        public List<AirToken> AirTokens { get; set; }
        public Fare Fare { get; set; }
        public List<Payment> Payments { get; set; }
        public Gds Gds { get; set; }
    }

    public class AirToken
    {
        public Segment Segment { get; set; }
        public string ReservationToken { get; set; }
    }

    public class Gds
    {
        public string Provider { get; set; }
        public string ReservationCode { get; set; }
    }

    public class Remark
    {
        public string Message { get; set; }
    }

    public class Air
    {
        public PriceGroup PriceGroup { get; set; }
        public List<AirTicket> AirTickets { get; set; }
        public Gds Gds { get; set; }
        public double Markup { get; set; }
        public string PackageGroup { get; set; }
        public string FareType { get; set; }
        public string FareCode { get; set; }
        public string BookingToken { get; set; }
        public string ReservationToken { get; set; }
        public List<Remark> Remarks { get; set; }
    }


    public class Booking
    {
        public DateTime CreationDate { get; set; }
        public DateTime IssueDateTimeLimit { get; set; }
        public string Status { get; set; }
        public double TotalOrderPrice { get; set; }
        public List<Payment> Payments { get; set; }
        public List<Pax> Paxs { get; set; }
        public List<Air> Airs { get; set; }
    }

    public class AirBooking
    {
        public Booking Booking { get; set; }
    }
    public class ImportaLocResponse
    {
        public List<AirBooking> AirBookings { get; set; }
    }

    public class CheckMarkdownAvailabilityResponse
    {
        public object status { get; set; }
        public Dictionary<string, bool> checkList { get; set; }
    }
}
