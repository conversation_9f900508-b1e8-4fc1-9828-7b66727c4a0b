using Backend.Flights.Models.GtwAereo;
using Newtonsoft.Json;
using System.Collections.Generic;

namespace Backend.Flights.CA.Model
{
    public class RateTokenFareGroupUpgCache
    {
        /// <summary>
        /// Dados referentes a informações de bagagem e serviços do priceGroup atual.
        /// </summary>
        [JsonProperty("bg")]
        public Dictionary<string, string> Baggages { get; set; }
        
        /// <summary>
        /// Dados referentes a informações de bagagem e serviços do priceGroup atual.
        /// </summary>
        [JsonProperty("FP")]
        public FareProfile FareProfile { get; set; }

        /// <summary>
        /// Ids dos priceGroups que são upgrades do atual.
        /// </summary>
        [JsonProperty("Upg")]
        public List<string> PossibleUpgrades { get; set; }

        /// <summary>
        /// RateTokens de todos os trechos e pernas possíveis, sendo que a chave do dicionário é formado pelo [Índice do trecho (segmentIndex)]-[Índice do vôo dentro do trecho (índice no array flights dentro do segment)]
        /// </summary>
        [JsonProperty("Tkn")]
        public Dictionary<string, string> Tokens { get; set; }
    }
}