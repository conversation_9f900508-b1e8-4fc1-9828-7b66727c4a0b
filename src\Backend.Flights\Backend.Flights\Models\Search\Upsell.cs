﻿using Backend.Flights.Models.GtwAereo;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Backend.Flights.Models.Search
{
    internal class FlightUpsell : Dictionary<string, List<string>>
    {
        private readonly Dictionary<string, FareProfile> dictFareProfs;
        public FlightUpsell(List<GtwAereo.Flight> flights, Dictionary<string, FareProfile> fareProfs)
        {
            this.dictFareProfs = fareProfs;
            buildUpsellData(flights);
        }

        private class Legs : IEquatable<Legs>
        {
            public List<string> FlightNumbers { get; set; } = new List<string>();

            public bool Equals(Legs other)
            {
                if (this.FlightNumbers.Count == 0 || this.FlightNumbers.Count != other.FlightNumbers.Count)
                    return false;

                for (var i = 0; i < this.FlightNumbers.Count; i++)
                    if (!string.Equals(this.FlightNumbers[i], other.FlightNumbers[i]))
                        return false;
                return true;
            }
        }

        private void buildUpsellData(List<GtwAereo.Flight> flights)
        {
            var linkedFlights = getLinkedFlights(flights);
            foreach (var item in flights)
            {
                var upgrade = item.UID;

                var result = new List<string>();
                while ((upgrade = linkedFlights[upgrade]) != null)
                    result.Add(upgrade);
                this.Add(item.UID, result);
            }
        }
        private Dictionary<string, string> getLinkedFlights(List<GtwAereo.Flight> flights)
        {
            Legs getLegs(GtwAereo.Flight flight) => new Legs { FlightNumbers = flight.Segments.SelectMany(x => x.Legs).Select(y => y.FlightNumber).ToList() };

            return flights.Select((flight, i) =>
    {
        var baseOfComparison = (flight.UID, flight.FareGroup.PriceWithTax, getLegs(flight), flight.Segments.FirstOrDefault()?.FareType);
        var nearestUpgrade = flights.Skip(i).FirstOrDefault(flightToMatch =>
         IsFlightUpgrade(baseOfComparison, (flightToMatch.UID, flightToMatch.FareGroup.PriceWithTax, getLegs(flightToMatch), flightToMatch.Segments.FirstOrDefault()?.FareType)));
        return new { flight.UID, NearestUpgrade = nearestUpgrade?.UID };
    }).ToDictionary(x => x.UID, x => x.NearestUpgrade);
        }
        /// <summary>
        /// Regra do upsell:
        /// Um vôo Y é considerado upgrade do vôo X se:
        /// - Y estiver abaixo de X na lista de vôos recebida como parâmetro e
        /// - o valor final do vôo X é menor que o valor final do vôo Y
        /// - os números de vôo de todas as pernas possíveis de Y for igual aos números de vôo de todas as pernas possíveis de X
        /// - é preciso haver alguma diferença nos serviços oferecidos (FareProfile) nos vôos X e Y
        /// </summary>

        // private bool flightIsUpgrade((string uid, decimal price, Legs legs) baseOfComparison, (string uid, decimal price, Legs legs) toCompare)
        //     => baseOfComparison.price < toCompare.price && baseOfComparison.legs.Equals(toCompare.legs) && hasDifferentServices(this.dictFareProfs[baseOfComparison.uid], this.dictFareProfs[toCompare.uid]);

        private bool IsFlightUpgrade((string uid, decimal price, Legs legs, string fareType) baseFlight, (string uid, decimal price, Legs legs, string fareType) flightToCompare)
        {
            bool isPriceHigher = baseFlight.price < flightToCompare.price;
            bool hasSameLegs = baseFlight.legs.Equals(flightToCompare.legs);
            bool hasDifferentServices = HasDifferentServices(this.dictFareProfs[baseFlight.uid], this.dictFareProfs[flightToCompare.uid]);
            bool hasSameFareType = string.Equals(baseFlight.fareType, flightToCompare.fareType, StringComparison.OrdinalIgnoreCase);

            return isPriceHigher && hasSameLegs && hasDifferentServices && hasSameFareType;
        }
        private static bool HasDifferentServices(FareProfile prof1, FareProfile prof2)
        {
            if (!string.Equals(prof1.FareFamily, prof2.FareFamily))
                return true;

            if (!baggageEquals(prof1.Baggage, prof2.Baggage))
                return true;

            if (!servicesEquals(prof1.Services, prof2.Services))
                return true;

            return false;
        }
        private static bool baggageEquals(Baggage bg1, Baggage bg2)
        {
            if (bg1 == null && bg2 == null) return true;
            if (bg1 == null || bg2 == null) return true;
            return bg1.IsIncluded == bg2.IsIncluded && bg1.Quantity == bg2.Quantity && bg1.Weight == bg2.Weight && string.Equals(bg1.Type, bg2.Type) && string.Equals(bg1.Uom, bg2.Uom);
        }
        private static bool servicesEquals(List<FareProfileService> services1, List<FareProfileService> services2)
        {
            if (services1 == null && services2 == null)
                return true;
            if (services1 == null || services2 == null)
                return false;
            if (services1.Count != services2.Count)
                return false;

            for (var i = 0; i < services1.Count; i++)
                if (!serviceEquals(services1[i], services2[i]))
                    return false;
            return true;
        }
        private static bool serviceEquals(FareProfileService serv1, FareProfileService serv2)
            => serv1.IsIncluded == serv2.IsIncluded && string.Equals(serv1.Description, serv2.Description) && string.Equals(serv1.Type, serv2.Type);
    }
}