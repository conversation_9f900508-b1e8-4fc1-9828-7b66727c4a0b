services:
  sub-backend-flights:
    image: sub-backend-flights:latest
    build:
      context: .
      dockerfile: Dockerfile
      args:
        VERACODE_APP_ID: "ff2db00e38778a7a1154c03528aff5ac"
        VERACODE_API_KEY: "db0e087b6e3d111befecb383a55ce1a7347babe11e7b4dc5c488a2779ff82c69bda43f5c9d20d70655065253c20b0fc692d604caec5a696965754f22d2766702"
        BUILD_ID: "1000999"
    ports:
      - "5001:80"
      - "5005:5005"
    environment:
      ASPNETCORE_ENVIRONMENT: "qa"
      DOTNET_USE_POLLING_FILE_WATCHER: "1"
      DOTNET_RUNNING_IN_CONTAINER: "true"
      DOTNET_MODIFIABLE_ASSEMBLIES: "debug"
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
