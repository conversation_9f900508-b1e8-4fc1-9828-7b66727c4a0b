﻿using Backend.Flights.Business;
using Backend.Flights.Util;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Backend.Flights.Controllers
{
    [ApiController, ApiVersion("1.0"), Route("api/[controller]")]
    public class LocationsController : ControllerBase
    {
        private readonly ConfigurationManager _config;
        private readonly ILogger<LocationsController> _logger;
        private readonly CA.UseCase.AirLocation.SearchLocationByIata _searchLocationByIata;

        public LocationsController(
            ILogger<LocationsController> logger, ConfigurationManager config,
            CA.UseCase.AirLocation.SearchLocationByIata searchLocationByIata
            )
        {
            _config = config;
            _logger = logger;
            _searchLocationByIata = searchLocationByIata;
        }

        [HttpGet, EnableCors("AllowAnyOrigin")]
        public ActionResult<Models.Locations.LocationsResponse> Get(string query) => callLocations("Get", query, new AirLocations(_config).Get);

        [HttpGet, Route("[action]"), EnableCors("AllowAnyOrigin")]
        public async Task<ActionResult<Models.Locations.Location>> LoadIata(string iata)
        {
            var cleanIata = iata?.Trim()?.ToUpper();
            if (cleanIata.Length != 2 && cleanIata.Length != 3)
            {
                return BadRequest("Bad iata code");
            }

            return Ok(await _searchLocationByIata.Execute(iata));
        }

        private ActionResult callLocations(string caller, string param, Func<string, ILogger, object> queryFn)
        {
            try
            {
                var result = queryFn(param, _logger);
                // [log-disabled] log(caller, param, string.Empty, true);
                return Ok(result);
            }
            catch (Exception ex)
            {
                log(caller, param, ex.Message + " - " + ex.StackTrace, false);
                return BadRequest(ex.Message);
            }
        }
        private void log(string caller, string query, string message, bool success)
        {
            using (_logger.BeginScope(new Dictionary<string, object>
            {
                ["operation"] = "LOCATIONSCTRL",
                ["caller"] = caller,
                ["query"] = query,
                ["success"] = success ? 1 : 0
            }))
            {
                _logger.LogInformation(message);
            }
        }
    }
}
