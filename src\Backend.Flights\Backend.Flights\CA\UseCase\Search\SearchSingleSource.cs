using Backend.Flights.CA.Model.Search;
using Backend.Flights.CA.Model.Search.Result;
using Backend.Flights.CA.Services.AirGateway;
using Backend.Flights.CA.Services.Cache;
using Backend.Flights.CA.Services.Id;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Backend.Flights.CA.UseCase.Search
{
    public class SearchSingleSource
    {
        private readonly IAirGateway _airGateway;
        private readonly IRandomIdService _randomIdService;
        private readonly ICacheService _cacheService;
        private readonly Upsell.ComputeUpsell _computeUpsell;
        private readonly Checkout.ComputeCheckoutCache _computeCheckoutCache;
        private readonly Checkout.SerializeCheckoutCache _serializeCheckoutCache;

        public SearchSingleSource(
            IAirGateway airGateway,
            IRandomIdService randomIdService,
            ICacheService cacheService,
            Upsell.ComputeUpsell computeUpsell,
            Checkout.ComputeCheckoutCache checkoutCache,
            Checkout.SerializeCheckoutCache serializeCheckoutCache)
        {
            _airGateway = airGateway;
            _randomIdService = randomIdService;
            _cacheService = cacheService;
            _computeUpsell = computeUpsell;
            _computeCheckoutCache = checkoutCache;
            _serializeCheckoutCache = serializeCheckoutCache;
        }

        public async IAsyncEnumerable<(string error, (string id, SearchResultFlight response)?)> Execute(
            Model.Context.BrandContext brandContext,
            SearchRequest request,
            string source,
            string searchId)
        {
            var responses = new Dictionary<string, SearchResultFlight>();

            await foreach (var (error, response) in _airGateway.Search(brandContext, request, source))
            {
                if (error != null)
                {
                    yield return ($"Gateway Error - {error.Code} - {error.Message} - ${error.CorrelationId}", null);
                }
                else
                {
                    var id = searchId + _randomIdService.SequentialId();
                    responses.Add(id, response);

                    yield return (null, (id, response));
                }
            }

            var upsell = _computeUpsell.Execute(responses);

            var myTasks = responses.Select(async item =>
            {
                var checkoutCache = await _computeCheckoutCache.Execute(item.Key, upsell.GetValueOrDefault(item.Key, new List<string>()), responses);
                await _cacheService.SetItem($"f/{item.Key}", _serializeCheckoutCache.Execute(checkoutCache));
            });

            await Task.WhenAll(myTasks);
        }
    }
}