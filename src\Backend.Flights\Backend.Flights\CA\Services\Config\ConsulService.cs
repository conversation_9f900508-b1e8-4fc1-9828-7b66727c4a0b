using Backend.Flights.Util;
using Consul;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Text;
using System.Threading.Tasks;

namespace Backend.Flights.CA.Services.Config
{
    public class ConsulService : IConfigService
    {
        public const string CONSUL_CONFIG = "AppSettings:Consul.Url";

        private readonly ConsulClient _consul;
        private readonly ILogger<ConsulService> _log;

        public ConsulService(ILogger<ConsulService> log, IConfiguration config)
        {
            _log = log;

            $"[ConsulService]: Creating service: CONSUL_CONFIG: {CONSUL_CONFIG}".LogInfo();

            var consulUrl = config.GetSection(CONSUL_CONFIG).Value;

            $"[ConsulService]: Consul URL: {consulUrl}".LogInfo();

            _consul = new ConsulClient((c) => { c.Address = new Uri(consulUrl); });
        }

        public async Task<string> GetConfig(string configName)
        {
            try
            {
                var kvResult = await _consul.KV.Get(configName);
                if (kvResult.Response?.Value == null)
                {
                    _log.LogInformation($"[ConsulService]: GetConfig: {configName} chave existe porém sem valor");
                    return string.Empty;
                }


                var value = Encoding.UTF8?.GetString(kvResult.Response?.Value);
                // [log-disabled] $"[ConsulService]: GetConfig: {configName} = {value}".LogInfo();
                return value;
            }
            catch (Exception ex)
            {
                _log.LogError(ex, ex.GetErrorLocation($"[ConsulService]: GetConfig: Falha ao buscar configuração: {configName}"));
            }

            return null;
        }
    }
}