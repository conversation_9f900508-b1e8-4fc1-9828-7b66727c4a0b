using System.Collections.Generic;
using Backend.Flights.CA.Model.Cart;
using Newtonsoft.Json;

namespace Backend.Flights.CA.Model.Opportunities
{
    public class OpportunitiesRequest
    {
        [JsonProperty("params")]
        public RequestParams Params { get; set; }
        [JsonProperty("availableItems")]
        public CartItem[] AvailableItems { get; set; }
        [JsonProperty("selectedItems")]
        public CartItem[] SelectedItems { get; set; }

        public class RequestParams
        {
            [JsonProperty("showSteps")]
            public bool ShowSteps { get; set; } = false;
            [JsonProperty("showPayloads")]
            public bool ShowPayloads { get; set; } = false;
            [JsonProperty("profileName")]
            public string ProfileName { get; set; }
            [JsonProperty("type")]
            public string Type { get; set; }
            [JsonProperty("profitSplit")]
            public ProfitSplit ProfitSplit { get; set; } // Adicionada a propriedade profitSplit
        }

        public class ProfitSplit
        {
            [JsonProperty("products")]
            public List<ProfitSplitProduct> Products { get; set; }
        }

        public class ProfitSplitProduct
        {
            [JsonProperty("type")]
            public string Type { get; set; }

            [JsonProperty("groups")]
            public List<int> Groups { get; set; }
        }
    }
}