# Project Description [sub-backend-flights]

This project provides an interface for users that need access to GWAEREO. We use it to list flights available, pricing, markdown, etc.
Both the packages and flights team consume this service. If you want to know more about how it functions, check the Instana link listed below.

Currently we are working to break down this project into others.

## 🚀 Como rodar o projeto localmente

### Pré-requisitos

- **.NET Core** (versão recomendada: 7.0)
  [.Net SDK 7.0](https://dotnet.microsoft.com/pt-br/download/dotnet/thank-you/sdk-7.0.410-windows-x64-installer)

### Instruções

1. Clone o repositório:

   ```bash
   git clone https://git.cvc.com.br/shopping-flights/sub-backend-flights.git
   cd src/Backend.Flights
   ```

2. Compile o projeto:

   ```bash
   dotnet build
   ```

3. Execute o projeto localmente:

   ```bash
   dotnet run
   ```

4. O projeto estará disponível localmente em:

   ```
   http://localhost:5000
   https://localhost:5001
   ```

---

### Instruções para rodar o projeto com Docker

1. Rodar o projeto com Docker:

   ```bash
   docker-compose --project-name bff-flights up
   ```

   O projeto estara disponivel na porta 5001.

   ```bash
   http://localhost:5001/index.html
   ```

---

## Useful Links

- [Swagger](http://sub-backend-flights.k8s-cvc.com.br/swagger/index.html)
- [Kibana](https://kibana.services.cvc.com.br/s/flights/app/kibana#/dashboard/d6a383f0-8800-11eb-a141-2929cc9468fa)
- [Instana - Deployment](https://apm-cvc.instana.io/#/kubernetes/deployment;deploymentId=mNfRGi86Zd3DgTgpH8szZdDJeB8;namespaceId=k-Vs2ji5sboO1yPh2wP7NGPE4Yo/)
- [Instana - Application](https://apm-cvc.instana.io/#/service;appId=YSlq6L7ARCGiUZXEf72mYQ;serviceId=2e600cb3f74957c36506da480cc48da08f3000ab)
- [Veracode](https://web.analysiscenter.veracode.com)

## Postman Collection

Under the docs folder you have a JSON file that you can import into POSTMAN with some simple requests

## Endpoints

- PROD: <http://sub-backend-flights.k8s-cvc.com.br/>
- QA: <http://sub-backend-flights.k8s-qa-cvc.com.br/>
- TI: <http://sub-backend-flights.k8s-ti-cvc.com.br/>

## Related Projects

- Gateway Aéreo: Interface that allows us to communicate with airlines. If you need help talk to someone on the Air Tribe
- [Search-Flights](https://git.cvc.com.br/Desenvolvimento-MS/shopping-flights/search-flights) is Proxy that is aiming to replace search in this project.

## How to make a Deployment

1. Open a merge request
2. If you are planning to deploy in QA or PROD, Create RFC in Jira and get through the necessary steps
3. Merge branch into master and keep an eye on the deployment at [Jenkins](http://corp-jenkins01.compute.br-sao-1.cvccorp.cloud:8080/job/MS/job/MS-sub-backend-flights/)
4. A new version tag is created automatically during the pipeline, select it.
5. The pipeline will notify you when the deploy is finished, make sure everything is working well
6. [Hopefully not needed] If anything goes wrong, you can trigger a manual build and perform a deploy using an older version

## Authors

**Shopping Flights team**
See also the list of [contributors](https://git.cvc.com.br/SubFlights/sub-results-flights/-/graphs/master) who participated in this project.

## Chaves Consul

As chaves do Consul estão organizadas da seguinte forma:

- sub-backend-flights
- platform/brand-contexts/flights

### Configurações Principais

#### Cache da Matrix de Vendas
- **Chave**: `sub-backend-flights/matrix.sales.cache.enabled`
- **Valor**: `true` (habilitado) ou `false` (desabilitado)
- **Descrição**: Controla se os resultados da busca do AsyncStream são salvos no cache para uso na matrix de vendas. Quando habilitado, os dados ficam disponíveis por 20 minutos e podem ser consultados via endpoints `GetCachedSearchResults` e `GetCachedSearchResultsFiltered`. Quando desabilitado, o AsyncStream funciona normalmente mas não salva dados no cache.
- **Padrão**: Se a chave não existir, considera como desabilitado

#### Logs de Request
- **Chave**: `sub-backend-flights/log.request.info`
- **Valor**: `true` ou `false`
- **Descrição**: Habilita logs detalhados das requisições para debugging

Urls Consul:

- [Consul TI](http://consul-dev.services.cvc.com.br:8500/ui/consul-dev/kv/sub-backend-flights/)
- [Consul QA](http://consul-qa.services.cvc.com.br:8500/ui/consul-qa/kv/sub-backend-flights/)
- [Consul PROD](http://consul-prod.services.cvc.com.br:8500/ui/consul-prod/kv/sub-backend-flights/)

## Cache Service

- [Url Download mongo](https://www.mongodb.com/try/download/community-kubernetes-operator)
- [Url Download redis](https://redis.io/download)

- Necessário instalar o mongo server localmente para rodar o projeto local.

- Hoje é utilizado o cache para armazenar os dados da pesquisa (asyncstream) para ser utilizado posteriormente no UpsellOrCheckoutData.
- Utiliza MongoDB para armazenar os dados, existe uma configuração para o REDIS também, mas não está em uso.
- A cheve de ativação do MONGO/REDIS é `sub-backend-flights/cacheType` = `MONGO` ou `REDIS`
- A chave de configuração para o MongoDB é `sub-backend-flights/mongo.connectionData` que deve conter:

  ```json
  {
    "server": "iz8itxfl.adb.sa-saopaulo-1.oraclecloudapps.com:27017",
    "isAutonomous": true
  }
  ```

- As credenciais do MongoDB são obtidas do ambiente (Vault):
  - `MONGODB_USERNAME_OCI`
  - `MONGODB_PASSWORD_OCI`
  
- A chave de configuração para o REDIS é `sub-backend-flights/redis.url`

## Logs

- Foram incluidos logs na inicialização do projeto para facilitar a identificação de problemas. Para visualizar os logs, basta acessar o Kubdash e procurar pelo índice `LOG-INFO` ou `LOG-ERROR`.

Urls Kubdash:

- [Kubdash TI](https://kubedash.k8s-ti-cvc.com.br/#/pod?namespace=sub-flights)
- [Kubdash QA](https://kubedash.k8s-qa-cvc.com.br/#/pod?namespace=sub-flights)
- [Kubdash PROD](https://kubedash.k8s-cvc.com.br/#/pod?namespace=sub-flights)

Também foram incluidos `logInformation` para facilitar a identificação de problemas no request.
Chave de configuração para habilitar logs no request: `sub-backend-flights/log.request.info` = `true`, com essa configuração habilitada vai logar a informação da url do endpoint e seu request body,
junto com essas informações vão estar presentas as propriedades `searchId` e `transactionId` para facilitar a identificação do request com o frontend.

Essa configuração está no arquivo `LogHelper.cs` na pasta `Util`.

Nos arquivos de configuração dos logs `appsettings.qa.json` e `appsettings.prod.json` estão as configurações para os logs do projeto.

Foi setado o log level para `Information` para os logs do projeto, e para os logs do `System.Net.Http` e `Microsoft` foi setado para `Error`. Isso porque esses logs são muito poluídos e não são necessários para a identificação de problemas.

No POD (Kubdash) de produção ainda está como `Error` para os logs do projeto, precisa alterar para `Information` e adicionar os `System.Net.Http` e `Microsoft` como `Error`.

- [Log no .NET Core e no ASP.NET Core](https://learn.microsoft.com/pt-br/aspnet/core/fundamentals/logging/?view=aspnetcore-3.1)

```json
{
  "ASPNETCORE_ENVIRONMENT": "prod",
  "Logging_Console_LogLevel_Default": "Information",
  "Logging_GELF_LogLevel_Default": "Error",
  "Logging_GELF_LogLevel_Microsoft": "Error",
  "Logging_GELF_LogLevel_System.Net.Http": "Error",
  "PROJECT_NAME": "sub-backend-flights"
}
```

O Gelf foi configurado para enviar os logs para o logstash, e a chave de configuração para o logstash é `AppSettings:Gelf.Url`.

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Error",
      "System.Net.Http": "Error"
    },
    "Console": {
      "LogLevel": {
        "Default": "Information"
      }
    },
    "GELF": {
      "LogLevel": {
        "Default": "Information",
        "Microsoft": "Error",
        "System.Net.Http": "Error"
      }
    }
  }
}
```

- Alguns logs estão desabilitados no código para não poluir o ambiente, caso seja necessário habilitar, basta descomentar as linhas com `[log-disabled]`.
- `AppSettings:Gelf.Url` é a chave de configuração para o logstash, onde os logs são enviados.

## Serviços externos

**locations-api** - Serviço que retorna as localidades de acordo com a query passada.

- [Repositorio Api Locations](https://git.cvc.com.br/Desenvolvimento-SOA/GTW_LAMBDA_LOCATIONS)

A url do serviço está configurada na chave `sub-backend-flights/locations.url` no consul.

Exemplo de request:

```bash
curl -X GET 'https://s2mdm1pq2i.execute-api.sa-east-1.amazonaws.com/b2c_qa/locations?productType=AIR&q=rio%20de%20janeiro' -H 'Connection: keep-alive'
-H 'Date: Tue, 04 Jun 2024 12:40:26 GMT'
-H 'x-amzn-RequestId: 2992fa46-c3f2-4785-871d-fa092231d71c'
-H 'Access-Control-Allow-Origin: *'
-H 'x-amz-apigw-id: Y16xKG27GjQEQ8w='
-H 'X-Amzn-Trace-Id: Root=1-665f0b3a-260207d17ec1c8b851b43f7e;Parent=31522087f2abd439;Sampled=0;lineage=36accebc:0'
-H 'X-Cache: Miss from cloudfront'
-H 'Via: 1.1 2e5f15b69d5f9af75749c48642a17b50.cloudfront.net (CloudFront)'
-H 'X-Amz-Cf-Pop: GRU3-C2'
-H 'X-Amz-Cf-Id: lVfeR73I7I_RnYzbD4pRDuw63roBpzq1O6coSe-mC4UhKklKGhjqRg==' '
```

---

**Corp login** - Serviço que retorna os dados do usuário logado e valida o token.

A url do serviço está configurada na chave `sub-backend-flights/corpLogin.url` no consul.

Exemplo de request:

```bash
https://api-springboot-corp-auth.k8s-qa-cvc.com.br/v1/auths/token/decode-token?token=eyJ4NXQjUzI1NiI6ImExUG9xdHpROVVQNjdpVUlkSzdsbXRQV2oyLTg5THBOMUZTN3FUa3RwOFEiLCJraWQiOiJjb3JwYXV0aG9yaXphdGlvbnNlcnZlci1kZXYudjIiLCJhbGciOiJSUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.eBnP0NwSDEwNEyBkGT16o-G5tf7aDU-EaEwcR6-8SX13e7otkVZcz9wK1_UvUoAGjg7qlL9-gkk_0Cat4-sgNlq2HmIaBTqtHcrt4rcCnTNhNHqN2EvfSa42sDfD47ObY73anKJcxJLvAXAVNmyq8L94u7xcnF9YsFeUuhgezX04PPuWD6Xfm1hoSV9ll8UKpFaACzjn0B6_7yvtGB_9FnTToxCu1iBJI7bcescd1rPZ5nh6mERUyYL8mSRPXDZO6YZZq1a7lQiVb8z_IUZzy5bNWi8-YOMyRf8tcpuU_Sw8ggC9eVUfTJMe5nQzTk1e3pMvA2TtRXGb6z8JRGa0Wg"
```

---

**Importa Loc** - Serviço que importa as informações do localizador das cias aéreas.

As chaves de configuração estão no consul.

`sub-backend-flights/importalocSysturValidator` - Chave de configuração para habilitar a validação do localizador da cia aérea.

`sub-backend-flights/importalocValidateUrl` - Chave de configuração para a url do serviço de validação do localizador da cia aérea.

Exemplo de request para o gateway de aéreo:

```bash
curl -X GET 'https://internal-search-lojas-prod.reservafacil.tur.br/gwaereo/v0/flights/bookings/SGOL/ODZEDK?packageGroup=STANDALONE&preferences=showTicketsInfo' -H 'Gtw-Username: lojas_wl'
-H 'Gtw-Password: gtRE_loj_&%20#@wl'
-H 'Gtw-Branch-Id: 1002'
-H 'Gtw-Agent-Sign: LOJ'
-H 'Accept-Encoding: gzip' '
```

Após a validação do localizador da cia aérea, é feita uma requisição para uma lambda que valida o LOC.
Obs: Essas são as informações que temos dela, pois herdamos o projeto e não temos muitas informações sobre ela.

- Nome da Lambda `B2B_Flights_Handler`
- [Gateway que chama a Lambda](https://sa-east-1.console.aws.amazon.com/apigateway/main/apis/askl22ffzc/resources?api=askl22ffzc)
- [Repositorio Lambda](https://git.cvc.com.br/Desenvolvimento-SOA/GTW_LAMBDA_FLIGHTS/-/blob/master/lib/handlers/check-list-air.js)
- Responsáveis: Lambda: [`Christiano Teodoro Ferreira Gomes`] | Infra: [`Claudinei Cleber Bagatoli`]

Aparentemente a Lambda consome os serviços abaixo para validar o LOC.

- `http://www.cvc.com.br/CVCWSCheckListAereo/`
- Requisição SOAP PROD: `http://cvcosbprod01.cvc.com.br:7011/`
- Requisição SOAP QA: `http://cvcosbhom01.cvc.com.br:7011/`

Exemplo de request para a lambda que valida o localizador da cia aérea:

```bash
curl -X GET 'https://askl22ffzc.execute-api.sa-east-1.amazonaws.com/b2b_prd/flights/checkList/?gatewayOrigin=VCRA&codigoEmpresa=2&importacao=S&codigoFilial=1002&localizador=ODZEDK&packageGroup=STANDALONE&source=SGOL' -H 'gtw-sec-user-token: eyJ4NXQjUzI1NiI6IkdNcmtZeEgxdFdqelBqTFNlNl9xZmdVdkxncVRtU3FaeGdTTGZmcDF3Q0EiLCJraWQiOiJjb3JwYXV0aG9yaXphdGlvbnNlcnZlci52MiIsImFsZyI6IlJTMjU2In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.REyjS5qtcL0kFgNQwgBRL2j6qe_xDItQo_hsHykFnsbaKJ6NKfS6ZgmWgPvlLwTBNWwApA_3PGlzKyqvO731twhLf7vYJ5BkSrJAbQ3Uw183zFSTmz2MjhkTQ4ICzwdaEGTZbOqYZKvyWd5B7v5r33FzpgK--R-DSFKTn2I5eNLlIhPGxyhcVOU_xKA11-kIYmRJw3PwmwLYm597mNW2UWa_9mgh8mqRaQN_5vhm4v8KDlKce4paF0OnZfUCPqhlnjcePDmUSreYMEmkYCwGL81extWIge7UibaeQq3PkHFZztoCMHOXxUS42Ex1U3ZfMvgwOZqYQJw9yzd9C_Wtsg'
-H 'gtw-transaction-id: atlas-e5f76d60-8f69-4d72-ac0e-0ebc4fb175d4' '
```

## Substituição das Lambdas por Serviços .NET (Update 02/2025 )

### Overview

Com a retirada dos serviços da AWS Lambda, foi necessário migrar as funcionalidades para serviços .NET. Neste projeto, foram substituídas as seguintes Lambdas:

---

### **1. CheckListAirService** (Substitui a Lambda de Validação de LOC)

#### **Finalidade**

- Valida um localizador (LOC) utilizando um endpoint SOAP.
- Substitui a chamada original para a Lambda `B2B_Flights_Handler`.

#### **Configuração**

- Obtida via Consul: `sub-backend-flights/importaloc.config`
- Endpoint:

  ```
  http://cvcosbhom01.cvc.com.br:7011/CVCWSCheckListAereo
  ```

- Query Parameters fixos:

  ```json
  {
    "gatewayOrigin": "VCRA",
    "codigoEmpresa": "2",
    "importacao": "S"
  }
  ```

- Exemplo de Chamada:

  ```bash
  curl -X GET 'http://cvcosbhom01.cvc.com.br:7011/CVCWSCheckListAereo?gatewayOrigin=VCRA&codigoEmpresa=2&importacao=S&localizador=ODZEDK&source=SGOL&packageGroup=STANDALONE&codigoFilial=1002'
  ```

---

### **2. PaymentCredipaxService** (Substitui a Lambda de Validação de Pagamentos)

#### **Finalidade**

- Valida pagamentos e verifica se a reserva já existe no sistema Systur.
- Substitui a chamada original para a Lambda `B2B_Proxy_Handler`.

#### **Configuração**

- Obtida via Consul: `sub-backend-flights/importaloc.config`
- Endpoint:

  ```
  http://systurhom.cvc.com.br/pls/systur/pkg_aer_validacao.prc_consulta_ficha_json
  ```

- Exemplo de Chamada:

  ```bash
  curl -X GET 'http://systurhom.cvc.com.br/pls/systur/pkg_aer_validacao.prc_consulta_ficha_json?P_LOCALIZADOR=ODZEDK&P_CD_FILIAL=1002'
  ```

---

### **Consul Keys**

As chaves de configuração estão armazenadas no Consul sob `sub-backend-flights/importaloc.config`. Exemplo:

```json
{
  "useNewService": true,
  "endpoints": {
    "checklist": {
      "url": "http://cvcosbhom01.cvc.com.br:7011",
      "path": "CVCWSCheckListAereo",
      "fixedQueryParams": {
        "gatewayOrigin": "VCRA",
        "codigoEmpresa": "2",
        "importacao": "S"
      }
    },
    "credipax": {
      "url": "http://systurhom.cvc.com.br/pls/systur",
      "path": "pkg_aer_validacao.prc_consulta_ficha_json",
      "fixedQueryParams": {}
    }
  }
}
```

---

**Gateway Aéreo** - Serviço que retorna os dados dos voos disponíveis.
Obs: Existe uma lista de sources que está configurada na chave `sub-backend-flights/gateway.sources`. É feita uma requisição por source para o GTW de Aéreo.

A url do serviço do GTW de Aéro está configurada na chave `platform/brand-contexts/flights` `GatewayUrl` no consul.
Essas configurações estão separadas por ambiente/contexto.

Exemplo de request:

```bash
curl -X GET 'https://internal-search-cvc-hom.reservafacil.tur.br/gwaereo/v0/flights?ages=30%2c30&routes=RIO%2cMIA%2c2024-07-02%2bMIA%2cRIO%2c2024-07-17&packageGroup=PACKAGE&businessClass=ALSO&preferences=persistLog,loadInstallments:true,language:pt_BR,currency:BRL&source=AMD' -H 'Gtw-Username: lojas_wl'
-H 'Gtw-Password: lojas_wl'
-H 'Gtw-Branch-Id: 1002'
-H 'Gtw-Agent-Sign: LOJ'
-H 'Accept-Encoding: gzip'
-H 'searchId: 4e29454e-5e5f-4dd5-9a26-f46996ed0f94' '
```

As informações do Gtw-Username, Gtw-Password, Gtw-Branch-Id e Gtw-Agent-Sign são passadas no header da requisição.
Caso não tenha essas informações, é pego do consul na chave `platform/brand-contexts/flights` objeto `GatewayHeaders`. (São separados por contexto, site, lojas, etc).

## Fluxo de pesquisa de um voo pelo site

1. O usuário faz uma requisição para o endpoint `asyncstream` que pesquisa os voos disponíveis.
2. O serviço de pesquisa de voos faz uma requisição por source para o GTW de Aéreo.
3. O GTW de Aéreo retorna os dados dos voos disponíveis e salva os dados da pesquisa no cache. (`MongoDB`)
4. Ao adicionar no carrinho é feita uma requisição para o endpoint de `upsellOrCheckoutData`.
5. O serviço de pesquisa de voos busca os dados da pesquisa no cache. (MongoDB) e retorna os dados da pesquisa dizendo se existe upsell ou não.
