using Backend.Flights.Models.Matriz3Dias;
using Backend.Flights.Models.Search;
using Backend.Flights.Services;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Backend.Flights.Controllers
{
    [ApiController, ApiVersion("1.0"), Route("api/[controller]")]
    public class CalendarController : Controller
    {
        private readonly ILogger<CalendarController> _logger;
        private readonly IAirGtw _airGatewayService;

        public CalendarController(ILogger<CalendarController> logger, IAirGtw gtwService)
        {
            _logger = logger;
            _airGatewayService = gtwService;
        }


        [EnableCors("AllowAnyOrigin"), HttpPost("{year}/{month}")]
        public async Task<object> MatrixAsyncSearch([FromBody] CalendarShopRequest request,
            [FromServices] IContextBrand contextBrand, int year, int month)
        {
            if (IsInvalidCityPairQuantity(request.CityPairs) ||
                HasInvalidPassengersQuantity(request?.Infants ?? 0,
                    request?.Children ?? 0, request.Adults) ||
                IsInvalidMonth(month) ||
                IsThreeYearsAheadOrPassedYear(year) ||
                IsPassedMonthAlready(year, month))
            {
                var resultError = Json(new { error = true, message = "Invalid arguments!" });
                resultError.StatusCode = 400;
                return resultError;
            }

            try
            {
                var firstDate = request.CityPairs[0].DepartureDate;
                var diffDays = firstDate.Day - 16;
                var diffMonths = firstDate.Month - month;
                var diffYears = firstDate.Year - year;

                request.CityPairs[0].DepartureDate = new DateValues()
                {
                    Day = 16,
                    Month = month,
                    Year = year,
                };

                foreach (var cp in request.CityPairs.Skip(1))
                {
                    cp.DepartureDate = new DateValues()
                    {
                        Day = cp.DepartureDate.Day - diffDays,
                        Month = cp.DepartureDate.Month - diffMonths,
                        Year = cp.DepartureDate.Year - diffYears,
                    };
                }

                var result = await _airGatewayService.CalendarShop(request, contextBrand, 15);

                return Json(FormatResponse(result, year, month));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Fail on matrix search");
                var resultError = Json(new
                {
                    error = true,
                    message = "Fail on matrix search",
#if DEBUG
                    detaill = ex.ToString()
#endif
                });
                resultError.StatusCode = 500;
                return resultError;
            }
        }

        private bool IsInvalidCityPairQuantity(List<CityPair> cities) => cities.Count < 1;

        private bool HasInvalidPassengersQuantity(int infants, int children, int adults)
        {
            return infants < 1 &&
                   children < 1 &&
                   adults < 1;
        }

        private bool IsInvalidMonth(int month) => month < 1 || month > 12;

        private bool IsThreeYearsAheadOrPassedYear(int year) =>
            year > DateTime.Now.Year + 2 || year < DateTime.Now.Year;

        private bool IsPassedMonthAlready(int year, int month) =>
            year == DateTime.Now.Year && month < DateTime.Now.Month;

        public static decimal?[] FormatResponse(MatrixResponse resp, int year, int month)
        {
            var yearMonth = $"{year:0000}-{month:00}-";
            return resp.DepartureDate
                .Where(d => d.Date.StartsWith(yearMonth))
                .Select(departureInfo =>
                (
                    departureInfo.ReturningDate.Count > 15
                        ? departureInfo.ReturningDate.Skip(15)
                        : departureInfo.ReturningDate
                ).FirstOrDefault()?.FareGroup?.Select(
                    fareGroup => fareGroup?.PriceWithTax)?.Min())
                .ToArray();
        }
    }
}