using System;
using System.Text.RegularExpressions;

namespace Backend.Flights.Util
{
    /// <summary>
    /// Utilitário para mascarar informações sensíveis em strings de conexão
    /// </summary>
    public static class ConnectionStringMasker
    {
        /// <summary>
        /// Ofusca a senha na string de conexão do MongoDB para logs seguros
        /// </summary>
        /// <param name="connectionString">String de conexão original</param>
        /// <returns>String de conexão com senha ofuscada</returns>
        public static string MaskMongoDbPassword(string connectionString)
        {
            if (string.IsNullOrEmpty(connectionString))
                return "[String de conexão mascarada]";

            try
            {
                // Regex para capturar o padrão ********************************
                // Substitui apenas a senha por asteriscos
                var maskedString = Regex.Replace(
                    connectionString,
                    @"(mongodb://[^:]+:)[^@]+(@)",
                    "$1*****$2"
                );

                // Se o regex não encontrou nada, retorna string mascarada
                return maskedString == connectionString ? "[String de conexão mascarada]" : maskedString;
            }
            catch
            {
                // Em caso de erro, retorna string mascarada
                return "[String de conexão mascarada]";
            }
        }

        /// <summary>
        /// Ofusca a senha em qualquer string de conexão que siga o padrão protocolo://usuario:senha@servidor
        /// </summary>
        /// <param name="connectionString">String de conexão original</param>
        /// <returns>String de conexão com senha ofuscada</returns>
        public static string MaskPassword(string connectionString)
        {
            if (string.IsNullOrEmpty(connectionString))
                return "[String de conexão mascarada]";

            try
            {
                // Regex genérico para capturar o padrão protocolo://usuario:senha@servidor
                var maskedString = Regex.Replace(
                    connectionString,
                    @"([a-zA-Z]+://[^:]+:)[^@]+(@)",
                    "$1*****$2"
                );

                // Se o regex não encontrou nada, retorna string mascarada
                return maskedString == connectionString ? "[String de conexão mascarada]" : maskedString;
            }
            catch
            {
                // Em caso de erro, retorna string mascarada
                return "[String de conexão mascarada]";
            }
        }
    }
}