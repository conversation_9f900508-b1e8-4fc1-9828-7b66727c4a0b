using Backend.Flights.CA.Services.Config;
using Backend.Flights.CA.Services.Json;
using Backend.Flights.CA.UseCase.Config;
using Backend.Flights.Util;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Backend.Flights.CA.Services.Cache
{
    public class MongoCache : ICacheService
    {
        public class Connection
        {
            public string Server { get; set; }
            public string User { get; set; }
            public string Pwd { get; set; }
            public string Database { get; set; }
            public bool IsAWS { get; set; }
            public bool IsAutonomous { get; set; } // Oracle Autonomous JSON Database

            public string BuildConnectionString()
            {
                var userPwd = $"{this.User}:{this.Pwd}";
                userPwd = userPwd == ":" ? "" : $"{userPwd}@";

                // #if LOCAL // LOCALPRODUCAO (Local Produção)
                // //return $"mongodb://{userPwd}localhost:27017/{this.Database}?authSource=admin";
                // return $"mongodb://{this.User}:{this.Pwd}@{this.Server}/" +
                // $"{this.User}?authMechanism=PLAIN&authSource=$external&ssl=true&retryWrites=false&loadBalanced=true";
                // #endif

                if (IsAutonomous)
                {
                    // Pegando as credenciais do Vault quando for Autonomous
                    this.User = Environment.GetEnvironmentVariable("MONGODB_USERNAME_OCI");
                    this.Pwd = Environment.GetEnvironmentVariable("MONGODB_PASSWORD_OCI");

                    if (string.IsNullOrEmpty(this.User) || string.IsNullOrEmpty(this.Pwd))
                    {
                        throw new Exception("MongoDB credentials not found in Vault");
                    }

                    return $"mongodb://{this.User}:{this.Pwd}@{this.Server}/" +
                           $"{this.Database}?authMechanism=PLAIN&authSource=$external&ssl=true&retryWrites=false&loadBalanced=true";
                }
                else
                {
                    // Configuração tradicional para MongoDB normal
                    if (!IsAWS)
                    {
                        return $"mongodb+srv://{userPwd}{this.Server}/{this.Database}?authSource=admin&retryWrites=true&w=majority";
                    }
                    else
                    {
                        return $"mongodb://{userPwd}{this.Server}/{this.Database}?authSource={this.Database}&readPreference=primary&appname=sub-backend-flights&ssl=true";
                    }
                }
            }
        }

        private class CacheData
        {
            [BsonRepresentation(BsonType.ObjectId)]
            public string _id { get; set; }
            public string key { get; set; }
            public string value { get; set; }
            public DateTime expiration { get; set; }
        }

        private readonly IJsonService _json;
        private readonly IWatchConfig _config;

        private SemaphoreSlim _lock = new SemaphoreSlim(1);
        private IMongoCollection<CacheData> _cache;

        public MongoCache(IJsonService json, IWatchConfig config)
        {
            _json = json;
            _config = config;
        }

        private void CreateClient(string config)
        {
            var connData = _json.Deserialize<Connection>(config);
            var tsl = true;

            // #if LOCAL // LOCALPRODUCAO (Local Produção)
            // // connData = new Connection { Server = "localhost:27017", User = "", Pwd = "", Database = "db-flights", IsAWS = false };
            // connData = new Connection { Server = "iz8itxfl.adb.sa-saopaulo-1.oraclecloudapps.com:27017", User = "db_flights", Pwd = "joAW2gPg0Se2dsQHzZx0Ny79j", Database = "db_flights", IsAWS = false, IsAutonomous = true };
            // // connData = new Connection { Server = "mapa01-qa.addqm.mongodb.net", User = "flights", Pwd = "flights123", Database = "db-flights", IsAWS = false };
            // tsl = false;
            // #endif

            var connectionString = connData.BuildConnectionString();
            $"[MongoCache]: String de Conexão: {ConnectionStringMasker.MaskMongoDbPassword(connectionString)}".LogInfo();

            //var settings = MongoClientSettings.FromUrl(new MongoUrl(connectionString));
            //settings.AllowInsecureTls = tsl;

            var client = new MongoClient(connectionString);

            // No Oracle Autonomous JSON Database, o nome do banco é o mesmo do usuário
            var databaseName = connData.IsAutonomous ? connData.User : connData.Database;
            var database = client.GetDatabase(databaseName);

            _cache = database.GetCollection<CacheData>("rateTokensAndFareProfiles");
            var keys = Builders<CacheData>.IndexKeys;

            if (!connData.IsAutonomous)
            {
                "[CacheServiceMongo]: Criando índices no MongoDB normal.".LogInfo();

                _cache.Indexes.CreateMany(new[] {
                    new CreateIndexModel<CacheData>(keys.Ascending(x => x.key), new CreateIndexOptions { Name = "key", Unique = true }),
                    new CreateIndexModel<CacheData>(keys.Ascending(x => x.expiration), new CreateIndexOptions { Name = "expirationIndex", ExpireAfter = TimeSpan.Zero }) // TTL Index
                });
            }
            else
            {
                // Foi comentado porque no inicio não estava suportando esses index, depois de uma atualização do pessoal começou a suportar
                //"[CacheServiceMongo]: Pulando criação do índice TTL no Oracle Autonomous JSON Database.".LogInfo();
                // Criar apenas índice único no Oracle Autonomous
                //_cache.Indexes.CreateOne(new CreateIndexModel<CacheData>(keys.Ascending(x => x.key), new CreateIndexOptions { Name = "key", Unique = true }));

                "[CacheServiceMongo]: Criando índices no Oracle Autonomous JSON Database.".LogInfo();

                _cache.Indexes.CreateMany(new[] {
                    new CreateIndexModel<CacheData>(keys.Ascending(x => x.key), new CreateIndexOptions { Name = "key", Unique = true }),
                    new CreateIndexModel<CacheData>(keys.Ascending(x => x.expiration), new CreateIndexOptions { Name = "expirationIndex", ExpireAfter = TimeSpan.Zero }) // TTL Index
                });
            }

        }

        private async Task<IMongoCollection<CacheData>> GetClient()
        {
            if (_cache == null)
            {
                await _lock.WaitAsync();
                try
                {
                    if (_cache == null)
                    {
                        _config.ConfigChanged += (_, d) =>
                        {
                            if (d.Name == IConfigService.MONGO_CONNECTION_DATA)
                            {
                                CreateClient(d.Value);
                            }
                        };
                        var config = await _config.GetConfig(IConfigService.MONGO_CONNECTION_DATA);
                        CreateClient(config);
                    }
                }
                finally
                {
                    _lock.Release();
                }
            }

            return _cache;
        }

        public async Task<string[]> GetItems(IEnumerable<string> ids)
        {
            var cli = await GetClient();
            if (!ids.Any())
            {
                return Array.Empty<string>();
            }

            var filter = new FilterDefinitionBuilder<CacheData>().In(x => x.key, ids);
            var mongoData = (await cli.FindAsync(filter))
                .ToEnumerable()
                .ToDictionary(x => x.key, x => x.value);
            return ids
                .Select((key) => mongoData.TryGetValue(key, out var value) ? value : null)
                .ToArray();
        }

        public async Task<string> GetItem(string id)
        {
            var cli = await GetClient();

            var filter = new FilterDefinitionBuilder<CacheData>().Eq(x => x.key, id);
            var mongoData = (await cli.FindAsync(filter))
                .ToEnumerable()
                .ToDictionary(x => x.key, x => x.value);
            return mongoData.TryGetValue(id, out var value) ? value : null;
        }

        public async Task SetItem(string id, string value, int ttlSeconds = 1260)
        {
            var cli = await GetClient();

            var filter = new FilterDefinitionBuilder<CacheData>().Eq(x => x.key, id);
            await cli.DeleteManyAsync(filter);
            await cli.InsertOneAsync(new CacheData()
            {
                key = id,
                value = value,
                expiration = DateTime.Now.AddSeconds(ttlSeconds)
            });
        }
    }
}
