using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;

namespace Backend.Flights.Util
{
    public class CurlLoggingMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<CurlLoggingMiddleware> _logger;
        private readonly ConfigurationManager _config;

        public CurlLoggingMiddleware(RequestDelegate next, ILogger<CurlLoggingMiddleware> logger, ConfigurationManager config)
        {
            _next = next;
            _logger = logger;
            _config = config;
        }

        public async Task Invoke(HttpContext context)
        {
            var operation = string.Empty;

            try
            {
                // Registra as informações de log apenas se a chave [log.request.info] estiver habilitada no Consul
                if (_config.AppSetting.TryGetValue("LogRequestInfo", out string logEnabled) && logEnabled.ToLower() == "true")
                {
                    var request = context.Request;
                    operation = context.Request.Path.Value?.Split('/').LastOrDefault()?.ToLower() ?? string.Empty;
                    var endpointsToIgnore = new List<string> { "/Log", "/Health", "/index.html", "/promotions" };
                    var requestBody = string.Empty;

                    // Verifica se o endpoint corrente está na lista de endpoints a ignorar
                    var ignoreEndpoint = string.IsNullOrEmpty(operation) || endpointsToIgnore.Any(endpoint => request.Path.Value?.Contains(endpoint, StringComparison.OrdinalIgnoreCase) == true);

                    if (!ignoreEndpoint)
                    {
                        string pathPrefix;
#if LOCAL
                        pathPrefix = "";
#else
                        pathPrefix = "/api/flights";
#endif

                        var fullPath = request.Path.ToString().Replace("/api", "", StringComparison.OrdinalIgnoreCase);

                        var endpoint = $"{request.Scheme}://{request.Host}{pathPrefix}{fullPath}{request.QueryString}";

                        var curlCommand = new StringBuilder($"curl -X {request.Method} \"{endpoint}\"");

                        List<string> headersToIgnore = new List<string>
                            {
                                "Connection",
                                "Pragma",
                                "Cache-Control",
                                "Sec-Fetch-Dest",
                                "Sec-Fetch-Mode",
                                "Sec-Fetch-Site",
                                "Sec-CH-UA",
                                "Sec-CH-UA-Mobile",
                                "Sec-CH-UA-Platform",
                                //"Cookie", // Utilizado
                                "Content-Length",
                                "Host",
                                "User-Agent",
                                "X-Dtpc",
                                "priority",
                                "Postman-Token",
                                "x-instana-l",
                                "x-instana-s",
                                "x-instana-t",
                                "X-Varnish",
                                "CDN-Loop",
                                "Incap-Client-IP",
                                "X-Request-ID",
                                "X-Real-IP",
                                "X-Forwarded-For",
                                "X-Forwarded-Host",
                                "X-Forwarded-Port",
                                "X-Forwarded-Proto",
                                "X-Forwarded-Scheme",
                                "X-Scheme",
                                "X-Original-Forwarded-For",
                                "traceparent",
                                "tracestate",
                                "X-Instana-T",
                                "X-Instana-S",
                                "X-dynaTrace-Application",
                                "X-dynaTrace-RequestState",
                                "X-dynaTrace",
                                "X-Ruxit-Forwarded-For"
                            };

                        // Adiciona os headers
                        foreach (var header in request.Headers)
                        {
                            if (!headersToIgnore.Contains(header.Key, StringComparer.OrdinalIgnoreCase))
                                curlCommand.Append($" -H \"{header.Key}: {header.Value}\"");
                        }

                        // Adiciona o corpo da requisição
                        if (request.Method == HttpMethods.Post || request.Method == HttpMethods.Put || request.Method == HttpMethods.Patch || request.Method == HttpMethods.Delete)
                        {
                            request.EnableBuffering();
                            using (var reader = new StreamReader(request.Body, Encoding.UTF8, true, 1024, true))
                            {
                                requestBody = await reader.ReadToEndAsync();
                                if (!string.IsNullOrEmpty(requestBody))
                                {
                                    // Deserializa e serializa o corpo da requisição para remover espaços em branco e formatação
                                    requestBody = JsonSerializer.Serialize(JsonSerializer.Deserialize<object>(requestBody));
                                    curlCommand.Append($" -d '{requestBody}'");
                                    request.Body.Position = 0;
                                }
                            }
                        }

                        // Trunca o comando curl se ele exceder o comprimento máximo permitido no logstash
                        var maxLength = 30000; //32766;
                        if (curlCommand.Length > maxLength)
                        {
                            curlCommand.Length = maxLength - 3;
                            curlCommand.Append("...");
                        }

                        // Adiciona informações adicionais
                        var transactionId = context.GetCookieHeaderValues("transactionId");

                        // Se o transactionId estiver nulo ou vazio, tenta pegar do header
                        if (string.IsNullOrEmpty(transactionId))
                        {
                            transactionId = context.Request.Headers["transactionId"].FirstOrDefault();
                        }

                        var searchId = GetPropertyValueIgnoreCase(requestBody, "SearchId");
                        searchId = string.IsNullOrEmpty(searchId) ? context.GetHeaderValues("SearchId") : searchId;

                        using (_logger.BeginScope(new Dictionary<string, object>
                        {
                            ["metadata"] = curlCommand.ToString(),
                            ["operation"] = operation,
                            ["transactionId"] = transactionId,
                            ["searchId"] = searchId
                        }))
                        {
                            _logger.LogInformation("[LogRequestInfo] - Informações do Request");
                        }

#if LOCAL
                        Console.WriteLine($"[CurlLoggingMiddleware] - Curl Command: {curlCommand}");
#endif

                    }
                }
            }
            catch (JsonException jsonEx)
            {
                LogErrorInformation(operation, "[CurlLoggingMiddleware] - JSON Error while logging request info:", jsonEx);
            }
            catch (IOException ioEx)
            {
                LogErrorInformation(operation, "[CurlLoggingMiddleware] - IO Error while logging request info:", ioEx);
            }
            catch (Exception ex)
            {
                LogErrorInformation(operation, "[CurlLoggingMiddleware] - General Error while logging request info:", ex);
            }

            // Chama o próximo middleware
            await _next(context);
        }

        private void LogErrorInformation(string operation, string message, Object exception)
        {
            using (_logger.BeginScope(new Dictionary<string, object>
            {
                ["short_message"] = "error",
                ["operation"] = operation,
                ["exception"] = exception
            }))
            {
                _logger.LogInformation(message);
            }
        }

        private string GetPropertyValueIgnoreCase(string jsonString, string propertyName)
        {
            if (string.IsNullOrEmpty(jsonString) || string.IsNullOrEmpty(propertyName))
                return null;

            var jObject = JObject.Parse(jsonString);

            // Tenta encontrar a propriedade com o nome exato
            var token = jObject.SelectToken(propertyName);

            // Se não encontrar, tenta encontrar ignorando maiúsculas e minúsculas
            if (token == null)
            {
                token = jObject.Descendants()
                    .OfType<JProperty>()
                    .FirstOrDefault(p => p.Name.Equals(propertyName, StringComparison.OrdinalIgnoreCase))
                    ?.Value;
            }

            return token?.ToString() ?? string.Empty;
        }
    }
}