
using Backend.Flights.CA.Model.Cart;
using Backend.Flights.CA.Model.Opportunities;
using Backend.Flights.CA.Model.Promotions;
using Backend.Flights.CA.UseCase.Opportunities;
using Backend.Flights.CA.UseCase.Opportunities.Service;
using System.Threading.Tasks;

namespace Backend.Flights.CA.UseCase.Promotions
{
    public class RequestPromotions : IRequestPromotions
    {
        private readonly IGetOpportunities _getOpportunities;
        private readonly IGetPromotionsIndividuals _getPromotionsIndividuals;

        public RequestPromotions(
            IGetOpportunities getOpportunities,
            IGetPromotionsIndividuals getPromotionsIndividuals)
        {
            _getOpportunities = getOpportunities;
            _getPromotionsIndividuals = getPromotionsIndividuals;
        }

        public async Task<PromotionsResponse> Execute(Model.Context.BrandContext brand, string userToken, OpportunitiesRequestItem[] availableItems, CartItem[] selectedItems, OpportunitiesRequest.RequestParams requestParams = null, bool isProfitSplit = false)
        {
            if (isProfitSplit == false && (brand.PromotionsActive != true || availableItems == null || availableItems.Length == 0))
            {
                return null;
            }

            if (isProfitSplit && (selectedItems == null || selectedItems.Length <= 0 || requestParams == null))
            {
                return null;
            }

            var opportunities = await _getOpportunities.Execute(availableItems, selectedItems, userToken, requestParams, isProfitSplit);

            return opportunities == null ? null : _getPromotionsIndividuals.Execute(opportunities);
        }
    }
}