# Release Notes

## [1.3.40] - 2024-05-29

### Changed

- Foram alterados os arquivos `appsettings.*.json`. Agora o `appsettings.*.json` está com o log level `Information` para os logs do projeto, e para os logs do `System.Net.Http` e `Microsoft` foi setado para `Error`. Isso porque esses logs são muito poluídos e não são necessários para a identificação de problemas.
- Produção ainda está como `Error` para os logs do projeto, `System.Net.Http` e `Microsoft`.

### Updated

- Atualizado o `README.md` da aplicação com informações sobre a nova versão.

### Added

- **Melhoria nos logs aplicação**
  - **Log de inicialização da aplicação**: Adicionado console logs na inicialização do aplicativo para melhorar a depuração. Se pesquisa por `LOG-INFO` ou `LOG-ERROR` no Ku<PERSON>dash ou `LogInfo` ou `LogError` na aplicação fica mais fácil de encontrar os logs.
    - Informações das chaves de configuração.
    - Informações de inicialização do banco de dados.
  
  - **Nova chave Consul**: `sub-backend-flights/log.request.info` para habilitar = `true`, caso contrario a aplicação entende que não deve logar as informações do request.

  - **LogInformation na chamada dos endpoints**: Adicionado a chave `sub-backend-flights/log.request.info` para habilitar logs no request. Com essa configuração habilitada vai logar a informação da url do endpoint e seu request body, junto com essas informações vão estar presentas as propriedades `searchId` e `transactionId` para facilitar a identificação do request com o frontend.

- **Outras melhorias nos logs**
  - Ajuste no retorno de erro do endpoint de promotions, agora vai logar no erro o request body.
  - Melhoria no [ContextBrandsService] quando não encontra a chave informada no x-brand-url. (Agora lista a chave usada e chaves disponiveis)

### Fixed

- **Ajuste na `[CheckoutData][CreateBaggageInfo]`**: pois em alguns resultados não estava vindo a informação do objeto services do GTW.
  - Antes estava quebrando a aplicação, agora não vai mais quebrar o app e vai jogar a informação default para bagagens (zero).

- **Chamada de promotions**
  - Ajuste para quando não retornava resultados no promotions, estava quebrando o codigo porque não tratava null. Quando isso acontecer estamos continuando com o fluxo normal e validando se tem promoção ou não, caso não tenha, ignoramos aquele registro.

### Removed

- Foram removidos a grande maioria dos `LogInformation` que existiam na aplicação, isso foi feito para melhorar o entendimento do código e evitar duplicidade de informações. Para saber quais foram desativados é só procurar por `[log-disabled]` no código.
