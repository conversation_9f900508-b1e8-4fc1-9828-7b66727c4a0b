﻿using Backend.Flights.Business;
using Backend.Flights.Business.Parsers;
using Backend.Flights.CA.Services.Http;
using Backend.Flights.CA.Services.Json;
using Backend.Flights.CA.UseCase.BrandContext;
using Backend.Flights.CA.UseCase.Promotions;
using Backend.Flights.Models;
using Backend.Flights.Models.Partners;
using Backend.Flights.Models.Search;
using Backend.Flights.Models.Search.Exclusive;
using Backend.Flights.Services;
using Backend.Flights.Util;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace Backend.Flights.Controllers
{
    public abstract class BaseSearchController : ControllerBase
    {
        protected readonly ILogger<Availability> _logger;
        protected readonly ConfigurationManager _config;
        private readonly ReturnCfg returnCfg;
        protected readonly ICacheService cache;
        protected readonly BrandContext _brandContext;
        protected readonly bool _enableContentProvider;
        protected readonly IContextBrands _contextBrands;
        protected readonly IHttpClient _httpClient;
        protected readonly IJsonService _json;

        public BaseSearchController(ILogger<Availability> logger, ConfigurationManager config, ICacheService cache, ReturnCfg returnCfg, IContextBrand brandContext, IContextBrands contextBrands, IHttpClient httpClient, IJsonService json, bool enableContentProvider = false)
        {
            _logger = logger;
            _config = config;
            this.returnCfg = returnCfg;
            this.cache = cache;
            _brandContext = brandContext.Context;
            _contextBrands = contextBrands;
            _enableContentProvider = enableContentProvider;
            _httpClient = httpClient;
            _json = json;
        }

        [EnableCors("AllowAnyOrigin"), HttpPost]
        public ActionResult<SearchResponse> Post(SearchRequest request)
        {
            try
            {
                var result = new SearchResponse() { SearchId = Guid.NewGuid().ToString(), Cookie = string.Empty };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        async private Task processingWrapper(string operation, SearchRequest request, Func<Availability, StreamWriter, ValueTask<int>> fnWriteData)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                Response.StatusCode = 200;
                Response.ContentType = "application/json";

                var sw = new StreamWriter(Response.Body);
                var av = new Availability(_logger, _config, cache, _brandContext, returnCfg, _enableContentProvider, _httpClient, _json);
                var totalResults = await fnWriteData(av, sw);
                await sw.FlushAsync();
                await sw.DisposeAsync();

                // [log-disabled]  if (_logger != null)
                //     finalLog(operation, stopwatch, request, "success", "Sucesso na busca", totalResults);
            }
            catch (Exception e)
            {
                if (_logger != null)
                    finalLog(operation, stopwatch, request, "error", $"{e.Message}", -1, e.StackTrace);
                await HttpResponseWritingExtensions.WriteAsync(Response, JsonConvert.SerializeObject(new { error = e.Message }));
            }
        }

        async private Task processingWrapper(string operation, SearchRequest request, Func<Availability, StreamWriter, ValueTask<(int totalResults, string searchCacheKey)>> fnWriteData)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                Response.StatusCode = 200;
                Response.ContentType = "application/json";

                var sw = new StreamWriter(Response.Body);
                var av = new Availability(_logger, _config, cache, _brandContext, returnCfg, _enableContentProvider, _httpClient, _json);
                var (totalResults, searchCacheKey) = await fnWriteData(av, sw);
                await sw.FlushAsync();
                await sw.DisposeAsync();
            }
            catch (Exception e)
            {
                if (_logger != null)
                    finalLog(operation, stopwatch, request, "error", $"{e.Message}", -1, e.StackTrace);
                await HttpResponseWritingExtensions.WriteAsync(Response, JsonConvert.SerializeObject(new { error = e.Message }));
            }
        }

        private void finalLog(string operation, Stopwatch stopwatch, SearchRequest request, string shortMessage, string longMessage, int totalResults, string stackTrace)
        {
            stopwatch.Stop();
            using (_logger.BeginScope(new Dictionary<string, object>
            {
                ["duration"] = stopwatch.ElapsedMilliseconds,
                ["StackTrace"] = stackTrace,
                ["Exception"] = stackTrace == "" ? "" : longMessage,
                ["short_message"] = shortMessage,
                ["operation"] = operation,
                ["branch-id"] = _brandContext.GatewayHeaders.BranchId,
                ["agent-sign"] = _brandContext.GatewayHeaders.AgentSign,
                ["origin"] = request.CityPairs[0].OriginIata,
                ["destination"] = request.CityPairs[0].DestinationIata,
                ["pax"] = $"{request.Adults}-{request.Children}-{request.Infants}",
                ["path"] = string.Join(",", request.CityPairs.Select(x => $"{x.OriginIata}-{x.DestinationIata}")),
                ["departureDates"] = string.Join(",", request.CityPairs.Select(x => x.DepartureDate.ToString())),
                ["searchtype"] = Availability.GetSearchType(request.CityPairs, _brandContext.isCvc()).ToString(),
                ["cabin"] = request.Cabin,
                ["userAgent"] = request.UserAgent,
                ["searchId"] = request.SearchId,
                ["clientId"] = request.ClientId,
                ["totalResults"] = totalResults,
                ["package"] = (request.Package ?? false).ToString(),
                ["request"] = JsonConvert.SerializeObject(request)
            }))
                _logger.LogInformation(longMessage);
        }

        private void finalLog(string operation, Stopwatch stopwatch, SearchRequest request, string shortMessage, string longMessage, int totalResults)
        {
            finalLog(operation, stopwatch, request, shortMessage, longMessage, totalResults, "");
        }

        [Route("[action]"), EnableCors("AllowAnyOrigin"), HttpPost]
        public async Task MarkdownSync(MarkdownSearchRequest request) => await processingWrapper("markdownsync", request, (av, sw) => av.MarkdownSearchSync(request, sw));

        /// <summary>
        /// Busca de vôos de forma bloqueante
        /// </summary>
        /// <remarks>
        /// Realiza busca de vôos e retorna o resultado de forma síncrona, ou seja, o controle de execução fica
        /// bloqueado até que as respostas de todos providers cadastrados no Gateway Aéreo retornem suas respostas.
        /// </remarks>
        /// <param name="request">
        /// Um objeto JSON detalhando os parâmetros da pesquisa. Neste objeto tem-se as informações de quantidade de
        /// adultos, crianças e bebês, se o vôo é direto ou não, se a passagem é para a classe econômica, e os trechos
        /// da viagem (incluindo o código IATA da cidade/aeroporto de origem e destino e data de embarque - cada trecho
        /// é um elemento num vetor de pares de cidades).
        /// Exemplo:
        /// {"Adults":"1","Children":"0","Infants":"0","DirectFlight":"false","Cabin":"eco","CityPairs":[{"DepartureDate":{"Day":"15","Month":"11","Year":"2019"},"OriginIata":"GRU","DestinationIata":"REC"}, {"DepartureDate":{"Day":"17","Month":"11","Year":"2019"},"OriginIata":"REC","DestinationIata":"GRU"}]}
        /// </param>
        /// <returns>
        /// A resposta é um objeto JSON com os grupos de preços, a matriz de preços e os filtros
        /// </returns>
        [Route("[action]"), EnableCors("AllowAnyOrigin"), HttpPost]
        public async Task Sync(SearchRequest request) => await processingWrapper("sync", request, (av, sw) => av.SearchSync(request, sw));

        /// <summary>
        /// Busca de vôos via stream
        /// </summary>
        /// <remarks>
        /// Realiza busca de vôos e retorna o resultado em um stream de dados, ou seja, o controle de execução retorna
        /// as respostas de cada um dos providers cadastrados no Gateway Aéreo assim que cada resposta esteja disponível.
        /// </remarks>
        /// <param name="request">
        /// Um objeto JSON detalhando os parâmetros da pesquisa. Neste objeto tem-se as informações de quantidade de
        /// adultos, crianças e bebês, se o vôo é direto ou não, se a passagem é para a classe econômica, e os trechos
        /// da viagem (incluindo o código IATA da cidade/aeroporto de origem e destino e data de embarque - cada trecho
        /// é um elemento num vetor de pares de cidades).
        /// Exemplo:
        /// {"Adults":"1","Children":"0","Infants":"0","DirectFlight":"false","Cabin":"eco","CityPairs":[{"DepartureDate":{"Day":"15","Month":"11","Year":"2019"},"OriginIata":"GRU","DestinationIata":"REC"}, {"DepartureDate":{"Day":"17","Month":"11","Year":"2019"},"OriginIata":"REC","DestinationIata":"GRU"}]}
        /// </param>
        /// <returns>
        /// A resposta é um objeto JSON com os grupos de preços, a matriz de preços e os filtros
        /// </returns>
        [Route("[action]"), EnableCors("AllowAnyOrigin"), HttpPost]
        public async Task AsyncStream(SearchRequest request)
        {
            var endDate = request.CityPairs.LastOrDefault()?.DepartureDate.ToDateTime();
            if (!Validations.CheckMaxSearchDurationExceeded(endDate))
            {
                StreamWriter sw = new StreamWriter(Response.Body, Encoding.UTF8);
                Response.StatusCode = (int)HttpStatusCode.OK;
                Response.ContentType = "application/json; charset=utf-8";

                await Response.StartAsync();

                await sw.WriteLineAsync(JsonConvert.SerializeObject(new
                {
                    priceGroup = Array.Empty<int>(),
                    error = "Maximum flight search limit is " + Validations.MaxDaysAllowedForTrip + " days.",
                    completedWork = 100
                }));

                await sw.FlushAsync();
                await Response.CompleteAsync();
                return;
            }

            await processingWrapper("asyncstream", request, (av, sw) => av.SearchAsync(request, sw));
        }

        [Route("[action]"), EnableCors("AllowAnyOrigin"), HttpPost]
        public async Task<List<SearchExclusiveResponse>> Exclusive(
            [FromServices] IRequestPromotions promotion,
            [FromServices] IGetCurrentBrandContext getCurrentBrandContext,
            SearchRequest request)
        {
            string gtwUserToken;
            if (!Request.Cookies.TryGetValue(IContextBrand.COOKIE_ACCESS_TOKEN, out gtwUserToken) &&
                    string.IsNullOrWhiteSpace(request.GtwUserToken))
            {
                var response = new SearchExclusiveResponse();
                var error = new Backend.Flights.Models.GtwAereo.Error
                {
                    Message = "Access Token not found.",
                    Operation = "Search Exclusive"
                };
                response.Error = error;
                return new List<SearchExclusiveResponse>() { response };

            }
            var endDate = request.CityPairs.LastOrDefault()?.DepartureDate.ToDateTime();
            if (!Validations.CheckMaxSearchDurationExceeded(endDate))
            {
                var response = new SearchExclusiveResponse();
                var error = new Backend.Flights.Models.GtwAereo.Error
                {
                    Message = "Maximum flight search limit is " + Validations.MaxDaysAllowedForTrip + " days.",
                    Operation = "Search Exclusive"
                };
                response.Error = error;
                return new List<SearchExclusiveResponse>() { response };

            }
            gtwUserToken ??= request.GtwUserToken;
            var contextBrand = await getCurrentBrandContext.Execute(gtwUserToken);
            contextBrand.UserToken = gtwUserToken;
            var avaiability = new Availability(_logger, _config, cache, _brandContext, ReturnCfg.Version3, _enableContentProvider, _httpClient, _json);
            return await avaiability.SearchExclusive(promotion, contextBrand, request);
        }

        private SearchRequest buildMockData()
        {
            var city1 = new CityPair() { OriginIata = "SAO", DestinationIata = "MIA", DepartureDate = new DateValues() { Day = 9, Month = 9, Year = 2021 } };
            var city2 = new CityPair() { OriginIata = "MIA", DestinationIata = "SAO", DepartureDate = new DateValues() { Day = 18, Month = 9, Year = 2021 } };
            var request = new SearchRequest()
            {
                Adults = 1,
                DirectFlight = false,
                Cabin = "eco",
                CityPairs = new List<CityPair>() { city1, city2 }
            };

            return request;
        }
        [Route("[action]"), EnableCors("AllowAnyOrigin"), HttpGet]
        public async Task SyncTest()
        {
            var mock = buildMockData();
            await processingWrapper("synctest", mock, (av, sw) => av.SearchSync(mock, sw));
        }
        [Route("[action]"), EnableCors("AllowAnyOrigin"), HttpGet]
        public async Task AsyncStreamTest()
        {
            var mock = buildMockData();
            await processingWrapper("asyncstreamtest", mock, (av, sw) => av.SearchAsync(mock, sw));
        }

        [Route("[action]"), EnableCors("AllowAnyOrigin"), HttpPost]
        public async Task<ActionResult<string>> GoogleFlightsSearch(GoogleFlightsRequest request)
        {
            var retorno = await new Availability(_logger, _config, cache, _brandContext, returnCfg, _enableContentProvider, _httpClient, _json).GoogleFlightsSearch(request);

            if (string.IsNullOrEmpty(retorno))
                return NoContent();
            return retorno;
        }

        /// <summary>
        /// Busca todos os resultados salvos no cache pelo searchCacheKey
        /// </summary>
        /// <param name="searchCacheKey">Chave única retornada pelo AsyncStream</param>
        /// <returns>Resultados salvos no cache</returns>
        [Route("[action]"), EnableCors("AllowAnyOrigin"), HttpGet]
        public async Task<ActionResult<CachedSearchResults>> GetCachedSearchResults(string searchCacheKey)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchCacheKey))
                {
                    return BadRequest(new { error = "searchCacheKey é obrigatório" });
                }

                var cachedData = await cache.GetValue(searchCacheKey);
                if (string.IsNullOrEmpty(cachedData))
                {
                    return NotFound(new { error = "Resultados não encontrados no cache ou expirados" });
                }

                // Descomprime os dados se estiverem comprimidos
                var decompressedData = CompressionUtil.DecompressString(cachedData);
                var results = JsonConvert.DeserializeObject<CachedSearchResults>(decompressedData);
                return Ok(results);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Erro ao buscar resultados do cache: {0}", ex.Message);
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Busca os resultados salvos no cache filtrados por UIDs específicos
        /// </summary>
        /// <param name="request">Request contendo searchCacheKey e lista de UIDs</param>
        /// <returns>Resultados salvos no cache, filtrados por UIDs</returns>
        [Route("[action]"), EnableCors("AllowAnyOrigin"), HttpPost]
        public async Task<ActionResult<CachedSearchResults>> GetCachedSearchResultsFiltered([FromBody] GetCachedSearchResultsRequest request)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(request.SearchCacheKey))
                {
                    return BadRequest(new { error = "searchCacheKey é obrigatório" });
                }

                var cachedData = await cache.GetValue(request.SearchCacheKey);
                if (string.IsNullOrEmpty(cachedData))
                {
                    return NotFound(new { error = "Resultados não encontrados no cache ou expirados" });
                }

                // Descomprime os dados se estiverem comprimidos
                var decompressedData = CompressionUtil.DecompressString(cachedData);
                var results = JsonConvert.DeserializeObject<CachedSearchResults>(decompressedData);

                // Filtra por UIDs se informados
                if (request.Uids != null && request.Uids.Any())
                {
                    // Filtra os resultados por UIDs
                    var filteredResults = new CachedSearchResults
                    {
                        SearchCacheKey = results.SearchCacheKey,
                        ProcessedAt = results.ProcessedAt,
                        TotalProviders = results.TotalProviders,
                        ProcessedProviders = results.ProcessedProviders,
                        ProvidersWithResults = results.ProvidersWithResults,
                        Note = results.Note,
                        Results = new Dictionary<string, Models.GtwAereo.FlightsResponse>()
                    };

                    foreach (var kvp in results.Results)
                    {
                        var filteredFlights = kvp.Value.Flights.Where(f => request.Uids.Contains(f.UID)).ToList();
                        if (filteredFlights.Any())
                        {
                            var filteredResponse = new Models.GtwAereo.FlightsResponse
                            {
                                Flights = filteredFlights,
                                Meta = kvp.Value.Meta,
                                Error = kvp.Value.Error
                            };
                            filteredResults.Results[kvp.Key] = filteredResponse;
                        }
                    }

                    results = filteredResults;
                }

                return Ok(results);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Erro ao buscar resultados do cache: {0}", ex.Message);
                return BadRequest(new { error = ex.Message });
            }
        }

        public class GetCachedSearchResultsRequest
        {
            public string SearchCacheKey { get; set; }
            public List<string> Uids { get; set; }
        }


    }

    [ApiVersion("1.0"), Route("api/search"), ApiController]
    public class SearchV1Controller : BaseSearchController
    {
        public SearchV1Controller(ILogger<Availability> logger, ConfigurationManager config, ICacheService cache, IContextBrand brandContext, IContextBrands contextBrands, IHttpClient httpClient, IJsonService json)
            : base(logger, config, cache, ReturnCfg.Version1, brandContext, contextBrands, httpClient, json) { }
    }

    [ApiVersion("2.0"), Route("api/search"), ApiController]
    public class SearchV2Controller : BaseSearchController
    {
        public SearchV2Controller(ILogger<Availability> logger, ConfigurationManager config, ICacheService cache, IContextBrand brandContext, IContextBrands contextBrands, IHttpClient httpClient, IJsonService json)
            : base(logger, config, cache, ReturnCfg.Version2, brandContext, contextBrands, httpClient, json) { }
    }

    [ApiVersion("3.0"), Route("api/search"), ApiController]
    public class SearchV3Controller : BaseSearchController
    {
        public SearchV3Controller(ILogger<Availability> logger, ConfigurationManager config, ICacheService cache, IContextBrand brandContext, IContextBrands contextBrands, IHttpClient httpClient, IJsonService json)
            : base(logger, config, cache, ReturnCfg.Version3, brandContext, contextBrands, httpClient, json, true) { }

        [Route("[action]"), EnableCors("AllowAnyOrigin"), HttpPost]
        async public Task<ObjectResult> UpsellOrCheckoutData(UpsellAndCheckoutData.RequestUpsellOrCheckoutDTO dto,
            [FromServices] IRequestPromotions promotion, [FromServices] IGetCurrentBrandContext getCurrentBrandContext,
            [FromServices] CA.UseCase.RateToken.GetRateTokenPriceInfo getRateTokenPriceInfo)
        {
            try
            {
                return Ok(await UpsellAndCheckoutData.RecoverFlight(_config, _logger, cache, _brandContext, dto,
                    _contextBrands, promotion, await getCurrentBrandContext.Execute(), getRateTokenPriceInfo, _httpClient, _json));
            }
            catch (Exception ex)
            {
                var parsedError = SearchParser.ParseRequestUpsellOrCheckoutDTOToError(dto, ex);
                _logger.LogError(ex, "Fail on UpsellOrCheckoutData: {0}", JsonConvert.SerializeObject(parsedError));
                if (ex is TimeoutException)
                    return StatusCode((int)HttpStatusCode.GatewayTimeout, parsedError);
                else
                    return BadRequest(parsedError);
            }
        }

        public class DeeplinkData
        {
            public List<string> RateTokens { get; set; }
        }
        [Route("[action]"), EnableCors("AllowAnyOrigin"), HttpPost]
        async public Task<ObjectResult> BuildCheckoutDataForDeeplink(DeeplinkData dto, [FromServices] CA.UseCase.RateToken.GetRateTokenPriceInfo getRateTokenPriceInfo)
        {
            string getProvider(string tk) => SearchParser.ParseRateToken(tk).Attribute("cmi").Value;
            try
            {
                var req = new CheckoutData.RequestData { Provider = getProvider(dto.RateTokens[0]), CanDirectToNewCheckout = true };
                var obj = new CheckoutData(req, base._config, base._logger, base._brandContext, null, null, null, null, getRateTokenPriceInfo, base._httpClient, base._json);
                var firstResponse = (await obj.CheckAvailability(dto.RateTokens.ToArray())).First();
                return Ok(await obj.BuildCheckoutResponse(null, null, firstResponse.ms, firstResponse.response));
            }
            catch (Exception ex)
            {
                return BadRequest(ex);
            }
        }

        public class PushFareDTO
        {
            public SearchRequest SearchParams { get; set; }
            public string BestPriceCia { get; set; }
            public decimal BestPrice { get; set; }
            public PriceMatrix Matrix { get; set; }
        }
        [Route("[action]"), EnableCors("AllowAnyOrigin"), HttpPost]
        async public Task<OkResult> PushFare(PushFareDTO dto)
        {
            await Availability.CallPushFare(_logger, _config, dto.SearchParams, dto.BestPriceCia, dto.BestPrice, dto.Matrix, _brandContext);
            return Ok();
        }

        [Route("[action]"), EnableCors("AllowAnyOrigin"), HttpPost]
        public async Task PartnerSearch(PartnerSearchRequest request)
        {
            await new Availability(_logger, _config, cache, _brandContext, ReturnCfg.Version3, _enableContentProvider, _httpClient, _json).GetPartnerFlights(request, Response);
        }
    }
}