﻿using Backend.Flights.CA.Services.ContentProvider;

namespace Backend.Flights.CA.UseCase.ContentProvider
{
    public class GetContentProviderUrl
    {
        public string Execute(Model.Context.BrandContext brandContext, string source)
        {
            return
                source == IContentProvider.SOURCE_GDS ?
                brandContext.ContentProvider!.Url + IContentProvider.URL_SUFIX :
                null;
        }
    }
}