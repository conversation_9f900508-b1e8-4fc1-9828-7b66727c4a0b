﻿using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Backend.Flights.Models.Partners
{

    public class OktoplusRequest : IPartnerRequest
    {
        public int idUsuarioIupi { get; set; }
        public int idSegmentoUsuario { get; set; }
        public ParametrosBusca parametrosBusca { get; set; }
    }

    public class ParametrosBusca
    {
        public string origem { get; set; }
        public string destino { get; set; }
        public string ida { get; set; }
        public string volta { get; set; }
        public int adultos { get; set; }
        public int criancas { get; set; }
        public int bebes { get; set; }
        public bool voosDiretos { get; set; }
        public string cabine { get; set; }
    }

    public class OktoplusRequestFactory : IPartnerRequestFactory
    {
        public IPartnerRequest Build(PartnerSearchRequest request, ILogger logger, BrandContext brandCtx)
        {
            try
            {
                if (!request.ComplementaryData.ContainsKey("IdUsuarioIupi") && !request.ComplementaryData.ContainsKey("IdSegmentoUsuario"))
                    throw new ArgumentException("IdUsuarioIupi or IdSegmentoUsuario is empty.");

                return new OktoplusRequest
                {
                    idSegmentoUsuario = Convert.ToInt32(request.ComplementaryData["IdSegmentoUsuario"]),
                    idUsuarioIupi = Convert.ToInt32(request.ComplementaryData["IdUsuarioIupi"]),
                    parametrosBusca = new ParametrosBusca
                    {
                        adultos = request.Adults,
                        bebes = request?.Infants ?? 0,
                        cabine = request.Cabin == "exe" ? "EXECUTIVA" : "ECONOMICA",
                        criancas = request?.Children ?? 0,
                        destino = request.CityPairs.FirstOrDefault().DestinationIata,
                        origem = request.CityPairs.FirstOrDefault().OriginIata,
                        ida = formatDate(request.CityPairs.FirstOrDefault().DepartureDate),
                        volta = request.CityPairs.Count > 1 ? formatDate(request.CityPairs[1].DepartureDate) : null,
                        voosDiretos = request.DirectFlight
                    }
                };

                string formatDate(Models.Search.DateValues dt) => string.Concat(dt.Year.ToString() + "-" + dt.Month.ToString("00") + "-" + dt.Day.ToString("00"));
            }
            catch (System.Exception e)
            {
                logger.LogError("Erro ao parsear Consulta de Parceiro.",
                    new Dictionary<string, object>
                    {
                        ["branch-id"] = brandCtx.GatewayHeaders.BranchId,
                        ["agent-sign"] = brandCtx.GatewayHeaders.AgentSign,
                        ["message"] = JsonConvert.SerializeObject(request),
                        ["operation"] = "PARTNERSEARCH",
                        ["error-message"] = e.Message
                    });

                throw;
            }
        }
    }
}
