using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Web;
using Backend.Flights.CA.Model;
using Backend.Flights.CA.Model.LocImport;
using Backend.Flights.CA.Services.Config;
using Backend.Flights.CA.Services.Http;
using Backend.Flights.CA.Services.Id;
using Backend.Flights.CA.Services.Json;
using Backend.Flights.CA.UseCase.Config;
using Microsoft.Extensions.Logging;

using ValidateResult = Backend.Flights.CA.SafeResult<
    Backend.Flights.CA.Model.LocImport.ImportLocPaymentValidationResponse,
    Backend.Flights.CA.Model.HttpErrorResult>;

namespace Backend.Flights.CA.Services.LocImportValidator
{
    public class PaymentCredipaxService : IPaymentCredipaxService
    {
        private readonly IHttpClient _http;
        private readonly IWatchConfig _watchConfig;
        private readonly IJsonService _json;
        private readonly IRandomIdService _randomId;
        private readonly ILogger<PaymentCredipaxService> _logger;

        public PaymentCredipaxService(IHttpClient http, IWatchConfig watchConfig, IRandomIdService randomId, IJsonService json, ILogger<PaymentCredipaxService> logger)
        {
            _http = http;
            _watchConfig = watchConfig;
            _randomId = randomId;
            _json = json;
            _logger = logger;
        }

        public async Task<ValidateResult> Validate(string loc, int branch, string userToken)
        {
            _logger.LogInformation("[IMPORTALOC][PaymentCredipaxService]: Iniciando validação do PaymentCredipax para loc={Loc} e branch={Branch}", loc, branch);

            // Obtém a configuração de payments do Consul
            var config = await _watchConfig.GetConfig<ImportaLocConfig>(IConfigService.IMPORTALOC_CONFIG);
            if (config == null)
            {
                return ValidateResult.Fail(new HttpErrorResult(500, "[IMPORTALOC][PaymentCredipaxService]: Configuração não encontrada", ""));
            }

            var endpointConfig = config.GetEndpoint("credipax");
            if (endpointConfig == null)
            {
                return ValidateResult.Fail(new HttpErrorResult(500, "[IMPORTALOC][PaymentCredipaxService]: Configuração de endpoint credipax não encontrada", ""));
            }

            // Constrói a URL completa com os parâmetros dinâmicos.
            string baseUrl = endpointConfig.FullUrl;
            string urlParams = baseUrl.Contains('?') ? "&" : "?";
            string finalUrl = $"{baseUrl}{urlParams}P_LOCALIZADOR={HttpUtility.UrlEncode(loc)}&P_CD_FILIAL={branch}";

            // Monta os headers para a chamada
            var headers = new Dictionary<string, string>
            {
                { "gtw-sec-user-token", userToken },
                { "gtw-transaction-id", $"atlas-flt-{_randomId.RandomId()}" }
            };

            var (responseString, httpError, httpResponse) = await _http.GetString(finalUrl, headers);

            if (httpError != null)
            {
                return ValidateResult.Fail(httpError);
            }

            // Desserializa a resposta para ImportLocPaymentValidationResponse
            try
            {
                var validationResponse = _json.Deserialize<ImportLocPaymentValidationResponse>(responseString);
                return ValidateResult.Success(validationResponse);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[IMPORTALOC][PaymentCredipaxService]: Erro ao desserializar a resposta para ImportLocPaymentValidationResponse.");
                return ValidateResult.Fail(new HttpErrorResult(500, ex.Message, responseString));
            }
        }
    }
}