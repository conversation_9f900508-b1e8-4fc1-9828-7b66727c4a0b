using Backend.Flights.CA.Model.Cart;
using Backend.Flights.CA.Model.Opportunities;
using System.Threading.Tasks;

namespace Backend.Flights.CA.UseCase.Opportunities
{
    public interface IGetOpportunities
    {
        Task<OpportunitiesResponse> Execute(OpportunitiesRequestItem[] availableItems, CartItem[] selectedItems, string userToken, OpportunitiesRequest.RequestParams requestParams = null, bool isProfitSplit = false);
    }
}