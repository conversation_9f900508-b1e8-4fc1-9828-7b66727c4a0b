﻿using Backend.Flights.CA.Services.Http;
using Backend.Flights.CA.Services.Json;
using Backend.Flights.Models;
using Backend.Flights.Models.GtwAereo;
using Backend.Flights.Models.Price;
using Backend.Flights.Models.Search;
using Backend.Flights.Services;
using Backend.Flights.Util;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;

namespace Backend.Flights.Business
{
    public class Pricing
    {
        private readonly ConfigurationManager _config;
        private readonly BrandContext _brandCtx;
        private readonly ILogger _logger;
        private readonly IHttpClient _httpClient;
        private readonly IJsonService _json;

        public Pricing(ConfigurationManager config, BrandContext brandCtx, ILogger logger, IHttpClient httpClient, IJsonService json)
        {
            _config = config;
            _brandCtx = brandCtx;
            _logger = logger;
            _httpClient = httpClient;
            _json = json;
        }

        public async Task<PriceResponse> VerifyFromCheckout(PriceRequest request)
        {
            var time = Stopwatch.StartNew();
            var response = await Verify(request);
            FillPriceGroupInfo(_config, response, request.Provider, _logger);
            time.Stop();

            LogPricing(_logger, _brandCtx.GatewayHeaders.AgentSign, _brandCtx.GatewayHeaders.BranchId, response, request.Provider, time.ElapsedMilliseconds, false, _brandCtx.isCvc());

            return response;
        }
        public async Task<PriceResponse> Verify(PriceRequest request)
        {
            var result = await new GtwAereo(_brandCtx, _config, _logger, false, _httpClient, _json).Pricing(request);

            try
            {
                return JsonConvert.DeserializeObject<PriceResponse>(result);
            }
            catch (Exception ex)
            {
                //_logger.LogError(ex, "Error parsing PriceResponse from GtwAereo. Response: {response}", result);
                throw new Exception($"Error parsing PriceResponse from GtwAereo. Response: {result}. Detalhes: {ex.Message}", ex);
            }

        }
        public static void FillPriceGroupInfo(ConfigurationManager _config, PriceResponse response, string provider, ILogger logger)
        {
            if (response.PriceGroup != null && response.PriceGroup.Segments.HasCount())
            {
                var locations = new AirLocations(_config);
                response.PriceGroup.FareGroup.Markup = parseMarkup(response.PriceGroup.Segments[0].RateToken);

                foreach (var segment in response.PriceGroup.Segments)
                {
                    var arrivalSeg = locations.LoadIata(segment.Arrival, logger);
                    segment.IsInternational = arrivalSeg.Country != "Brasil";
                    segment.Provider = provider;

                    foreach (var leg in segment.Legs)
                    {
                        var departureIata = locations.LoadIata(leg.Departure, logger);
                        var arrivalIata = locations.LoadIata(leg.Arrival, logger);

                        leg.DepartureDescription = departureIata.City;
                        leg.DepartureCode = departureIata.Mastercode ?? 0;
                        leg.ArrivalDescription = arrivalIata.City;
                        leg.ArrivalCode = arrivalIata.Mastercode ?? 0;
                    }
                }
            }
        }

        private static decimal parseMarkup(string token)
        {
            var decode64 = Base64UrlTextEncoder.Decode(token);
            var decodeStr = System.Text.Encoding.UTF8.GetString(decode64);

            var startIndex = decodeStr.IndexOf("mkp") + 5;
            var lengthSubstring = (decodeStr.IndexOf("ofd") - startIndex) - 2;

            var markupValue = decodeStr.Substring(startIndex, lengthSubstring);

            return decimal.Parse(markupValue, NumberFormatInfo.InvariantInfo);
        }


        public static void LogPricing(ILogger _logger, string agentSign, string branchId, PriceResponse response, string provider, long duration, bool preCheckout, bool isCvc, string searchId = null, string clientId = null)
        {
            var operation = "PRICING" + (preCheckout ? "-PRECHECKOUT" : "");
            if (response.PriceGroup != null)
            {
                var pg = response.PriceGroup;
                var pgs = pg.Segments;
                _logger.BeginScope(new Dictionary<string, object>
                {
                    ["app_name"] = "sub-backend-flights",
                    ["duration"] = duration,
                    ["origin"] = pgs[0].Departure,
                    ["destination"] = pgs[0].Arrival,
                    ["operation"] = operation,
                    ["short_message"] = pg.FareGroup.ReCharging == "true" ? "repricing" : "success",
                    ["product"] = "AIR",
                    ["provider"] = provider,
                    ["air_company"] = pg.ValidatingBy.Iata,
                    ["notax_price"] = pg.FareGroup.PriceWithoutTax,
                    ["total_price"] = pg.FareGroup.PriceWithTax,
                    ["outbound_date"] = pgs[0].DepartureDate,
                    ["inbound_date"] = pgs[pgs.Count() - 1].DepartureDate,
                    ["flight_class"] = pgs[0].FareType,
                    ["travel_type"] = Availability.GetSearchType(pgs.Select(x => new CityPair { OriginIata = x.Departure, DestinationIata = x.Arrival }).ToList(), isCvc).ToString(),
                    ["branch-id"] = branchId,
                    ["agent-sign"] = agentSign,
                    ["clientId"] = clientId,
                    ["searchId"] = searchId
                });
                _logger.LogInformation(response.Error != null ? response.Error.Message : "success");
            }
            else
            {
                _logger.BeginScope(new Dictionary<string, object>
                {
                    ["app_name"] = "sub-backend-flights",
                    ["duration"] = duration,
                    ["operation"] = operation,
                    ["short_message"] = "error",
                    ["product"] = "AIR",
                    ["provider"] = provider,
                    ["branch-id"] = branchId,
                    ["agent-sign"] = agentSign,
                    ["clientId"] = clientId,
                    ["searchId"] = searchId
                });
                _logger.LogInformation(response.Error.Message);
            }
        }
    }
}
