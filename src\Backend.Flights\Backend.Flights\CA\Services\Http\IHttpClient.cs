using Backend.Flights.CA.Model;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;

namespace Backend.Flights.CA.Services.Http
{
    public interface IHttpClient
    {
        Task<(string, HttpErrorResult, HttpResponseMessage)> GetString(string url, Dictionary<string, string> headers = null, int? timeout = null, bool returnOnHeaderRecivied = false);
        Task<(string, HttpErrorResult, HttpResponseMessage)> PostString(string url, string data, Dictionary<string, string> headers = null, int? timeout = null);
        Task<(MemoryStream, HttpErrorResult, HttpResponseMessage)> GetMemoryStream(string url, Dictionary<string, string> headers = null, int? timeout = null);
        Task<(MemoryStream, HttpErrorResult, HttpResponseMessage)> PostMemoryStream(string url, string data, Dictionary<string, string> headers = null, int? timeout = null);
        Task<(Stream, HttpErrorResult, HttpResponseMessage)> GetResponseStream(string url, Dictionary<string, string> headers = null, int? timeout = null);
        Task<(Stream, HttpErrorResult, HttpResponseMessage)> PostResponseStream(string url, string data, Dictionary<string, string> headers = null, int? timeout = null);
    }
}