environment:
  - name: TI
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 300Mi
                cpu: 100m
              limits:
                memory: 1500Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 15
            livenessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 15
            ports:
              - containerPort: 80
          hpa:
            minReplicas: 40
            maxReplicas: 80
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 120
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 300
          service:
            ports:
              - name: mainport
                port: 80
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: sub-backend-flights
                service:
                  port:
                    number: 80
              - host: sub-backend-flights
                service:
                  port:
                    number: 80
          configmap:
            data:
              - name: ASPNETCORE_ENVIRONMENT
                value: ti
              - name: PROJECT_NAME
                value: sub-backend-flights
  - name: QA-TMP
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 300Mi
                cpu: 100m
              limits:
                memory: 500Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 15
            livenessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 15
            ports:
              - containerPort: 80
          hpa:
            minReplicas: 40
            maxReplicas: 80
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 120
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 300
          service:
            ports:
              - name: mainport
                port: 80
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: sub-backend-flights
                service:
                  port:
                    number: 80
              - host: sub-backend-flights
                service:
                  port:
                    number: 80
          configmap:
            data:
              - name: ASPNETCORE_ENVIRONMENT
                value: qa
              - name: PROJECT_NAME
                value: sub-backend-flights
  - name: QA
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 300Mi
                cpu: 100m
              limits:
                memory: 1500Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 15
            livenessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 15
            ports:
              - containerPort: 80
          hpa:
            minReplicas: 40
            maxReplicas: 80
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 120
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 300
          service:
            ports:
              - name: mainport
                port: 80
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: sub-backend-flights
                service:
                  port:
                    number: 80
              - host: sub-backend-flights
                service:
                  port:
                    number: 80
          configmap:
            data:
              - name: ASPNETCORE_ENVIRONMENT
                value: qa
              - name: PROJECT_NAME
                value: sub-backend-flights
  - name: PILOT
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 300Mi
                cpu: 100m
              limits:
                memory: 1500Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 15
            livenessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 15
            ports:
              - containerPort: 80
          hpa:
            minReplicas: 40
            maxReplicas: 80
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 120
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 300
          service:
            ports:
              - name: mainport
                port: 80
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: sub-backend-flights
                service:
                  port:
                    number: 80
              - host: sub-backend-flights
                service:
                  port:
                    number: 80
          configmap:
            data:
              - name: ASPNETCORE_ENVIRONMENT
                value: prod
              - name: PROJECT_NAME
                value: sub-backend-flights
  - name: PROD
    farm:
      - name: default
        kubernetes:
          deployment:
            resources:
              requests:
                memory: 300Mi
                cpu: 100m
              limits:
                memory: 1500Mi
                cpu: 500m
            readinessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 15
            livenessProbe:
              health-check:
                path: /health
                port: 80
              initialDelaySeconds: 15
            ports:
              - containerPort: 80
          hpa:
            minReplicas: 40
            maxReplicas: 80
            metrics:
              resources:
                - name: cpu
                  target:
                    type: Utilization
                    averageUtilization: 120
                - name: memory
                  target:
                    type: Utilization
                    averageUtilization: 300
          service:
            ports:
              - name: mainport
                port: 80
          ingress:
            externalDnsType: nginx-private
            ingressClassName: nginx-private
            type: http
            rules:
              - host: sub-backend-flights
                service:
                  port:
                    number: 80
              - host: sub-backend-flights
                service:
                  port:
                    number: 80
          configmap:
            data:
              - name: ASPNETCORE_ENVIRONMENT
                value: prod
              - name: PROJECT_NAME
                value: sub-backend-flights
