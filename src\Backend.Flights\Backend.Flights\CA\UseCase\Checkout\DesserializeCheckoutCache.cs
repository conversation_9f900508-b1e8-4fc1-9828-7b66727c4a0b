﻿using Microsoft.IO;
using System;
using System.IO.Compression;

namespace Backend.Flights.CA.UseCase.Checkout
{
    public class DesserializeCheckoutCache
    {
        private readonly RecyclableMemoryStreamManager _recyclableMemoryStreamManager;

        public DesserializeCheckoutCache(RecyclableMemoryStreamManager recyclableMemoryStreamManager)
        {
            _recyclableMemoryStreamManager = recyclableMemoryStreamManager;
        }

        public Model.Cache.CartCheckout Execute(string checkout)
        {

            using var memoryStream = _recyclableMemoryStreamManager.GetStream(
                Convert.FromBase64String(checkout));
            using var deflateStream = new DeflateStream(memoryStream, CompressionMode.Decompress);

            return ProtoBuf.Serializer.Deserialize<Model.Cache.CartCheckout>(deflateStream);
        }
    }
}
