﻿using Newtonsoft.Json;
using System.Collections.Generic;

namespace Backend.Flights.Models.Promotions
{
    public class BffPromotionResponse
    {
        [JsonProperty("promotion")]
        public PromotionInfo Promotion { get; set; }
        [JsonProperty("statements")]
        public List<Statement> Statements { get; set; }
        [JsonProperty("flightId")]
        public string FlightId { get; set; }
    }


}