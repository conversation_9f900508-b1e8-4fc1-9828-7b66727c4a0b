using Gelf.Extensions.Logging;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;

namespace Backend.Flights
{
    public class Program
    {
        public static void Main(string[] args)
        {
            CreateWebHostBuilder(args).Build().Run();
        }

        public static IWebHostBuilder CreateWebHostBuilder(string[] args) =>
            WebHost.CreateDefaultBuilder(args)
                .UseStartup<Startup>()
                .ConfigureLogging((context, builder) =>
                {
                    builder.ClearProviders();
                    builder.AddConfiguration(context.Configuration.GetSection("Logging"))
                        .AddConsole()
                        .AddDebug()
#if !DEBUG
                        .AddGelf(options =>
                        {
                            options.Host = context.Configuration.GetSection("AppSettings:Gelf.Url").Value;
                            options.IncludeScopes = true;
                            options.LogSource = context.HostingEnvironment.ApplicationName;
                            options.AdditionalFields["timestamp"] = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"); // ISO8601 com UTC e milissegundos
                            options.AdditionalFields["machine_name"] = Environment.MachineName;
                            options.AdditionalFields["app_name"] = "sub-backend-flights";
                        })
#endif
                    ;
                });
    }
}
