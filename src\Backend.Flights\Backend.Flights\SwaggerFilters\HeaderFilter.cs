﻿using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System;
using System.Collections.Generic;

namespace Backend.Flights.SwaggerFilters
{
    public class HeaderFilter : IOperationFilter
    {
        private string name = String.Empty;

        public HeaderFilter(string headerName)
        {
            this.name = headerName;
        }

        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            if (operation.Parameters == null)
                operation.Parameters = new List<OpenApiParameter>();

            operation.Parameters.Add(new OpenApiParameter
            {
                Name = this.name,
                In = ParameterLocation.Header,
                Schema = new OpenApiSchema { Type = "String" },
                Required = true
            });
        }
    }
}
