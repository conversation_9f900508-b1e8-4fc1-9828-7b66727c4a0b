using Newtonsoft.Json;

namespace Backend.Flights.CA.Services.Json
{
    public class JsonNewtonsoft : IJsonService
    {
        public string Serialize(object source)
        {
            return JsonConvert.SerializeObject(source);
        }

        public T Deserialize<T>(string json)
        {
            return JsonConvert.DeserializeObject<T>(json);
        }

        public dynamic Deserialize(string json)
        {
            return JsonConvert.DeserializeObject(json);
        }
    }
}