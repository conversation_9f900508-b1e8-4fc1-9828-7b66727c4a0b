using Backend.Flights.CA.Model.Cart;
using Backend.Flights.CA.Model.Search.Result;
using ProtoBuf;
using System;
using System.Collections.Generic;

namespace Backend.Flights.CA.Model.Search
{
    [ProtoContract(SkipConstructor = true)]
    public class SearchCacheItem
    {
        [ProtoMember(1)]
        public CartFlightItem CartBase;

        [ProtoMember(2)] 
        public Dictionary<string, SearchCacheItem.CacheDetail> Detail;

        [ProtoMember(3)] 
        public Dictionary<string, SearchResultFlight.ResultFareProfile> Upsell;

        [ProtoContract(SkipConstructor = true)]
        public class CacheDetail
        {
            [ProtoMember(1)]
            public DateTime ProductStartDate;

            [ProtoMember(2)]
            public DateTime ProductEndDate;

            [ProtoMember(3)]
            public CacheDetailSegment[] Segments;
        }

        [ProtoContract(SkipConstructor = true)]
        public class CacheDetailSegment
        {
            [ProtoMember(1)]
            public int RouteRPH;
            
            [ProtoMember(2)] 
            public string PackageGroup;
            
            [ProtoMember(3)] 
            public string RateToken;
            
            [ProtoMember(4)] 
            public CartFlightItem.Segment[] Segments;
        }
    }
}