# Etapa de build
FROM mcr.microsoft.com/dotnet/sdk:7.0 AS build-env
WORKDIR /app

# Copiar csproj e restaurar dependencias
COPY ./src/* ./
WORKDIR /app/Backend.Flights
RUN dotnet restore

# Adicionar pacotes (se necessário)
# RUN dotnet add Backend.Flights.csproj package Instana.Tracing.Core
# RUN dotnet add Backend.Flights.csproj package Instana.Tracing.Core.Rewriter.Linux

# Build da aplicacao
RUN dotnet publish -c Release -o out

# Etapa de verificação do Veracode
FROM veracode/api-wrapper-java:latest AS veracode
ARG VERACODE_APP_ID
ARG VERACODE_API_KEY
ARG BUILD_ID
COPY --from=build-env /app/Backend.Flights/out /home/<USER>/app
RUN java -jar /opt/veracode/api-wrapper.jar \
    -vid $VERACODE_APP_ID \
    -vkey $VERACODE_API_KEY \
    -version $BUILD_ID \
    -action UploadAndScan \
    -createprofile true \
    -appname "sub-backend-flights" \
    -filepath /home/<USER>/app; exit 0;

# Build da imagem final
FROM mcr.microsoft.com/dotnet/aspnet:7.0
WORKDIR /app
COPY --from=veracode /home/<USER>/app .

# Uncomment the following lines if Instana tracing is needed
# ENV DOTNET_STARTUP_HOOKS=/app/Instana.Tracing.Core.dll
# ENV CORECLR_ENABLE_PROFILING=1
# ENV CORECLR_PROFILER={cf0d821e-299b-5307-a3d8-b283c03916dd}
# ENV CORECLR_PROFILER_PATH=/app/instana_tracing/CoreProfiler.so

ENTRYPOINT ["dotnet", "Backend.Flights.dll"]
