﻿using Backend.Flights.CA.Model.Context;
using Backend.Flights.CA.Model.Search;
using Backend.Flights.CA.Model.Search.Result;
using Backend.Flights.CA.Services.Http;
using Backend.Flights.CA.Services.Json;
using Backend.Flights.CA.Services.UserToken;
using Backend.Flights.CA.UseCase.ContentProvider;
using Backend.Flights.CA.UseCase.Search;
using Backend.Flights.Util;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Backend.Flights.CA.Services.AirGateway
{
    public class AirGateway : IAirGateway
    {
        private readonly ILogger _logger;
        private readonly IHttpClient _httpClient;
        private readonly IJsonService _json;
        private readonly GetSearchUrl _getSearchUrl;
        private readonly GetRealProvider _getRealProvider;

        public AirGateway(
            IHttpClient httpClient,
            GetSearchUrl getSearchUrl,
            GetRealProvider getRealProvider,
            IJsonService json,
            ILogger<AirGateway> logger)
        {
            _httpClient = httpClient;
            _getSearchUrl = getSearchUrl;
            _getRealProvider = getRealProvider;
            _json = json;
            _logger = logger;
        }

        public static string GetAges(SearchRequest request)
        {
            var isInvalid = request.Adults == 0 && request.Children == 0 && request.Infants == 0;

            var m = new[] {
                new { Age = 30, Total = isInvalid ? 1 : request.Adults },
                new { Age = 10, Total = request.Children },
                new { Age = 1, Total = request.Infants }
            };

            var sb = new StringBuilder();
            foreach (var item in m)
            {
                if (item.Total == 0) continue;

                if (sb.Length > 0)
                    sb.Append(",");
                sb.Insert(0, $"{item.Age},", item.Total).Remove(sb.Length - 1, 1);
            }
            return sb.ToString();
        }
        public static string GetCityPairs(IEnumerable<SearchCityPair> cityPairs)
        {
            if (cityPairs.Count() == 0)
                return string.Empty;

            var sb = new StringBuilder();
            foreach (var pair in cityPairs)
                sb.AppendFormat("{0},{1},{2}-{3:D2}-{4:D2}+", pair.OriginIata, pair.DestinationIata, pair.DepartureDate.Year, pair.DepartureDate.Month, pair.DepartureDate.Day);
            return sb.Remove(sb.Length - 1, 1).ToString();
        }

        public static string MakeUrlSearchParams(BrandContext brandContext, SearchRequest request)
        {
            var sb = new StringBuilder();
            sb.Append("ages=");
            sb.Append(HttpUtility.UrlEncode(GetAges(request)));
            sb.Append("&routes=");
            sb.Append(HttpUtility.UrlEncode(GetCityPairs(request.CityPairs)));

            sb.Append("&packageGroup=");
            if (brandContext.User?.UserType == IUserTokenService.USER_TYPE_VENDOR)
            {
                if (!string.IsNullOrWhiteSpace(request.PackageGroup) &&
                    !"undefined".Equals(request.PackageGroup?.ToLower() ?? string.Empty))
                {
                    sb.Append(HttpUtility.UrlEncode(request.PackageGroup!.ToUpper()));
                }
                else
                {
                    sb.Append("STANDALONE");
                }
            }
            else
            {
                sb.Append(request.Package == true ? "PACKAGE" : "VHI,VHIPLUS,STANDALONE");
            }

            sb.Append("&businessClass=");
            sb.Append(request.Cabin.IsNotEmptyAndEquals("exe") ? "YES" : "ALSO");
            sb.Append("&preferences=persistLog,ignoreCachedUnavailableFlights,loadInstallments:true,language:pt_BR,currency:BRL");

            return sb.ToString();
        }

        string IAirGateway.MakeUrlSearchParams(BrandContext brandContext, SearchRequest request)
        {
            return MakeUrlSearchParams(brandContext, request);
        }

        private SearchResultError ParseError(Model.HttpErrorResult error)
        {
            try
            {
                var e = _json.Deserialize<SearchResultError>(error.Body);
                if (e != null && !string.IsNullOrEmpty(e.Message))
                {
                    return e;
                }

            }
            catch
            {
            }

            return new SearchResultError($"http-{error.HttpErrorCode}", $"{error.Message}\r\n\r\n{error.Body}", string.Empty);
        }

        public async IAsyncEnumerable<(SearchResultError, SearchResultFlight)> Search(BrandContext brandContext, SearchRequest request, string source)
        {
            var url =
                _getSearchUrl.Execute(brandContext, source) +
                MakeUrlSearchParams(brandContext, request) +
                "&source=" +
                _getRealProvider.Execute(source);

            var (res, httpError, httpResponse) = await _httpClient.GetMemoryStream(url, brandContext.GatewayHeaders);

            if (res != null)
            {
                var serializer = new Newtonsoft.Json.JsonSerializer();
                using (res)
                {
                    using var textReader = new StreamReader(res, Encoding.UTF8);
                    using var reader = new Newtonsoft.Json.JsonTextReader(textReader);
                    while (await reader.ReadAsync())
                    {
                        if (reader.TokenType == Newtonsoft.Json.JsonToken.PropertyName)
                        {
                            if (reader.Value as string == "flights" &&
                                await reader.ReadAsync() &&
                                reader.TokenType == Newtonsoft.Json.JsonToken.StartArray)
                            {
                                while (
                                    await reader.ReadAsync() &&
                                    reader.TokenType == Newtonsoft.Json.JsonToken.StartObject)
                                {
                                    SearchResultFlight flight;
                                    SearchResultError error = null;
                                    try
                                    {
                                        flight = serializer.Deserialize<SearchResultFlight>(reader);
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.LogError(ex, ex.GetErrorLocation("Fail on flight parse"));
                                        flight = null;
                                        error = new SearchResultError(
                                            "bff", "Fail on flight parse", string.Empty);
                                    }

                                    yield return (error, flight);
                                }
                            }
                            else
                            {
                                await reader.SkipAsync();
                            }
                        }
                    }
                }
            }
            else
            {
                yield return (ParseError(httpError), null);
            }
        }

        public async Task<SafeResult<dynamic, Tuple<int, string, string>>> LocImport(BrandContext brandContext, string loc, string source, string packageGroup)
        {
            var url = brandContext.GatewayUrl +
                $"/flights/bookings/{HttpUtility.UrlEncode(source.ToUpper())}/{HttpUtility.UrlEncode(loc.ToUpper())}?packageGroup={HttpUtility.UrlEncode(packageGroup.ToUpper())}&preferences=showTicketsInfo";

            var (res, httpError, httpResponse) = await _httpClient.GetString(url, brandContext.GatewayHeaders);

            if (httpError != null)
            {
                _logger.LogError("Fail on loc import ({loc}, {source}, {packageGroup}). http code: {httpCode}. message: {httpMessage}. body: {body}",
                    loc, source, packageGroup, httpError.HttpErrorCode, httpError.Message, httpError.Body);

                var error = _json.Deserialize(httpError.Body);
                string errorMessage = error.error.message;
                string gtwErrorCode = error.error.code;

                Tuple<int, string, string> err = (httpError.HttpErrorCode, errorMessage, gtwErrorCode).ToTuple();

                return SafeResult<dynamic, Tuple<int, string, string>>.Fail(err);
            }

            return SafeResult<dynamic, Tuple<int, string, string>>.Success(_json.Deserialize(res));
        }
    }
}