﻿using Backend.Flights.CA.Services.Config;
using System.Threading.Tasks;

namespace Backend.Flights.Services
{
    public interface IConsul
    {
        public const string STR_PROJECT_NAME = IConfigService.PROJECT_NAME;
        public const string STR_KEY_GATEWAY_SOURCES = STR_PROJECT_NAME + "/gateway.sources";

        Task<string> GetValueAsync(string key);
        Task<string> GetValueOrEmptyAsync(string key);
    }
}