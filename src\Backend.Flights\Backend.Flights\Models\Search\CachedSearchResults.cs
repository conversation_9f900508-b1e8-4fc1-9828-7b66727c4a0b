using Backend.Flights.Models.GtwAereo;
using System;
using System.Collections.Generic;

namespace Backend.Flights.Models.Search
{
    /// <summary>
    /// Modelo para representar os resultados salvos no cache
    /// </summary>
    public class CachedSearchResults
    {
        /// <summary>
        /// Chave única do cache
        /// </summary>
        public string SearchCacheKey { get; set; }

        /// <summary>
        /// Resultados por provider
        /// </summary>
        public Dictionary<string, FlightsResponse> Results { get; set; }

        /// <summary>
        /// Data/hora de processamento
        /// </summary>
        public DateTime ProcessedAt { get; set; }

        /// <summary>
        /// Total de providers esperados
        /// </summary>
        public int TotalProviders { get; set; }

        /// <summary>
        /// Total de providers processados
        /// </summary>
        public int ProcessedProviders { get; set; }

        /// <summary>
        /// Lista de providers que retornaram resultados não vazios
        /// Exemplo: ["GOL", "LATAM", "AZUL"] - indica que apenas esses 3 providers retornaram voos
        /// </summary>
        public List<string> ProvidersWithResults { get; set; } = new List<string>();

        /// <summary>
        /// Nota sobre o processamento dos dados
        /// </summary>
        public string Note { get; set; }
    }
}