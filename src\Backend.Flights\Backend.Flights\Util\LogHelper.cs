using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Backend.Flights.Util
{
    public class LogHelper
    {
        private readonly ILogger _logger;
        private readonly ConfigurationManager _config;

        public LogHelper(ILogger logger, ConfigurationManager config)
        {
            _logger = logger;
            _config = config;
        }

        public class LogMetadata
        {
            public string Endpoint { get; set; }
            public string RequestBody { get; set; }

            public string GetLogString()
            {
                return $"Request to {Endpoint} - request body: {RequestBody}";
            }
        }

        public void LogRequestInfo(HttpContext context, object requestBody)
        {
            try
            {
                // Registra as informações de log apenas se a chave [log.request.info] estiver habilitada no Consul
                if (_config.AppSetting.TryGetValue("LogRequestInfo", out string logEnabled) && logEnabled.ToLower() == "true")
                {
                    // Lista de endpoints a ignorar
                    List<string> endpointsToIgnore = new List<string> { "/Log", "/Health" };

                    // Verifica se o endpoint corrente está na lista de endpoints a ignorar
                    if (endpointsToIgnore.Any(endpoint => context.Request.Path.Value.Contains(endpoint, StringComparison.OrdinalIgnoreCase)))
                        return;

                    var logMetadata = new LogMetadata
                    {
                        Endpoint = $"{context.Request.Scheme}://{context.Request.Host}{context.Request.Path}",
                        RequestBody = JsonConvert.SerializeObject(requestBody)
                    };

                    var transactionId = GetHeaderValues(context, "transactionId");
                    var searchId = GetPropertyValueIgnoreCase(requestBody, "SearchId");
                    var operation = context.Request.Path.Value.Split('/').Last().ToLower();

                    using (_logger.BeginScope(new Dictionary<string, object>
                    {
                        ["metadata"] = logMetadata.GetLogString(),
                        ["operation"] = operation,
                        ["transactionId"] = transactionId,
                        ["searchId"] = searchId
                    }))
                        _logger.LogInformation("[LogRequestInfo] - Informações do Request");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error while logging request info: " + ex.Message);
            }
        }

        // Estou pegando a informação do header pois o context.Request.Cookies não está retornando o valor atualizado do cookie
        private static string GetHeaderValues(HttpContext context, string headerName)
        {
            if (!context.Request.Headers.TryGetValue("Cookie", out var cookieHeaders))
                return string.Empty;

            var values = new List<string>();

            foreach (var cookie in cookieHeaders)
            {
                var cookieParts = cookie.Split(';');
                foreach (var part in cookieParts)
                {
                    var keyValue = part.Trim().Split('=');
                    if (keyValue.Length == 2 && string.Equals(keyValue[0].Trim(), headerName, StringComparison.OrdinalIgnoreCase))
                    {
                        values.Add(keyValue[1].Trim());
                    }
                }
            }

            return values.Count > 0 ? string.Join(", ", values) : string.Empty;
        }

        public void LogRequestInfo(IHttpContextAccessor context, object requestBody)
        {
            try
            {
                LogRequestInfo(context.HttpContext, requestBody);
            }
            catch (Exception)
            {
            }
        }

        private object GetPropertyValueIgnoreCase(object obj, string propertyName)
        {
            if (obj == null || string.IsNullOrEmpty(propertyName))
                return null;

            var property = obj.GetType().GetProperties()
                .FirstOrDefault(p => p.Name.Equals(propertyName, StringComparison.OrdinalIgnoreCase));

            return property?.GetValue(obj) ?? string.Empty;
        }
    }
}