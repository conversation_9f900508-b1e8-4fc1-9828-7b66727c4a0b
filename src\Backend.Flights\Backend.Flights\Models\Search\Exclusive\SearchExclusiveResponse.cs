using System;
using System.Collections.Generic;
using Backend.Flights.Models.GtwAereo;

namespace Backend.Flights.Models.Search.Exclusive
{
    public class SearchExclusiveResponse
    {
        public Error Error { get; set; }
        public List<Flight> Flights { get; set; }
    }

    public class Flight
    {
        public FareGroup FareGroup { get; set; }
        public List<Segment> Segments { get; set; }

    }

    public class FareGroup
    {
        public bool IsPromotional { get; set; }
        public decimal PromoPriceWithTax { get; set; }
        public decimal PriceWithTax { get; set; }
    }

    public class Segment
    {
        public DateTime DepartureDate { get; set; }
        public string RateToken { get; set; }
        public bool National { get; set; }
    }
}