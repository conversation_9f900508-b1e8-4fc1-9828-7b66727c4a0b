using Backend.Flights.CA.Model.Cart;
using Backend.Flights.CA.Model.Opportunities;
using Backend.Flights.CA.Model.Promotions;
using System.Threading.Tasks;

namespace Backend.Flights.CA.UseCase.Promotions
{
    public interface IRequestPromotions
    {
        Task<PromotionsResponse> Execute(
            Model.Context.BrandContext brand,
            string userToken,
            OpportunitiesRequestItem[] availableItems,
            CartItem[] selectedItems,
            OpportunitiesRequest.RequestParams requestParams = null,
            bool isProfitSplit = false);
    }
}