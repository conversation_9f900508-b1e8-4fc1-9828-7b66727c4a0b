﻿using Backend.Flights.CA.Model.Cache;
using Backend.Flights.CA.UseCase.FeatureFlag;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Net;
using System.Security.Cryptography;
using System.Text;

namespace Backend.Flights.Controllers
{
    [ApiController, ApiVersion("1.0"), Route("api/[controller]")]
    public class FeatureFlagController : Controller
    {
        private static FeatureFlagCache _cache = null;

        private readonly GetFeatureFlag _getFeatureFlag;
        private readonly ILogger<FeatureFlagController> _log;

        public FeatureFlagController(GetFeatureFlag getFeatureFlag, ILogger<FeatureFlagController> log)
        {
            _getFeatureFlag = getFeatureFlag;
            _log = log;
        }

        private string ComputeEtag(string value)
        {
            using var md5 = MD5.Create();

            var hash = md5.ComputeHash(Encoding.UTF8.GetBytes(value));
            return @$"W/""{Convert.ToBase64String(hash)}""";
        }

        [HttpGet]
        public IActionResult Index()
        {
            Response.Headers["CacheControl"] = "max-age=60, public";

            var (flag, update) = _getFeatureFlag.Execute();

            if (update > _cache?.LastUpdate)
            {
                _cache = new FeatureFlagCache(flag, ComputeEtag(flag), DateTime.MinValue);
            }

            _log.LogInformation($"IfNoneMatch: {Request.Headers["IfNoneMatch"]}");
            if (Request.Headers["IfNoneMatch"] == _cache?.Etag)
            {
                return new StatusCodeResult((int)HttpStatusCode.NotModified);
            }

            Response.Headers["ETag"] = _cache.Etag;

            return new ContentResult()
            {
                Content = flag,
                ContentType = "application/json",
                StatusCode = (int)HttpStatusCode.OK,
            };
        }
    }
}
