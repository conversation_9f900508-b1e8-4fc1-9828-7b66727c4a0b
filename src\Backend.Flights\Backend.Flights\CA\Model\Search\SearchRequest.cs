using System.ComponentModel.DataAnnotations;

namespace Backend.Flights.CA.Model.Search
{
    public class SearchRequest
    {
        [Required]
        public SearchCityPair[] CityPairs { get; set; }

        [Required]
        public int Adults { get; set; }

        public int Children { get; set; }

        public int Infants { get; set; }

        public bool DirectFlight { get; set; }

        public string Cabin { get; set; }

        public bool? Baggage { get; set; }

        public bool? Package { get; set; }

        public string PackageGroup { get; set; }

        public string UserAgent { get; set; }

        public string SearchId { get; set; }

        public string ClientId { get; set; }
    }
}

