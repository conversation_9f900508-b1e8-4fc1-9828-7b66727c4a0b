using Backend.Flights.CA.Model.Context;
using Backend.Flights.CA.Services.Config;
using Backend.Flights.CA.Services.Http;
using Backend.Flights.CA.Services.Json;
using Backend.Flights.CA.UseCase.Config;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace Backend.Flights.CA.Services.UserToken
{
    public class UserTokenService : IUserTokenService
    {
        private class JWTPayload
        {
            public User Credential { get; set; }
        }

        private readonly IWatchConfig _config;
        private readonly IHttpClient _httpClient;
        private readonly IJsonService _json;

        public UserTokenService(IWatchConfig config, IHttpClient httpClient, IJsonService json)
        {
            _config = config;
            _httpClient = httpClient;
            _json = json;
        }

        public User GetUserInfo(string userToken)
        {
            if (string.IsNullOrWhiteSpace(userToken))
            {
                return null;
            }

            var parts = userToken.Split('.');
            if (parts.Length != 3)
            {
                return null;
            }

            var body = Base64UrlToBase64(parts[1]);
            var json = Encoding.UTF8.GetString(Convert.FromBase64String(body));
            return _json.Deserialize<JWTPayload>(json)?.Credential;
        }

        public Task<bool> ValidateUser(string userToken)
        {
            // TODO: validate token internally

            return Task.FromResult(true);

            //var url = await _config.GetConfig(IConfigService.CORP_LOGIN_URL);
            //var (_, err) = await _httpClient.GetString(
            //    $"{url}?token={userToken}",
            //    new Dictionary<string, string>()
            //    {
            //        {"Authorization", $"Bearer {userToken}"}
            //    }
            //    );

            //return err == null;
        }

        private static string Base64UrlToBase64(string base64Url)
        {
            var base64 = base64Url
                .Replace("-", "+")
                .Replace("_", "/")
                .Replace(".", "=");
            var padding = base64.Length % 4;
            if (padding > 0)
            {
                base64 += new string('=', 4 - padding);
            }

            return base64;
        }
    }
}