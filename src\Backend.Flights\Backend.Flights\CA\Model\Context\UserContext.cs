using System.Collections.Generic;
using Newtonsoft.Json;


namespace Backend.Flights.CA.Model.Context
{
    public class User
    {
        public Dictionary<string, string> GatewayHeaders { get; private set; }
        public int? BranchId { get; private set; }
        public string UserType { get; private set; }
        public string AgentSign { get; private set; }
        public string AgencyId { get; private set; }
        public string Name { get; private set; }
        [JsonProperty("user")]
        public string UserCorp { get; private set; }
        public string Email { get; private set; }

        public User(Dictionary<string, string> gatewayHeaders, int? branchId = null, string userType = null, string agentSign = null, string agencyId = null, string name = null, string userCorp = null, string email = null)
        {
            this.GatewayHeaders = gatewayHeaders;
            this.BranchId = branchId;
            this.UserType = userType;
            this.AgentSign = agentSign;
            this.AgencyId = agencyId;
            this.Name = name;
            this.UserCorp = userCorp;
            this.Email = email;
        }
    }
}