using Microsoft.Extensions.Caching.Memory;
using System;
using System.Threading.Tasks;

namespace Backend.Flights.CA.Services.Cache
{
    public class LocalCacheService : ILocalCacheService
    {
        private readonly IMemoryCache _cache;

        public LocalCacheService(IMemoryCache cache)
        {
            _cache = cache;
        }

        public Task<T> Get<T>(string key) where T : class
        {
            return Task.FromResult(_cache.TryGetValue(key, out var val) ? val as T : null);
        }

        public Task Set<T>(string key, T value, int ttlSeconds = 3600)
        {
            _cache.Set(key, value, new MemoryCacheEntryOptions()
            {
                SlidingExpiration = TimeSpan.FromSeconds(ttlSeconds)
            });

            return Task.CompletedTask;
        }
    }
}