﻿using Backend.Flights.CA.Model.Search.Result;
using MongoDB.Driver.Linq;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Backend.Flights.CA.UseCase.Upsell
{
    public class ComputeUpsell
    {
        private readonly GroupSameFlights _groupSameFlights;

        public ComputeUpsell(GroupSameFlights groupSameFlights)
        {
            _groupSameFlights = groupSameFlights;
        }

        public IReadOnlyDictionary<string, List<string>> Execute(Dictionary<string, SearchResultFlight> recommendations)
        {
            var groupedFlights = _groupSameFlights.Execute(recommendations);

            var res = new ConcurrentDictionary<string, List<string>>();


            Parallel.ForEach(groupedFlights, flightGroup =>
            {
                for (var flightIndex = 0; flightIndex < flightGroup.Count; flightIndex++)
                {
                    var flightKey = flightGroup[flightIndex];
                    var flight = recommendations[flightKey];

                    res.TryAdd(
                        flightKey,
                        flightGroup
                        .Skip(flightIndex + 1)
                        .Where(upsellKey => flight.IsUpssel(recommendations[upsellKey]))
                        .Take(3)
                        .ToList());
                }
            });

            return res;
        }
    }
}
