﻿using Backend.Flights.CA.Model;
using Backend.Flights.CA.UseCase.Id;
using System;
using System.Text;

namespace Backend.Flights.CA.Services.Id
{
    public class NanoidRandomIdService : IRandomIdService
    {
        private readonly IdConfig _config;
        private int[] _currentId;
        private readonly object _currentIdLock = new { };

        public NanoidRandomIdService(GetIdConfig getIdConfig)
        {
            _config = getIdConfig.Config;
            _currentId = new int[] { 0 };
        }

        private string CurrentId()
        {
            var stb = new StringBuilder();
            foreach (var i in _currentId)
            {
                stb.Append(_config.Characters[i]);
            }
            return stb.ToString();
        }

        private string MoveNextId()
        {
            lock (_currentIdLock)
            {
                var result = CurrentId();
                var lastIdx = _currentId.Length - 1;
                var nextValue = _currentId[lastIdx] + 1;
                if (nextValue >= _config.Characters.Length)
                {
                    _currentId[lastIdx] = 0;
                    Array.Resize(ref _currentId, _currentId.Length + 1);
                    _currentId[lastIdx + 1] = 0;
                }
                else
                {
                    _currentId[lastIdx] = nextValue;
                }
                return result;
            }
        }

        public string RandomId()
        {
            return Nanoid.Nanoid.Generate(_config.Characters, _config.Size);
        }

        public string SequentialId()
        {
            return MoveNextId();
        }
    }

}