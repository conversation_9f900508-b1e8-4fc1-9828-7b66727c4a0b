﻿using System.Collections.Generic;

namespace Backend.Flights.Models.Serviceui
{
    public class GetSearchStatusRequest
    {
        public GetSearchStatusRequestEnvelope req { get; set; }
        public List<PullStatusFrom> pullStatusFrom { get; set; }
    }

    public class GetSearchStatusRequestEnvelope
    {
        public int AirOrdering { get; set; }
        public int ItemsPerPageAir { get; set; }
        public TravelEngineGetStatusSearchRQ TravelEngineGetStatusSearchRQ { get; set; }
    }

    public class TravelEngineGetStatusSearchRQ
    {
        public string SearchId { get; set; }
        public string Culture { get; set; }
        public SearchData SearchData { get; set; }
    }

    public class PullStatusFrom
    {
        public string Key { get; set; }
        public string Value { get; set; }
    }

}
