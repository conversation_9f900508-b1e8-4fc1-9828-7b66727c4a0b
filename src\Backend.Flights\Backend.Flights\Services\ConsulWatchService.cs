using Microsoft.Extensions.Hosting;
using System;
using System.Collections.Concurrent;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace Backend.Flights.Services
{
    public class ConsulWatchService : BackgroundService, IConsulWatch
    {
        #region Constantes

        private const int INT_UPDATE_INTERVAL = 5 * 60 * 1000; // 5 minutos

        #endregion

        #region Declarações

        private readonly IConsul _consul;
        private readonly ConcurrentDictionary<string, string> _cache = new ConcurrentDictionary<string, string>();

        #endregion

        #region Eventos

        public event EventHandler<ConsulKeyUpdatedEventArgs> KeyChanged;

        #endregion

        #region Construtor

        public ConsulWatchService(IConsul consul)
        {
            _consul = consul;
        }

        #endregion

        #region Funções públicas

        public async Task<string> WatchKey(string key)
        {
            return GetFromCache(key) ?? await AddToCache(key);
        }

        #endregion

        #region Funções protegidas

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                await Task.Delay(INT_UPDATE_INTERVAL, stoppingToken);
                foreach (var key in _cache.Keys.ToArray())
                {
                    var value = await _consul.GetValueOrEmptyAsync(key);

                    if (_cache.TryGetValue(key, out var cacheValue) &&
                        cacheValue.Equals(value))
                    {
                        continue;
                    }

                    _cache.AddOrUpdate(key, (_) => value, (_1, _2) => value);
                    OnKeyChanged(key, value);
                }
            }
        }

        #endregion

        #region Funções privadas

        private string GetFromCache(string key)
        {
            return _cache.TryGetValue(key, out var value) ? value : null;
        }

        private async Task<string> AddToCache(string key)
        {
            var value = await _consul.GetValueOrEmptyAsync(key);

            _cache.AddOrUpdate(key, (_) => value, (_1, _2) => value);
            OnKeyChanged(key, value);

            return value;
        }

        private void OnKeyChanged(string key, string value)
        {
            KeyChanged?.Invoke(this, new ConsulKeyUpdatedEventArgs(key, value));
        }

        #endregion
    }
}