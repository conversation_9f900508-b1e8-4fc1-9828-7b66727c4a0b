﻿namespace Backend.Flights.Models.Serviceui
{

    public class SearchResponseError
    {
        public SearchDetailsResponseError ServiceError { get; set; }
    }

    public class SearchDetailsResponseError : Error
    {
        public string Operation {get; set;}
        public bool OfferUpsell { get; set; }
        public string Provider { get; set; }
        public string PriceGroupId { get; set; }
        public bool CanDirectToNewCheckout { get; set; }
        public SegmentAndFlightIdxResponseError[] CacheIds { get; set; }
        public string SearchId { get; set; }
        public string ClientId { get; set; }
        public FlightExtraDataResponseError FlightId { get; set; }
        public string[] Flights { get; set; }
        public bool CanDirectToPreCheckout { get; set; }
        public string[] OperationalId { get; set; }

        public struct SegmentAndFlightIdxResponseError
        {
            public int SegmentIndex { get; set; }
            public int ScheduleIndex { get; set; }
            public int SegmentHour { get; set; }
        }

        public struct FlightExtraDataResponseError
        {
            public string[] OperationalId { get; set; }
            public string[] Flights { get; set; }
        }
    }
}
