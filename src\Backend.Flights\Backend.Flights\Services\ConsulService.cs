﻿using Backend.Flights.Util;
using Consul;
using System;
using System.Text;
using System.Threading.Tasks;

namespace Backend.Flights.Services
{
    public class ConsulService : IConsul
    {
        private readonly ConsulClient _consul;

        public ConsulService(string url)
        {
            _consul = new ConsulClient(x => { x.Address = new Uri(url); });
        }

        public string GetValue(string key)
        {
            try
            {
                var result = Encoding.UTF8.GetString(_consul.KV.Get(key).Result.Response.Value);
                $"[Chave Consul]: [{key}] encontrada. Valor: {result}".LogInfo();
                return result;
            }
            catch (Exception ex)
            {
                var message = $"[Chave Consul]: [{key}] não encontrada. Erro: {ex.Message}.";
                $"{message}".LogError();
                throw new Exception(message);
            }
        }

        public string GetValueOrEmpty(string key)
        {
            try
            {
                var byteValue = _consul.KV.Get(key)?.Result?.Response?.Value;
                var result = byteValue == null ? "" : Encoding.UTF8.GetString(byteValue);
                $"[Chave Consul]: [{key}] encontrada. Valor: {result}".LogInfo();
                return result;
            }
            catch
            {
                return string.Empty;
            }
        }

        public async Task<string> GetValueAsync(string key)
        {
            try
            {
                var result = Encoding.UTF8.GetString((await _consul.KV.Get(key)).Response.Value);
                $"[Chave Consul]: [{key}] encontrada. Valor: {result}".LogInfo();
                return result;
            }
            catch (Exception ex)
            {
                var message = $"[Chave Consul]: [{key}] não encontrada. Erro: {ex.Message}.";
                $"{message}".LogError();
                throw new Exception(message);
            }
        }
        public async Task<string> GetValueOrEmptyAsync(string key)
        {
            try
            {
                var byteValue = (await _consul.KV.Get(key))?.Response?.Value;
                var result = byteValue == null ? "" : Encoding.UTF8.GetString(byteValue);
                $"[Chave Consul]: [{key}] encontrada. Valor: {result}".LogInfo();
                return result;
            }
            catch
            {
                return string.Empty;
            }
        }
    }
}
