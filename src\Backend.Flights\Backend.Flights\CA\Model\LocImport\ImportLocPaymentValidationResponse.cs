﻿using Newtonsoft.Json;

namespace Backend.Flights.CA.Model.LocImport
{
    /// <summary>
    /// Representa a resposta completa do serviço de payments/credipax.
    /// </summary>
    public class ImportLocPaymentValidationResponse
    {
        public string Status { get; set; }
        public string Message { get; set; }

        // Lista de pedidos (orders)
        public Order[] Orders { get; set; }

        // Lista de voos adicionais (pode existir fora dos pedidos)
        public Flight[] Flights { get; set; }

        // Lista de passageiros adicionais (fora dos pedidos)
        [JsonProperty("paxs")]
        public Pax[] Paxs { get; set; }
    }

    public class Order
    {
        public string Pnr { get; set; }
        public string Provider { get; set; }

        [JsonProperty("order")]
        public long? OrderNumber { get; set; }

        public int? Branch { get; set; }
        public string PackageGroup { get; set; }
        public string Active { get; set; }
        public decimal? DiscPromo { get; set; }
        public decimal? DiscEntry { get; set; }
        public Credit[] Credit { get; set; }
        public string Mkp { get; set; }

        [JsonProperty("var")]
        public string Var { get; set; }

        public string Bag { get; set; }
        public string TxAd { get; set; }
        public string QtPax { get; set; }

        // Voos e passageiros podem vir dentro do pedido também.
        public Flight[] Flights { get; set; }
        [JsonProperty("paxs")]
        public Pax[] Paxs { get; set; }
    }

    public class Credit
    {
        public decimal? Credipax { get; set; }
        public decimal? Balance { get; set; }
        public decimal? CreditValue { get; set; }
        public int? ContractorCode { get; set; }
        public int? ContractorId { get; set; }
        public Pax[] Pax { get; set; }
        public int? Entry { get; set; }
        public decimal? PriceWithTax { get; set; }
    }

    public class Flight
    {
        public Segment[] Segments { get; set; }
    }

    public class Segment
    {
        public string Departure { get; set; }
        public string DepartureDate { get; set; }
        public string Arrival { get; set; }
        public string ArrivalDate { get; set; }
        public Leg[] Legs { get; set; }
    }

    public class Leg
    {
        public OperatedBy OperatedBy { get; set; }
        public int? FlightNumber { get; set; }
        public string Departure { get; set; }
        public string Arrival { get; set; }
        public string DepartureDate { get; set; }
        public string ArrivalDate { get; set; }
    }

    public class OperatedBy
    {
        public string Name { get; set; }
        public string Iata { get; set; }
    }

    public class Pax
    {
        public int? Id { get; set; }
        public string FirstName { get; set; }
        public string Surname { get; set; }
        public string Age { get; set; }
        public string BirthDate { get; set; }
    }
}