﻿using Backend.Flights.Models;
using System.Net;

namespace Backend.Flights.Services
{
    public interface IContextBrand
    {
        public const string COOKIE_ACCESS_TOKEN = "access_token";
        public const string LOJA_CTX = "lojas_wl";
        public const string AGENT_SIGN_LOJAS = "LOJ";

        BrandContext Context { get; }
        void SetAuthHeaders(WebClient wc, BrandContext bc = null);
    }
}