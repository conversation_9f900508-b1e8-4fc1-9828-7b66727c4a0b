using Backend.Flights.CA.Model.Context;
using System.Threading.Tasks;

namespace Backend.Flights.CA.Services.UserToken
{
    public interface IUserTokenService
    {
        public const string USER_TYPE_VENDOR = "VENDOR";

        public const string USER_CTX_LOJA = "lojas_wl";

        public const string COOKIE_ACCESS_TOKEN = "access_token";

        public const string AGENT_SIGN_LOJAS = "LOJ";

        User GetUserInfo(string userToken);
        Task<bool> ValidateUser(string userToken);
    }
}