using Backend.Flights.CA.Model.Cart;

namespace Backend.Flights.CA.Model.Opportunities
{
    public class OpportunitiesResponse
    {
        public OpportunitiesResponseItem[] AvailableItems { get; set; }
        public OpportunitiesResponseItem[] SelectedItems { get; set; }
    }

    public class OpportunitiesResponseItem : CartItem
    {
        public dynamic Original;
        public OpportunitiesResponseItem(int rph, string rateToken, dynamic original, int? routeRPH) : base(rph, rateToken, routeRPH)
        {
            this.Original = original;
        }
    }
}