{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:52384", "sslPort": 44356}}, "$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"TI": {"commandName": "Project", "launchBrowser": false, "launchUrl": "health", "applicationUrl": "https://localhost:5001;http://localhost:5000", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "ti", "PROJECT_NAME": "sub-backend-flights", "SEARCH_FLIGHTS_API_KEY": "GQoEcktP0n1hEbzAR5aFvNYrxMHi", "MONGODB_USERNAME_OCI": "db_flights", "MONGODB_PASSWORD_OCI": "joAW2gPg0Se2dsQHzZx0Ny79j", "MONGODB_DATABASE": "db_flights"}}, "QA": {"commandName": "Project", "launchBrowser": false, "launchUrl": "health", "applicationUrl": "https://localhost:5001;http://localhost:5000", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "qa", "PROJECT_NAME": "sub-backend-flights", "SEARCH_FLIGHTS_API_KEY": "GQoEcktP0n1hEbzAR5aFvNYrxMHi", "MONGODB_USERNAME_OCI": "db_flights", "MONGODB_PASSWORD_OCI": "joAW2gPg0Se2dsQHzZx0Ny79j", "MONGODB_DATABASE": "db_flights"}}, "PROD": {"commandName": "Project", "launchBrowser": false, "launchUrl": "health", "applicationUrl": "https://localhost:5001;http://localhost:5000", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "prod", "PROJECT_NAME": "sub-backend-flights", "SEARCH_FLIGHTS_API_KEY": "GQoEcktP0n1hEbzAR5aFvNYrxMHi", "MONGODB_USERNAME_OCI": "db_flights", "MONGODB_PASSWORD_OCI": "joAW2gPg0Se2dsQHzZx0Ny79j", "MONGODB_DATABASE": "db_flights"}}}}