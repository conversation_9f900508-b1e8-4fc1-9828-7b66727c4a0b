using System.Collections.Generic;
using Newtonsoft.Json;

namespace Backend.Flights.CA.Model.LocImport
{
    public class CheckListAirResponse
    {
         [JsonProperty("localizadorProvedor")]
        public string LocalizadorProvedor { get; set; }
        
        [JsonProperty("gdsOrigem")]
        public string GdsOrigem { get; set; }
        
        [JsonProperty("provedorOrigem")]
        public string ProvedorOrigem { get; set; }
        
        [JsonProperty("dataVencimento")]
        public string DataVencimento { get; set; }
        
        [JsonProperty("status")]
        public string Status { get; set; }
        
        [JsonProperty("tipoTarifa")]
        public string TipoTarifa { get; set; }
        
        [JsonProperty("ciaAereaValidadora")]
        public string CiaAereaValidadora { get; set; }
        
        [JsonProperty("checkList")]
        public Dictionary<string, bool> CheckList { get; set; }
        
        [JsonProperty("erro")]
        public string Erro { get; set; }
        
        [JsonProperty("dataEmbarque")]
        public string DataEmbarque { get; set; }
        
        [JsonProperty("mec")]
        public string Mec { get; set; }
        public string ErrorText => Erro;
    }
}