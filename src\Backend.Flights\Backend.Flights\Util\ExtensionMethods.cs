﻿using Backend.Flights.Models.Search;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace Backend.Flights.Util
{
    public static class ExtensionMethods
    {
        public static bool HasCount(this ICollection list)
        {
            return list != null && list.Count > 0;
        }

        public static bool IsNotEmptyAndEquals(this string str, string strCompare)
        {
            return (!string.IsNullOrEmpty(str) && str.Equals(strCompare));
        }

        public static bool IsNotEmptyAndTrue(this bool? bln)
        {
            return bln.HasValue && bln == true;
        }

        public static DateTime ToDateTime(this DateValues dateValues)
        {
            return new DateTime(dateValues.Year, dateValues.Month, dateValues.Day);
        }

        public static string ToTitleCase(this string str)
        {
            string title = string.Empty;

            if (!string.IsNullOrEmpty(str))
                title = CultureInfo.CurrentCulture.TextInfo.ToTitleCase(str.ToLower());

            return title;
        }
    }

    public static class HttpRequestExtensions
    {
        public static Dictionary<string, string> GetHeadersHttpRequestMessage(this HttpRequestMessage httpRequestMessage)
        {
            return httpRequestMessage.Headers.ToDictionary(header => header.Key, header => header.Value.FirstOrDefault());
        }
    }

    public static class HttpRequestMessageExtensions
    {
        public static async Task<string> ToCurlCommand(this HttpRequestMessage request, bool forcePrint = false, int maxLength = 30000)
        {
            try
            {
#if LOCAL
                forcePrint = true;
#endif

                var printLog = forcePrint;

                var endpointsToIgnore = new List<string> { "/opportunities" };

#if LOCAL
                endpointsToIgnore = new List<string>();
#endif

                var url = request.RequestUri.AbsoluteUri;

                var separator = $"\n{string.Concat(Enumerable.Repeat("=", 100))}\n";
                var method = request.Method.ToString();
                var headers = string.Join("\n", request.Headers.Select(h => $"-H '{h.Key}: {string.Join(",", h.Value)}'"));

                var requestBody = "";
                if (request.Content != null)
                {
                    // Adiciona o header de content type se ele não existir, pegando o request.Content
                    if (request.Content.Headers.ContentType != null)
                    {
                        headers += $"\n-H 'Content-Type: {request.Content.Headers.ContentType.MediaType}'";
                    }

                    requestBody = await request.Content.ReadAsStringAsync();
                    requestBody = $"--data '{requestBody}'";
                }

                var curlCommand = $"{separator}curl -X {method} '{url}' {headers} {requestBody}'{separator}";

                // Trunca o comando curl se ele exceder o comprimento máximo permitido
                if (curlCommand.Length > maxLength)
                {
                    curlCommand = curlCommand[..(maxLength - 3)] + "...";
                }

                Console.WriteLine(curlCommand);

                // Verifica se a URL contém algum dos endpoints a serem ignorados ou se não é para printar o Log
                if (!printLog || endpointsToIgnore.Any(endpoint => url.Contains(endpoint, StringComparison.OrdinalIgnoreCase)))
                {
                    return "";
                }

                return curlCommand;
            }
            catch (Exception)
            {
                return "";
            }
        }
    }

    public static class HttpResponseMessageExtensions
    {
        public static async Task<string> ToCurlCommand(this HttpResponseMessage response, bool forcePrint = false, int maxLength = 30000)
        {
            try
            {
#if LOCAL
                forcePrint = true;
#endif

                var printLog = forcePrint;

                var endpointsToIgnore = new List<string> { "/opportunities" };
                var url = response.RequestMessage.RequestUri.AbsoluteUri;

#if LOCAL
                endpointsToIgnore = new List<string>();
#endif

                var separator = $"\n{string.Concat(Enumerable.Repeat("=", 100))}\n";
                var method = response.RequestMessage.Method.ToString();
                var headers = string.Join("\n", response.Headers.Select(h => $"-H '{h.Key}: {string.Join(",", h.Value)}'"));

                var requestBody = "";
                if (response.Content != null && response.RequestMessage.Method != HttpMethod.Get)
                {
                    requestBody = await response.Content.ReadAsStringAsync();
                    requestBody = $"--data '{requestBody}'";
                }

                var curlCommand = $"{separator}curl -X {method} '{url}' {headers} {requestBody}'{separator}";

                // Trunca o comando curl se ele exceder o comprimento máximo permitido
                if (curlCommand.Length > maxLength)
                {
                    curlCommand = curlCommand.Substring(0, maxLength - 3) + "...";
                }

                Console.WriteLine(curlCommand);

                // Verifica se a URL contém algum dos endpoints a serem ignorados ou se não é para printar o Log
                if (!printLog || endpointsToIgnore.Any(endpoint => url.Contains(endpoint, StringComparison.OrdinalIgnoreCase)))
                {
                    return "";
                }

                return curlCommand;
            }
            catch (Exception)
            {
                return "";
            }
        }
    }

    public static class ExceptionExtensions
    {
        public static string GetErrorLocation(this Exception ex, string message = "")
        {
            try
            {
                StackTrace stackTrace = new StackTrace(ex, true);
                StackFrame firstFrame = stackTrace.GetFrame(0);
                StackFrame lastFrame = stackTrace.GetFrame(stackTrace.FrameCount - 1);

                static string GetFrameDetails(StackFrame frame)
                {
                    string method = frame.GetMethod().Name;
                    string reflectionType = frame.GetMethod().ReflectedType.Name;

                    // Extrair o texto entre <>
                    string reflectionTypeName = reflectionType;
                    var match = System.Text.RegularExpressions.Regex.Match(reflectionType, @"<([^>]+)>");
                    if (match.Success)
                    {
                        reflectionTypeName = match.Groups[1].Value;
                    }

                    int line = frame.GetFileLineNumber();
                    return $"method '{method}', line {line}, type '{reflectionTypeName}'";
                }

                string firstFrameDetails = GetFrameDetails(firstFrame);
                string lastFrameDetails = GetFrameDetails(lastFrame);

                return $"{message} Error occurred: First frame: {firstFrameDetails}. Last frame: {lastFrameDetails}. Exception message: {ex.Message}";
            }
            catch
            {
                return $"{message} {ex.Message}";
            }
        }
    }
}
