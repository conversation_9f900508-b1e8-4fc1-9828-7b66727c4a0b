using System;
using System.Collections.Generic;

namespace Backend.Flights.CA.Model.Context
{
    public static class UserHeaderGenerator
    {
        public static Dictionary<string, string> GenerateGatewaySoapHeaders(User user, string userToken)
        {
            try
            {
                var headers = new Dictionary<string, string>(user?.GatewayHeaders ?? new Dictionary<string, string>())
                {
                    ["Gtw-Sec-User-Token"] = userToken
                };

                // Se não houver "gtw-transaction-id", gera um novo
                if (!headers.TryGetValue("gtw-transaction-id", out string transactionId) || string.IsNullOrWhiteSpace(transactionId))
                {
                    transactionId = $"atlas-{Guid.NewGuid()}";
                    headers["gtw-transaction-id"] = transactionId;
                }

                // Define ou sobrescreve o header "Gtw-Branch-Id" se o BranchId estiver preenchido
                if (user != null && user.BranchId.HasValue)
                {
                    headers["Gtw-Branch-Id"] = user.BranchId.Value.ToString();
                }

                headers["Gtw-Agency-Id"] = user != null && !string.IsNullOrEmpty(user.AgencyId) ? user.AgencyId : "0";

                return headers;
            }
            catch (Exception ex)
            {
                Console.WriteLine("[GenerateGatewaySoapHeaders]: Ocorreu um erro ao gerar os headers do usuário: {0}", ex.Message);
                return null;
            }
        }
    }
}