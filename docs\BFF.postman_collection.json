{"info": {"_postman_id": "fd4552b7-dc6b-4997-9e88-9b66ad958ff7", "name": "BFF", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Local", "item": [{"name": "Search - SUB", "request": {"method": "POST", "header": [{"key": "x-api-version", "value": "1.0", "type": "text"}, {"key": "X-BRAND-URL", "value": "www.submarinoviagens.com.br", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"Adults\": 1,\n    \"Children\": 0,\n    \"Infants\": 0,\n    \"DirectFlight\": false,\n    \"Cabin\": \"eco\",\n    \"CityPairs\": [\n        {\n            \"OriginIata\": \"IGU\",\n            \"DestinationIata\": \"SCL\",\n            \"DepartureDate\": {\n                \"Year\": 2021,\n                \"Month\": 7,\n                \"Day\": 15\n            }\n        }\n    ],\n    \"UserAgent\": \"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.192 Safari/537.36\",\n    \"SearchId\": \"4fea4692-ae94-4b6b-acc5-857dd95da631\",\n    \"ClientId\": \"GA1.3.631364409.1612374023\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://localhost:5001/api/search/sync", "protocol": "https", "host": ["localhost"], "port": "5001", "path": ["api", "search", "sync"]}}, "response": []}]}, {"name": "Prod", "item": [{"name": "Search - SUB", "request": {"method": "POST", "header": [{"key": "x-api-version", "value": "3.0", "type": "text"}, {"key": "X-BRAND-URL", "value": "www.submarinoviagens.com.br", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"Adults\": 1,\n    \"Children\": 0,\n    \"Infants\": 0,\n    \"DirectFlight\": false,\n    \"Cabin\": \"eco\",\n    \"CityPairs\": [\n        {\n            \"OriginIata\": \"IGU\",\n            \"DestinationIata\": \"SCL\",\n            \"DepartureDate\": {\n                \"Year\": 2021,\n                \"Month\": 7,\n                \"Day\": 15\n            }\n        }\n    ],\n    \"SearchId\": \"4fea4692-ae94-4b6b-acc5-857dd95da631\",\n    \"ClientId\": \"GA1.3.631364409.1612374023\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://sub-backend-flights.k8s-cvc.com.br/api/search/sync", "protocol": "http", "host": ["sub-backend-flights", "k8s-cvc", "com", "br"], "path": ["api", "search", "sync"], "query": [{"key": "x-brand-url", "value": "www.cvc.com.br", "disabled": true}]}}, "response": []}]}]}