﻿using Backend.Flights.CA.Model.LocImport;
using Backend.Flights.CA.Services.Config;
using Backend.Flights.CA.Services.Http;
using Backend.Flights.CA.Services.Id;
using Backend.Flights.CA.Services.Json;
using Backend.Flights.CA.UseCase.Config;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Web;

using ValidateResult = Backend.Flights.CA.SafeResult<
    Backend.Flights.CA.Model.LocImport.ImportLocPaymentValidationResponse,
    Backend.Flights.CA.Model.HttpErrorResult>;

namespace Backend.Flights.CA.Services.LocImportValidator
{
    public class SysturLocImportPaymentValidator : ILocImportPaymentValidator
    {
        private readonly IHttpClient _http;
        private readonly IWatchConfig _config;
        private readonly IJsonService _json;
        private readonly IRandomIdService _randomId;

        public SysturLocImportPaymentValidator(
            IHttpClient http,
            IWatchConfig config,
            IRandomIdService randomId,
            IJsonService json)
        {
            _http = http;
            _config = config;
            _randomId = randomId;
            _json = json;
        }

        public async Task<ValidateResult> Validate(string loc, int branch, string userToken)
        {
            var (validationResponse, httpError, httpResponse) = await _http.GetString(
                $"{await _config.GetConfig(IConfigService.IMPORTALOC_SYSTUR_PAYMENT_VALIDATE)}p_localizador={HttpUtility.UrlEncode(loc)}&p_cd_filial={branch}",
                new Dictionary<string, string>()
                {
                { "gtw-sec-user-token", userToken},
                { "gtw-transaction-id", $"atlas-flt-{_randomId.RandomId()}"}
                });

            if (httpError != null)
            {
                return ValidateResult.Fail(httpError);
            }

            return ValidateResult.Success(
                _json.Deserialize<ImportLocPaymentValidationResponse>(validationResponse)
                );
        }
    }
}
