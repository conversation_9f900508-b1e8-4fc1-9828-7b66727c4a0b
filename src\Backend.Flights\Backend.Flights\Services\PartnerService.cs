﻿using Backend.Flights.CA.Services.Http;
using Backend.Flights.CA.Services.Json;
using Backend.Flights.Models;
using Backend.Flights.Models.Partners;
using Backend.Flights.Util;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;

namespace Backend.Flights.Services
{
    public class PartnerService
    {

        private readonly ILogger _logger;
        private readonly BrandContext brandCtx;
        private readonly IHttpClient _httpClient;
        private readonly IJsonService _json;

        public PartnerService(ILogger logger, BrandContext brandCtx, IHttpClient httpClient, IJsonService json)
        {
            _logger = logger;
            this.brandCtx = brandCtx;
            _httpClient = httpClient;
            _json = json;
        }

        public async Task SearchFlightsPartnerPrecification(IPartnerRequest req, HttpResponse originResponse)
        {
            Stopwatch t = new Stopwatch();

            try
            {
                t.Start();

                using (var request = new HttpRequestMessage(HttpMethod.Post, brandCtx.PartnerData.BrokerUrl))
                using (var httpContent = CreateHttpContent(req))
                {
                    request.Headers.Add("Authorization", brandCtx.PartnerData.Authorization);
                    request.Headers.Add("Accept-Encoding", "gzip");

                    request.Content = httpContent;

                    var headers = HttpRequestExtensions.GetHeadersHttpRequestMessage(request);
                    var (responsePayload, httpError, httpResponse) = await _httpClient.GetString(brandCtx.PartnerData.BrokerUrl, headers, timeout: 35, returnOnHeaderRecivied: true);

                    httpResponse.EnsureSuccessStatusCode();
                    t.Stop();

                    LogPartnerSearch(req, t.ElapsedMilliseconds, "success", "Sucesso na busca ao parceiro Oktoplus!.");

                    originResponse.StatusCode = 200;
                    originResponse.ContentType = "application/json";
                    var sw = new StreamWriter(originResponse.Body);

                    await httpResponse.Content.CopyToAsync(sw.BaseStream);

                    await sw.FlushAsync();
                    await sw.DisposeAsync();
                }
            }
            catch (Exception e)
            {
                originResponse.StatusCode = 500;
                await originResponse.WriteAsync(e.Message);
                LogPartnerSearch(req, t.ElapsedMilliseconds, "error", e.Message);
            }
        }

        private static HttpContent CreateHttpContent(object content)
        {
            HttpContent httpContent = null;

            if (content != null)
            {
                var ms = new MemoryStream();
                SerializeJsonIntoStream(content, ms);
                ms.Seek(0, SeekOrigin.Begin);
                httpContent = new StreamContent(ms);
                httpContent.Headers.ContentType = new MediaTypeHeaderValue("application/json");
            }

            return httpContent;
        }

        private static void SerializeJsonIntoStream(object value, Stream stream)
        {
            using (var sw = new StreamWriter(stream, new System.Text.UTF8Encoding(false), 1024, true))
            using (var jtw = new JsonTextWriter(sw) { Formatting = Formatting.None })
            {
                var js = new JsonSerializer();
                js.Serialize(jtw, value);
                jtw.Flush();
            }
        }


        private void LogPartnerSearch(IPartnerRequest req, long duration, string status, string message)
        {
            _logger.BeginScope(new Dictionary<string, object>
            {
                ["duration"] = duration,
                ["operation"] = "PARTNERSEARCH",
                ["short_message"] = status,
                ["message"] = req,
            });
            _logger.LogInformation(message);
        }
    }
}