﻿using Microsoft.Extensions.Logging;
using Microsoft.IO;
using System;
using System.Diagnostics;
using System.IO.Compression;

namespace Backend.Flights.CA.UseCase.Checkout
{
    public class SerializeCheckoutCache
    {
        private readonly ILogger _logger;
        private readonly RecyclableMemoryStreamManager _recyclableMemoryStreamManager;

        public SerializeCheckoutCache(
            ILogger<SerializeCheckoutCache> logger,
            RecyclableMemoryStreamManager recyclableMemoryStreamManager)
        {
            _recyclableMemoryStreamManager = recyclableMemoryStreamManager;
            _logger = logger;
        }

        public string Execute(Model.Cache.CartCheckout checkout)
        {
            using var memoryStream = _recyclableMemoryStreamManager.GetStream();

            var ts = new Stopwatch();
            ts.Start();

            ProtoBuf.Serializer.Serialize(memoryStream, checkout);

            ts.Stop();

            double uncompressedSize = memoryStream.Length / 100.0;

            // [log-disabled] _logger.LogInformation("Serialized {size} bytes in {time}ms", memoryStream.Length, ts.ElapsedMilliseconds);

            memoryStream.SetLength(0);

            using var deflateStream = new DeflateStream(memoryStream, CompressionLevel.Fastest, true);

            ts.Reset();
            ts.Start();

            ProtoBuf.Serializer.Serialize(deflateStream, checkout);
            deflateStream.Flush();

            ts.Stop();

            // [log-disabled] _logger.LogInformation("Serialized {size} bytes compressed by deflate ({rate}%) in {time}ms",
            //     memoryStream.Length,
            //     memoryStream.Length / uncompressedSize,
            //     ts.ElapsedMilliseconds);

            return Convert.ToBase64String(memoryStream.ToArray());
        }
    }
}
