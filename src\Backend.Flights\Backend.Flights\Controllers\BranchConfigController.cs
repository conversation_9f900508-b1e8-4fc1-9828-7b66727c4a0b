using Backend.Flights.Models.Branch;
using Backend.Flights.Services;
using Microsoft.AspNetCore.Mvc;

namespace Backend.Flights.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [ApiVersion("1.0")]
    public class BranchConfigController : ControllerBase
    {
        private readonly IBranchConfigService _branchConfigService;


        public BranchConfigController(IBranchConfigService branchConfigService)
        {
            _branchConfigService = branchConfigService;
        }

        [HttpGet("{branchId:int}")]
        public ActionResult<BranchConfig> IsApiVersion4(int branchId)
        {
            return Ok(_branchConfigService.VerifyApiByBranch(branchId));
        }
    }
}