﻿namespace Backend.Flights.CA.Model.LocImport
{
    public class LocImportErrorResult
    {
        public int HttpCode { get; }
        public string HttpMessage { get; }
        public string ErrorMessage { get; }
        public string GtwErrorCode { get; }

        public bool IsHttpError => !string.IsNullOrEmpty(HttpMessage);

        private LocImportErrorResult(int httpCode, string httpMessage, string errorMessage, string gtwErrorCode)
        {
            HttpCode = httpCode;
            HttpMessage = httpMessage;
            ErrorMessage = errorMessage;
            GtwErrorCode = gtwErrorCode;
        }

        public static LocImportErrorResult HttpError(int code, string message, string gtwErrorCode)
        {
            return new LocImportErrorResult(code, message, $"HTTP {code} - {message}", gtwErrorCode);
        }

        public static LocImportErrorResult HttpError(int code, string message)
        {
            return new LocImportErrorResult(code, message, $"HTTP {code} - {message}", null);
        }

        public static LocImportErrorResult Error(string message)
        {
            return new LocImportErrorResult(0, null, message, null);
        }
    }
}
