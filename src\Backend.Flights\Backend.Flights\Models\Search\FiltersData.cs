﻿using System.Collections.Generic;

namespace Backend.Flights.Models.Search
{
    public class FiltersData
    {
        public List<FiltersAirCompanies> AirCompanies { get; set; }
        public List<FiltersStops> NumberOfStops { get; set; }
        public FiltersPrice Price { get; set; }
        public List<FiltersDuration> Duration { get; set; }
        public List<FiltersAirports> AirportsInbound { get; set; }
        public List<FiltersAirports> AirportsOutbound { get; set; }
    }

    public class FiltersAirCompanies
    {
        public string CiaCode { get; set; }
        public string CiaName { get; set; }
        public decimal MinPrice { get; set; }
    }

    public class FiltersStops
    {
        public decimal MinPrice { get; set; }
        public int Stops { get; set; }
    }

    public class FiltersPrice
    {
        public decimal MinPrice { get; set; }
        public decimal MaxPrice { get; set; }
    }

    public class FiltersDuration
    {
        public int MinDuration { get; set; }
        public int MaxDuration { get; set; }
    }

    public class FiltersAirports
    {
        public string Description { get; set; }
        public string Iata { get; set; }
        public decimal MinPrice { get; set; }
    }

}
