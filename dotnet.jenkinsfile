@Library('cvc-jen<PERSON>-lib@3.0.0')
import br.com.cvccorp.jenkins.Commons
final commons = new Commons()

final _projectName = "sub-backend-flights"
final _namespace = "sub-flights"
final _gitUrl = "******************:Desenvolvimento-MS/sub-backend-flights.git"
final _appUrlTiSub = "sub-backend-flights.k8s-ti-cvc.com.br"
final _appUrlQaSub = "sub-backend-flights.k8s-qa-cvc.com.br"
final _appUrlProdSub = "sub-backend-flights.k8s-cvc.com.br"


pipeline {
    agent any
    stages {
        stage('Deploy TI') {
            steps {
                script {
                    deployTI(_projectName, _namespace, _gitUrl, _appUrlTiSub) {
                        stage('Unit Tests') {
                           //commons.dotnet_unit_tests("Backend.Flights")
                        }
                    }
                }
            }
        }
        stage('Deploy QA') {
            steps {
                script {
                    deployQA(_projectName, _namespace, _gitUrl,_appUrlQaSub) {
                    }
                }
            }
        }
        stage('Deploy PROD') {
            steps{
                script {
                    deployPRD(_projectName, _namespace, _gitUrl,_appUrlProdSub) {
                    }
                }    
            }
        }
    }
}