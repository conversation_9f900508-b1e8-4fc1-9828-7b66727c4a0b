<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Backend.Flights</name>
    </assembly>
    <members>
        <member name="M:Backend.Flights.Business.AvailabilityGTW.ProcessResult(System.String,System.String)">
            <summary>
            This is a callback function that is called from each thread in the GtwAereo().Flights() call. After
            a thread has completed its call to the backend server to query the flights list, this callback will be
            called to process the result
            The method has a mutually exclusive code region to avoid a race condition where multiple results arrive
            in succession and try to update the same instance of the object, which considering how the merge
            method was built could cause one or more results to be lost (if the race condition happened)
            </summary>
            <param name="json"></param>
        </member>
        <member name="M:Backend.Flights.Controllers.SearchController.Sync(Backend.Flights.Models.Search.SearchRequest)">
            <summary>
            Busca de vôos de forma bloqueante
            </summary>
            <remarks>
            Realiza busca de vôos e retorna o resultado de forma síncrona, ou seja, o controle de execução fica
            bloqueado até que as respostas de todos providers cadastrados no Gateway Aéreo retornem suas respostas.
            </remarks>
            <param name="request">
            Um objeto JSON detalhando os parâmetros da pesquisa. Neste objeto tem-se as informações de quantidade de
            adultos, crianças e bebês, se o vôo é direto ou não, se a passagem é para a classe econômica, e os trechos
            da viagem (incluindo o código IATA da cidade/aeroporto de origem e destino e data de embarque - cada trecho
            é um elemento num vetor de pares de cidades).
            Exemplo:
            {"Adults":"1","Children":"0","Infants":"0","DirectFlight":"false","Cabin":"eco","CityPairs":[{"DepartureDate":{"Day":"15","Month":"11","Year":"2019"},"OriginIata":"GRU","DestinationIata":"REC"}, {"DepartureDate":{"Day":"17","Month":"11","Year":"2019"},"OriginIata":"REC","DestinationIata":"GRU"}]}
            </param>
            <returns>
            A resposta é um objeto JSON com os grupos de preços, a matriz de preços e os filtros
            </returns>
        </member>
        <member name="M:Backend.Flights.Controllers.SearchController.AsyncStream(Backend.Flights.Models.Search.SearchRequest)">
            <summary>
            Busca de vôos via stream
            </summary>
            <remarks>
            Realiza busca de vôos e retorna o resultado em um stream de dados, ou seja, o controle de execução retorna
            as respostas de cada um dos providers cadastrados no Gateway Aéreo assim que cada resposta esteja disponível.
            </remarks>
            <param name="request">
            Um objeto JSON detalhando os parâmetros da pesquisa. Neste objeto tem-se as informações de quantidade de
            adultos, crianças e bebês, se o vôo é direto ou não, se a passagem é para a classe econômica, e os trechos
            da viagem (incluindo o código IATA da cidade/aeroporto de origem e destino e data de embarque - cada trecho
            é um elemento num vetor de pares de cidades).
            Exemplo:
            {"Adults":"1","Children":"0","Infants":"0","DirectFlight":"false","Cabin":"eco","CityPairs":[{"DepartureDate":{"Day":"15","Month":"11","Year":"2019"},"OriginIata":"GRU","DestinationIata":"REC"}, {"DepartureDate":{"Day":"17","Month":"11","Year":"2019"},"OriginIata":"REC","DestinationIata":"GRU"}]}
            </param>
            <returns>
            A resposta é um objeto JSON com os grupos de preços, a matriz de preços e os filtros
            </returns>
        </member>
    </members>
</doc>
