using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Backend.Flights.CA.Model.Context;
using Backend.Flights.CA.Model.LocImport;

namespace Backend.Flights.CA.Services.LocImportValidator
{
    public interface ICheckListAirService
    {
        Task<SafeResult<CheckListAirResponse, Tuple<int, string>>> Validate(string loc, string source, string packageGroup, int branch, string userToken, BrandContext brandContext);
    }
}