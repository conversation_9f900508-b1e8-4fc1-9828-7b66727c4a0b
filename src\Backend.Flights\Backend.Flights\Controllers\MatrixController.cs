﻿using Backend.Flights.Models.Matriz3Dias;
using Backend.Flights.Services;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Backend.Flights.Controllers
{
    [ApiController, ApiVersion("1.0"), Route("api/[controller]")]
    public class MatrixController : Controller
    {
        private readonly ILogger<MatrixController> _logger;
        private readonly IAirGtw _airGatewayService;

        public MatrixController(ILogger<MatrixController> logger, IAirGtw gtwService)
        {
            _logger = logger;
            _airGatewayService = gtwService;
        }


        [EnableCors("AllowAnyOrigin"), HttpPost]
        public async Task<object> MatrixAsyncSearch([FromBody] CalendarShopRequest request, [FromServices] IContextBrand contextBrand)
        {

            if (request.CityPairs.Count < 1 ||
                (
                    request.Infants < 1 &&
                    request.Children < 1 &&
                    request.Adults < 1
                ))
            {
                var resultError = Json(new { error = true, message = "Invalid arguments!" });
                resultError.StatusCode = 400;
                return resultError;
            }

            try
            {
                var result = await _airGatewayService.CalendarShop(request, contextBrand, 3);

                return Json(FormatResponse(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Fail on matrix search");
                var resultError = Json(new
                {
                    error = true,
                    message = "Fail on matrix search",
#if DEBUG
                    detaill = ex.ToString()
#endif
                });
                resultError.StatusCode = 500;
                return resultError;
            }
        }

        public static object FormatResponse(MatrixResponse resp)
        {
            var departureDates = resp.DepartureDate.Select(dd => dd.Date).Take(7);
            var returnDates = resp.DepartureDate[0].ReturningDate
                .Select(rd => rd.Date).Take(7).ToArray();

            return new
            {
                departureDate = departureDates,
                returnDate = returnDates,
                dataPriceNew = Enumerable.Range(0, returnDates.Length).Select(index => GetBestPrice(resp, index))
            };
        }

        public static IEnumerable<decimal> GetBestPrice(MatrixResponse resp, int index)
        {
            return resp.DepartureDate.Take(7).Select(dd =>
            {
                var prices = dd.ReturningDate[index].FareGroup
                    .Where(fg => fg != null)
                    .Select(fg => fg.PriceWithTax)
                    .ToArray();

                return prices.Length > 0 ? prices.Min() : 0;
            });
        }

    }
}