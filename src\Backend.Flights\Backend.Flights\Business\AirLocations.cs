﻿using Backend.Flights.Util;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Linq;
using System.Threading.Tasks;

namespace Backend.Flights.Business
{
    public class AirLocations
    {
        private readonly string locationUrl;
        public AirLocations(ConfigurationManager config) => locationUrl = config.AppSetting["Locations.Url"];

        public Models.Locations.LocationsResponse Get(string query, ILogger logger) => queryGtw(logger, locationUrl, query);
        public Models.Locations.Location LoadIata(string iata, ILogger logger)
        {
            var allLocations = queryGtw(logger, locationUrl, iata);

            if (allLocations.Locations.HasCount())
            {
                var airport = allLocations.Locations.FirstOrDefault(loc => loc.Iata.Equals(iata.ToUpper()) && loc.Type.Equals("AIRPORT"));
                if (airport != null)
                {
                    return airport;
                }
                // Returns a City instead -> Solves issue when searching for London
                return allLocations.Locations.FirstOrDefault(loc => loc.Iata.Equals(iata.ToUpper()));
            }

            return new Models.Locations.Location(
                iata: iata.ToUpper(),
                city: iata.ToUpper(),
                state: iata.ToUpper(),
                country: iata.ToUpper()
            );
        }

        private static readonly ConcurrentDictionary<string, Models.Locations.LocationsResponse> dictLocations = new ConcurrentDictionary<string, Models.Locations.LocationsResponse>();
        private static Models.Locations.LocationsResponse queryGtw(ILogger logger, string locationUrl, string iata)
            => dictLocations.GetOrAdd(iata, x =>
            {
                var task = Services.Locations.GetAsync(logger, locationUrl, x);
                Task.WaitAll(task);
                return parseToLocationResponse(task.Result);
            });

        private static Models.Locations.LocationsResponse parseToLocationResponse(Models.Locations.LocationsResponse allLocations)
        {
            allLocations.Locations.RemoveAll(loc => string.IsNullOrEmpty(loc.Iata.Trim()));

            //TODO: Corrigir na fonte (GTW_Locations)
            var saoAirport = allLocations.Locations.Where(loc => loc.Type == "AIRPORT" && loc.Iata == "SAO").FirstOrDefault();
            if (saoAirport != null)
                saoAirport.City = "São Paulo";
            return allLocations;
        }
    }
}
