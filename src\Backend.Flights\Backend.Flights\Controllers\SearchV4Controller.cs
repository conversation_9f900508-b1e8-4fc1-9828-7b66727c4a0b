using Backend.Flights.Business;
using Backend.Flights.CA.Model.Search;
using Backend.Flights.CA.Services.Json;
using Backend.Flights.CA.UseCase.AirLocation;
using Backend.Flights.CA.UseCase.BrandContext;
using Backend.Flights.CA.UseCase.RateToken;
using Backend.Flights.CA.UseCase.Search;
using Backend.Flights.CA.UseCase.Upsell;
using Backend.Flights.Services;
using Backend.Flights.Util;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using SearchResponse = Backend.Flights.CA.Model.Search.Response.SearchResponsePriceGroup;
using SearchResult = Backend.Flights.CA.Model.Search.Result.SearchResultFlight;
using AddAggrement = Backend.Flights.CA.Model.Search.Response.SearchResponsePriceGroup.AddAggrement;
using TaxRav = Backend.Flights.CA.Model.Search.Response.SearchResponsePriceGroup.TaxRav;
using TaxRavValues = Backend.Flights.CA.Model.Search.Response.SearchResponsePriceGroup.TaxRavValues;
using TaxRavDetail = Backend.Flights.CA.Model.Search.Response.SearchResponsePriceGroup.TaxRavDetail;

namespace Backend.Flights.Controllers
{
    [ApiVersion("4.0"), Route("api/search"), ApiController]
    public class SearchV4Controller : ControllerBase
    {
        private readonly IJsonService _json;
        private readonly SearchParallel _searchParallel;
        private readonly GetRateTokenAttributes _getRateTokenAttributes;
        private readonly SmallDescriptionByIata _smallDescriptionByIata;
        private readonly IGetCurrentBrandContext _getCurrentBrandContext;
        private readonly GetUpsellOrCheckout _getUpsellOrCheckout;

        public SearchV4Controller(
            IJsonService json,
            SearchParallel searchParallel,
            GetRateTokenAttributes getRateTokenAttributes,
            IGetCurrentBrandContext getCurrentBrandContext,
            GetUpsellOrCheckout getUpsellOrCheckout,
            SmallDescriptionByIata smallDescriptionByIata)
        {
            _json = json;
            _searchParallel = searchParallel;
            _getRateTokenAttributes = getRateTokenAttributes;
            _getCurrentBrandContext = getCurrentBrandContext;
            _getUpsellOrCheckout = getUpsellOrCheckout;
            _smallDescriptionByIata = smallDescriptionByIata;
        }

        [Route("[action]"), EnableCors("AllowAnyOrigin"), HttpPost]
        async public Task<ObjectResult> UpsellOrCheckoutData(UpsellAndCheckoutData.RequestUpsellOrCheckoutDTO dto)
        {

            var (error, upsell, cartItem) = await _getUpsellOrCheckout.Execute(
                dto.PriceGroupId,
                dto.CacheIds.Select(item => new[] { item.SegmentIndex, item.ScheduleIndex, item.SegmentHour }).ToArray(),
                dto.OfferUpsell);
            cartItem?.SetParamsInfo(dto);

            if (error != null)
            {
                switch (error.Value)
                {
                    case GetUpsellOrCheckout.Error.CacheNotFound:
                        return NotFound(new
                        {
                            error = true,
                        });
                    case GetUpsellOrCheckout.Error.BadSegment:
                        return BadRequest(new
                        {
                            error = true,
                        });
                }
            }

            if (upsell != null)
            {
                var upsellData = new
                {
                    upsellData = upsell
                };
                return Ok(upsellData);
            }

            var contract = new
            {
                checkoutData = new
                {
                    type = "flight",
                    data = cartItem
                }
            };
            return Ok(contract);
        }

        [Route("[action]"), EnableCors("AllowAnyOrigin"), HttpPost]
        public async Task AsyncStream(SearchRequest request)
        {
            StreamWriter sw = new StreamWriter(Response.Body, Encoding.UTF8);
            var endDate = request.CityPairs.LastOrDefault()?.DepartureDate.AsDate;
            var isDurationExceeded = !Validations.CheckMaxSearchDurationExceeded(endDate);
            var isInvalidRequest = IsInvalidFlightSearchRequest(request);

            if (isInvalidRequest)
            {
                Response.StatusCode = (int)HttpStatusCode.UnprocessableEntity;
                Response.ContentType = "application/json; charset=utf-8";

                await Response.StartAsync();

                await sw.WriteLineAsync(_json.Serialize(new
                {
                    error = true,
                    message = "fail on search params validation"
                }));

                await sw.FlushAsync();
                await Response.CompleteAsync();
                return;
            }

            if (isDurationExceeded)
            {
                Response.StatusCode = (int)HttpStatusCode.OK;
                Response.ContentType = "application/json; charset=utf-8";

                await Response.StartAsync();

                await sw.WriteLineAsync(_json.Serialize(new
                {
                    priceGroup = Array.Empty<int>(),
                    error = "Maximum flight search limit is " + Validations.MaxDaysAllowedForTrip + " days.",
                    completedWork = 100
                }));

                await sw.FlushAsync();
                await Response.CompleteAsync();
                return;
            }

            Response.StatusCode = 200;
            Response.ContentType = "application/json; charset=utf-8";

            await Response.StartAsync();

            List<SearchResponse> responsePool = new List<SearchResponse>(20);

            var currentBrandContext = await _getCurrentBrandContext.Execute();
            await foreach (var (completedWork, error, value) in _searchParallel.Execute(currentBrandContext, request))
            {
                if (value != null)
                {
                    var (id, response) = value.Value;
                    responsePool.Add(await Parse(id, response));

                    if (responsePool.Count >= 20)
                    {
                        await sw.WriteLineAsync(
                            _json.Serialize(new
                            {
                                priceGroups = responsePool,
                                completedWork = (int)Math.Round(completedWork, 0)
                            }));
                        await sw.FlushAsync();
                        responsePool.Clear();
                    }
                }
                else if (error != null)
                {
                    await sw.WriteLineAsync(
                            _json.Serialize(new
                            {
                                error,
                                completedWork = (int)Math.Round(completedWork, 0)
                            }));
                    await sw.FlushAsync();
                }
            }

            await sw.WriteLineAsync(
                _json.Serialize(new
                {
                    priceGroups = responsePool,
                    completedWork = 100
                }));
            await sw.WriteLineAsync();
            await sw.FlushAsync();
            await Response.CompleteAsync();
        }

        private static bool IsInvalidFlightSearchRequest(SearchRequest request)
        {
            return request.CityPairs.Length < 1 ||
                     request.CityPairs.Any(c => string.IsNullOrWhiteSpace(c.OriginIata) ||
                     string.IsNullOrWhiteSpace(c.DestinationIata) ||
                     c.DepartureDate.AsDate < DateTime.Today);
        }

        private static string ConvertStringPTC(SearchResult flight)
        {
            if (flight?.FareGroup?.Fares == null || flight.FareGroup.Fares.Count() == 0)
            {
                return string.Empty;
            }

            var ptcList = flight.FareGroup.Fares
                .Where(f => !string.IsNullOrWhiteSpace(f.PassengersType))
                .Select(f => $"{f.PassengersType} - {f.PriceWithTax}");

            return string.Join(" | ", ptcList);
        }

        private static List<TaxRavValues> GetValuesRAV(SearchResult.ResultTax tax)
        {
            List<TaxRavValues> taxValues = new List<TaxRavValues>();
            if (tax?.Values != null)
            {
                foreach (SearchResult.ResultTaxValues value in tax.Values)
                {
                    TaxRavValues taxValue = new TaxRavValues
                    {
                        Amount = value?.Amount ?? 0,
                        Currency = value?.Currency
                    };
                    taxValues.Add(taxValue);
                }
            }
            return taxValues;
        }

        private static List<TaxRavDetail> GetTaxDetailRAV(SearchResult.ResultTax tax)
        {
            List<TaxRavDetail> taxDetails = new List<TaxRavDetail>();
            if (tax?.Details != null)
            {
                foreach (SearchResult.ResultTaxDetails detail in tax.Details)
                {
                    TaxRavDetail taxDetail = new TaxRavDetail
                    {
                        Code = detail?.Code,
                        Amount = detail?.Amount ?? 0,
                        Description = detail?.Description
                    };
                    taxDetails.Add(taxDetail);
                }
            }
            return taxDetails;
        }


        private static TaxRav ConvertTaxInTaxRAV(SearchResult.ResultTax tax)
        {
            TaxRav taxRav = new TaxRav
            {
                Code = tax?.Code,
                Description = tax?.Description,
                Values = GetValuesRAV(tax),
                Details = GetTaxDetailRAV(tax)
            };
            return taxRav;
        }

        private static TaxRav GetTaxRAV(SearchResult flight)
        {
            var taxRavs = flight.FareGroup?.Fares
                ?.SelectMany(fare => fare?.Taxes ?? Enumerable.Empty<SearchResult.ResultTax>())
                ?.Where(tax => tax?.Code == "RAV")
                ?.Select(ConvertTaxInTaxRAV)
                ?.ToList();

            return taxRavs?.FirstOrDefault();
        }

        private async Task<SearchResponse> Parse(string uid, SearchResult flight)
        {
            SearchResult.ResultSegment firstSegment = flight.Segments.First();
            SearchResult.ResultLeg firstLeg = firstSegment.Legs.First();

            Dictionary<string, string> infos = _getRateTokenAttributes.Execute(firstSegment.RateToken, "mkp", "mki", "cmi", "rtc", "oii", "pot");

            decimal markup = infos.TryGetValue("mkp", out string mkp) ? decimal.Parse(mkp) : 0;
            string pricingId = infos.TryGetValue("mki", out string mki) ? mki : string.Empty;
            string provider = infos.TryGetValue("cmi", out string cmi) ? cmi : string.Empty;
            string accountCode = infos.TryGetValue("rtc", out string rtc) ? rtc : string.Empty;
            string officeId = infos.TryGetValue("oii", out string oii) ? oii : string.Empty;
            decimal netValue = infos.TryGetValue("pot", out string pot) ? decimal.Parse(pot) : 0;

            var (tax, fee) = flight.GetTaxAndFee();

            SearchResult.ResultFare adultFare = flight.FareGroup.Fares.First(f => f.PassengersType == "ADT");
            SearchResult.ResultFare childFare = flight.FareGroup.Fares.FirstOrDefault(f => f.PassengersType == "CHD");
            SearchResult.ResultFare infantFare = flight.FareGroup.Fares.FirstOrDefault(f => f.PassengersType == "INF");

            var adultTaxAndFee = adultFare.GetTaxAndFee();
            var childTaxAndFee = childFare?.GetTaxAndFee() ?? (0, 0);
            var infantTaxAndFee = infantFare?.GetTaxAndFee() ?? (0, 0);

            bool isRefundable = flight.Segments.All(s => s.FareProfile.Services.FirstOrDefault(v => v.Type != null && v.Type == "REFUNDABLE")?.IsIncluded ?? false);

            SearchResult.ResultFareProfile fareProfile = flight.Segments.First().FareProfile;

            SearchResponse.ResponseSegment[] segments = await Task.WhenAll(flight.Segments.Select(async s => await parseResponseSegment(s, flight, fareProfile, provider, isRefundable)));

            AddAggrement addAggrement = new AddAggrement
            {
                AccountCode = accountCode,
                PTC = ConvertStringPTC(flight)
            };

            TaxRav taxRav = GetTaxRAV(flight);


            SearchResponse.ResponseDebugInformation debugInfo = new SearchResponse.ResponseDebugInformation(
                markup,
                firstLeg.FareBasis,
                firstSegment.FareType,
                pricingId,
                firstSegment.PackageGroup,
                addAggrement,
                officeId,
                netValue,
                taxRav
            );

            SearchResponse.ResponsePriceDetailByPassengerType priceDetail = new SearchResponse.ResponsePriceDetailByPassengerType(
                new SearchResponse.ResponsePriceDetail(adultFare.PassengersCount, adultTaxAndFee.fee, adultTaxAndFee.tax),
                new SearchResponse.ResponsePriceDetail(childFare?.PassengersCount ?? 0, childTaxAndFee.fee, childTaxAndFee.tax),
                new SearchResponse.ResponsePriceDetail(infantFare?.PassengersCount ?? 0, infantTaxAndFee.fee, infantTaxAndFee.tax)
            );

            return new SearchResponse(
                uid,
                segments,
                debugInfo,
                flight.FareGroup.PriceWithTax,
                1,
                tax * adultFare.PassengersCount,
                fee * adultFare.PassengersCount,
                adultFare.PriceWithTax,
                adultFare.PriceWithoutTax,
                childFare?.PriceWithoutTax ?? 0,
                infantFare?.PriceWithoutTax ?? 0,
                priceDetail,
                string.Empty
            );
        }

        private async Task<SearchResponse.ResponseSegment> parseResponseSegment(SearchResult.ResultSegment segment, SearchResult flight, SearchResult.ResultFareProfile fareProfile, string provider, bool isRefundable)
        {
            SearchResponse.ResponseFlight[] responseFlights = await Task.WhenAll(segment.Legs.Select(async l => await parseResponseFlight(l, fareProfile)));
            SearchResponse.ResponseLeg[] legs = segment.Legs.Select(l => new SearchResponse.ResponseLeg(l.FareClass)).ToArray();

            return new SearchResponse.ResponseSegment(
                responseFlights,
                segment.RouteRPH,
                segment.Departure,
                segment.Arrival,
                flight.ValidatingBy.Iata,
                flight.ValidatingBy.Name.Replace("Airlines", string.Empty).Trim().ToTitleCase(),
                segment.Duration,
                provider,
                segment.NumberOfStops,
                isRefundable,
                segment.FareType,
                legs,
                segment.OperationalId
            );
        }

        private async Task<SearchResponse.ResponseFlight> parseResponseFlight(
            SearchResult.ResultLeg leg,
            SearchResult.ResultFareProfile fareProfile
        )
        {
            string descriptionArrival = await _smallDescriptionByIata.Execute(leg.Arrival);
            string descriptionDeparture = await _smallDescriptionByIata.Execute(leg.Departure);

            SearchResponse.ResponseBaggageInfo baggageInfo = new SearchResponse.ResponseBaggageInfo(
                fareProfile.Services?.Any(s => s.Type == "HAND_LUGGAGE" && !s.IsIncluded) == true ? 0 : 1,
                decimal.ToInt32(fareProfile.Baggage?.Quantity ?? 0),
                HandBaggageWeightService.parse(fareProfile?.Services?.FirstOrDefault(s => s.Type == "HAND_LUGGAGE")?.Description),
                decimal.ToInt32(fareProfile?.Baggage?.Weight ?? 0)
            );
            SearchResponse.ResponseStop[] responseStops = leg.Stops.Select(p => new SearchResponse.ResponseStop(p.Airport, p.DepartureDate, p.ArrivalDate)).ToArray();

            return new SearchResponse.ResponseFlight(
                leg.Arrival,
                descriptionArrival,
                leg.Departure,
                descriptionDeparture,
                leg.ArrivalDate,
                leg.DepartureDate,
                leg.OperatedBy.Iata,
                leg.OperatedBy.Name.ToTitleCase(),
                leg.SeatClass?.Description ?? string.Empty,
                baggageInfo,
                responseStops,
                leg.NumberOfStops,
                leg.FlightNumber,
                leg.Duration,
                leg.SeatsLeft
            );
        }
    }
}