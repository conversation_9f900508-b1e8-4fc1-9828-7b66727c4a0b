﻿using Backend.Flights.Business.Parsers;
using Backend.Flights.CA.Services.Http;
using Backend.Flights.CA.Services.Json;
using Backend.Flights.CA.UseCase.Promotions;
using Backend.Flights.Models;
using Backend.Flights.Models.GtwAereo;
using Backend.Flights.Models.Promotions;
using Backend.Flights.Models.Search;
using Backend.Flights.Services;
using Backend.Flights.Util;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Backend.Flights.CA.Model.Opportunities;
using Backend.Flights.CA.UseCase.RateToken;

namespace Backend.Flights.Business
{
    public class UpsellAndCheckoutData
    {
        /// <summary>
        /// Inclui os rateTokens, fareProfiles e os possíveis upgrades no cache. A chave para obter os valores será:
        /// prefix: "RTKFP", key: [Id do priceGroup]
        /// </summary>
        public static void CacheFlights(ILogger logger, ICacheService cache, List<Models.GtwAereo.Flight> flights) => new CreateCacheData(logger, cache, flights).Execute();
        public class RateTokenFareGroupUpgCache
        {
            /// <summary>
            /// Dados referentes a informações de bagagem e serviços do priceGroup atual.
            /// </summary>
            [JsonProperty("bg")]
            public Dictionary<string, string> Baggages { get; set; }

            [JsonProperty("bgn")]
            public Backend.Flights.Models.GtwAereo.BaggageByPTC BaggagesNew { get; set; } // Novo objeto para bagagens

            /// <summary>
            /// Dados referentes a informações de bagagem e serviços do priceGroup atual.
            /// </summary>
            [JsonProperty("FP")]
            public FareProfile FareProfile { get; set; }

            /// <summary>
            /// Ids dos priceGroups que são upgrades do atual.
            /// </summary>
            [JsonProperty("Upg")]
            public List<string> PossibleUpgrades { get; set; }

            /// <summary>
            /// RateTokens de todos os trechos e pernas possíveis, sendo que a chave do dicionário é formado pelo [Índice do trecho (segmentIndex)]-[Índice do vôo dentro do trecho (índice no array flights dentro do segment)]
            /// </summary>
            [JsonProperty("Tkn")]
            public Dictionary<string, string> Tokens { get; set; }
        }

        public class CacheTokens
        {
            public int Idx { get; set; }
            public string RateToken { get; set; }
            public string SegmentHour { get; set; }
            public BaggageChange Baggage { get; set; }
        }

        public class FlightChange
        {
            public PriceChange Price { get; set; }
            public RefundableChange Refundable { get; set; }
            public List<BaggageChange> Baggages { get; set; }
            public Dictionary<string, List<BaggageNewChange>> BaggagesNew { get; set; }
        }

        public class PriceChange
        {
            public bool IsChange { get; set; }
            public int CurrentPriceWithoutTax { get; set; }
            public int NewPriceWithoutTax { get; set; }
            public int CurrentPriceWithTax { get; set; }
            public int NewPriceWithTax { get; set; }
            public int PriceDifference { get; set; }
            public string PriceChangeType { get; set; }
        }

        public enum PriceChangeType
        {
            EXPENSIVE,
            LOWEST,
            EQUAL
        }

        public class RefundableChange
        {
            public bool currentRefundable { get; set; }
            public bool newRefundable { get; set; }
        }

        public class BaggageChange
        {
            public BaggageChangeType HandLuggage { get; set; }
            public BaggageChangeType Checked { get; set; }
        }

        public class BaggageChangeType
        {
            public int CurrentWeight { get; set; }
            public int CurrentQuantity { get; set; }
            public int NewWeight { get; set; }
            public int NewQuantity { get; set; }
        }

        public class BaggageNewChange
        {
            public string Type { get; set; }
            public BaggageDetails Current { get; set; }
            public BaggageDetails New { get; set; }
        }

        public class BaggageDetails
        {
            public int Count { get; set; }
            public double? Weight { get; set; }
            public BaggageDimensionsChange Dimensions { get; set; }
            public double? LinearSize { get; set; }
        }

        public class BaggageDimensionsChange
        {
            public double? Length { get; set; }
            public double? Width { get; set; }
            public double? Depth { get; set; }
        }

        internal class CreateCacheData
        {
            internal const int expirySeconds = 20 * 60; //Os dados estarão disponíveis por 20 minutos.

            private readonly ILogger logger;
            private readonly ICacheService cache;
            private readonly List<Models.GtwAereo.Flight> flights;
            public CreateCacheData(ILogger logger, ICacheService cache, List<Models.GtwAereo.Flight> flights)
            {
                this.logger = logger;
                this.cache = cache;
                this.flights = flights;
            }

            // Salva o Cache do AsyncStream
            public void Execute()
            {
                //Temporariamente, logaremos todos os serviços de uma cia.
                // [log-disabled] temp_logCiaServices();

                //Temporariamente
                // [log-disabled] temp_logZeroDurationFlights();

                var data = buildCacheData();
                var task = cache.BatchAdd(data, expirySeconds, logger);
                Task.WaitAll(task);

                //#if LOCAL
                //Este método deve ser utilizado apenas para a geração de mocks, no caso de mudanças no BFF que demandem alteração do mock do teste de regressão no front-end.
                //File.AppendAllLines(@"d:\fareProf-rateToken-Mock.txt", cacheDataMockFileGenerator(data));
                //#endif
            }

            private CacheTokens mountKeyCache(Segment segment, int idx)
            {
                var depDate = segment.DepartureDate.Hour == 0 && segment.DepartureDate.Minute == 0 ? "" : int.Parse(segment.DepartureDate.ToString("HHmm")).ToString();
                var arrDate = segment.ArrivalDate.Hour == 0 && segment.ArrivalDate.Minute == 0 ? "" : int.Parse(segment.ArrivalDate.ToString("HHmm")).ToString();
                var baggage = mapBaggage(segment.FareProfile);
                return new CacheTokens
                {
                    Idx = idx,
                    RateToken = segment.RateToken,
                    SegmentHour = $"{depDate}{arrDate}",
                    Baggage = baggage
                };
            }

            private UpsellAndCheckoutData.BaggageChange mapBaggage(FareProfile fareProfile)
            {
                UpsellAndCheckoutData.BaggageChange baggage = new UpsellAndCheckoutData.BaggageChange();
                baggage.HandLuggage = new BaggageChangeType();
                baggage.Checked = new BaggageChangeType();

                var hasHandLuggageAndIsIncluded = fareProfile.Services != null && fareProfile.Services.Any(s => s.Type == "HAND_LUGGAGE" && s.IsIncluded);

                baggage.HandLuggage.CurrentQuantity = !hasHandLuggageAndIsIncluded ? 0 : 1;
                baggage.HandLuggage.CurrentWeight = !hasHandLuggageAndIsIncluded ? 0 : Services.HandBaggageWeightService.parse(fareProfile.Services.FirstOrDefault(s => s.Type == "HAND_LUGGAGE")?.Description);

                baggage.Checked.CurrentQuantity = decimal.ToInt32(fareProfile.Baggage?.Quantity ?? 0);
                baggage.Checked.CurrentWeight = decimal.ToInt32(fareProfile.Baggage?.Weight ?? 0);

                return baggage;
            }

            private FareProfile AdjustRefundable(Segment segment, bool allSegmentsRefundable)
            {
                if (segment != null && segment.FareProfile != null && segment.FareProfile.Services != null)
                {
                    // Encontrar o serviço "REFUNDABLE" e ajustar o valor de IsIncluded com base em allSegmentsRefundable
                    var refundableService = segment.FareProfile.Services.FirstOrDefault(service => service.Type != null && service.Type.Equals("REFUNDABLE"));
                    if (refundableService != null)
                        refundableService.IsIncluded = allSegmentsRefundable;
                }
                return segment?.FareProfile;
            }

            // Monta o Cache que vai ser Salvo do AsyncStream
            private Dictionary<string, string> buildCacheData()
            {
                var fareProfs = (from x in flights
                                 where x.Segments != null && x.Segments.Count > 0
                                 let allSegmentsRefundable = x.Segments.All(s => s?.FareProfile?.Services?.Find(service => service.Type != null && service.Type.Equals("REFUNDABLE"))?.IsIncluded ?? false)
                                 let firstSegment = x.Segments.FirstOrDefault()
                                 let fareProfile = firstSegment != null ? AdjustRefundable(firstSegment, allSegmentsRefundable) : null
                                 select new { x.UID, FareProfile = fareProfile }).ToDictionary(x => x.UID, x => x.FareProfile);

                var baggages = (from x in flights
                                from sg in (from s in x.Segments
                                            group s by s.RouteRPH into gs
                                            select new
                                            {
                                                SegmentGroup = gs.Key,
                                                Tokens = gs.Select(mountKeyCache)
                                            })
                                from token in sg.Tokens
                                group new { sg.SegmentGroup, token.Idx, token.RateToken, token.SegmentHour, token.Baggage } by x.UID into g
                                select new { g.Key, Rts = g.ToDictionary(x => $"{x.SegmentGroup}-{x.Idx}-{x.SegmentHour}", x => JsonConvert.SerializeObject(x.Baggage)) }
                              ).ToDictionary(x => x.Key, x => x.Rts);

                var baggageNew = (from flight in flights
                                  let baggageByPTC = flight.FareGroup?.Baggages
                                  where baggageByPTC != null && baggageByPTC.HasBaggage()
                                  select new
                                  {
                                      flight.UID,
                                      Baggages = JsonConvert.SerializeObject(baggageByPTC)
                                  }).ToDictionary(x => x.UID, x => x.Baggages);

                var tokens = (from x in flights
                              from sg in (from s in x.Segments
                                          group s by s.RouteRPH into gs
                                          select new
                                          {
                                              SegmentGroup = gs.Key,
                                              Tokens = gs.Select(mountKeyCache)
                                          })
                              from token in sg.Tokens
                              group new { sg.SegmentGroup, token.Idx, token.RateToken, token.SegmentHour } by x.UID into g
                              select new { g.Key, Rts = g.ToDictionary(x => $"{x.SegmentGroup}-{x.Idx}-{x.SegmentHour}", x => x.RateToken) }
                              ).ToDictionary(x => x.Key, x => x.Rts);

                var upsellData = new FlightUpsell(flights, fareProfs);
                var result = (from x in fareProfs
                              join t in tokens on x.Key equals t.Key
                              join b in baggages on x.Key equals b.Key
                              join u in upsellData on x.Key equals u.Key
                              join bn in baggageNew on x.Key equals bn.Key into baggageNewGroup
                              from bn in baggageNewGroup.DefaultIfEmpty()
                              select new
                              {
                                  UID = x.Key,
                                  Data = new RateTokenFareGroupUpgCache
                                  {
                                      FareProfile = x.Value,
                                      PossibleUpgrades = u.Value,
                                      Tokens = t.Value,
                                      Baggages = b.Value,
                                      BaggagesNew = string.IsNullOrWhiteSpace(bn.Value) ? new Models.GtwAereo.BaggageByPTC() : JsonConvert.DeserializeObject<Models.GtwAereo.BaggageByPTC>(bn.Value)
                                  }
                              }
                        ).ToDictionary(x => x.UID, x => JsonConvert.SerializeObject(x.Data));

                return result;
            }


            /// <summary>
            ///        Criado para proposito de logar os voos que estão com legs duration 0 no gtw aereo
            ///        ideia é ter numeros para provar que isso está nos impactando
            ///        deve ser removido apos a correção do gtwaereo
            /// </summary>
            private void temp_logZeroDurationFlights()
            {
                if (flights[0].Segments[0].Provider == "AMD")
                {
                    var legs = flights.SelectMany(x => x.Segments).SelectMany(x => x.Legs);

                    using (logger.BeginScope(new Dictionary<string, object>
                    {
                        ["operation"] = "LOGZERODURATION",
                        ["provider"] = flights[0].Segments[0].Provider,
                        ["flights_count"] = legs.Count(),
                        ["flights_zero_duration_count"] = legs.Count(y => y.Duration == 0),
                        ["iatas"] = string.Concat(flights[0].Segments[0].Departure, " - ", flights[0].Segments[0].Arrival),
                        ["dates"] = string.Concat(flights[0].Segments[0].DepartureDate.ToShortDateString(), " - ", flights[0].Segments[flights[0].Segments.Count - 1].DepartureDate.ToShortDateString()),
                        ["air_company"] = legs.FirstOrDefault().ManagedBy.Iata

                    }))
                        logger.LogInformation("ZeroDuration"); // Metodo comentado
                }
            }
            private void temp_logCiaServices()
            {
                var servs = (from f in flights
                             group f by f.ValidatingBy.Name into c
                             select new
                             {
                                 Cia = c.Key,
                                 Flights = (from f in c
                                            from x in f.Segments[0].FareProfile?.Services ?? new List<FareProfileService>()
                                            group x by x into g
                                            select new { g.Key.Type, g.Key.Description, Total = g.Count() }).Distinct().ToList()
                             }).ToList();

                foreach (var cia in servs)
                    using (logger.BeginScope(new Dictionary<string, object>
                    {
                        ["operation"] = "CIASERVICETYPES",
                        ["serviceTypes"] = JsonConvert.SerializeObject(cia.Flights),
                        ["provider"] = flights[0].Segments[0].Provider,
                        ["cia"] = cia.Cia,
                    }))
                        logger.LogInformation("Service"); // Metodo comentado
            }

            /// <summary>
            /// Este método deve ser utilizado apenas para a geração de mocks, no caso de mudanças no BFF que demandem alteração do mock do teste de regressão no front-end.
            /// É preciso lembrar de excluir o arquivo antes de rodar o método para evitar dados desnecessários no controle de versão.
            /// </summary>
            private static IEnumerable<string> cacheDataMockFileGenerator(Dictionary<string, string> data)
            {
                var tokenCounter = 0;
                var contractResolver = new DefaultContractResolver { NamingStrategy = new CamelCaseNamingStrategy() };
                foreach (var pg in data)
                {
                    var obj = JsonConvert.DeserializeObject<RateTokenFareGroupUpgCache>(pg.Value);
                    var mockModelObj = new
                    {
                        UID = pg.Key,
                        Upg = obj.PossibleUpgrades,
                        Tkn = obj.Tokens.ToDictionary(x => x.Key, x => $"Token {tokenCounter++}"),
                        //Como o serializer padrão do .net core 2.2 usa CamelCase, a resposta da api TokensAndUpsellData é em camel case então é preciso gerar o mock com esse mesmo formato.
                        FP = JsonConvert.SerializeObject(obj.FareProfile, new JsonSerializerSettings { ContractResolver = contractResolver }),
                    };

                    yield return JsonConvert.SerializeObject(mockModelObj) + ",";
                }
            }
        }


        public static Task<object> RecoverFlight(ConfigurationManager config, ILogger logger, ICacheService cache,
            BrandContext brandCtx, RequestUpsellOrCheckoutDTO request, IContextBrands contextBrands,
            IRequestPromotions promotion, CA.Model.Context.BrandContext caBrandContext, CA.UseCase.RateToken.GetRateTokenPriceInfo getRateTokenPriceInfo, IHttpClient httpClient, IJsonService json)
            => new RecoverCacheData(config, logger, cache, brandCtx, request, contextBrands, promotion, caBrandContext, getRateTokenPriceInfo, httpClient, json).Execute();
        public class RequestUpsellOrCheckoutDTO
        {
            /// <summary>
            /// Caso esteja true, oferece upgrades, caso existam. Se estiver false, ignora possíveis upgrades e já gera o payload para checkout.
            /// </summary>
            public bool OfferUpsell { get; set; }
            public string Provider { get; set; }
            public string PriceGroupId { get; set; }
            public bool CanDirectToNewCheckout { get; set; }
            public SegmentAndFlightIdx[] CacheIds { get; set; }
            public string SearchId { get; set; }
            public string ClientId { get; set; }
            public FlightExtraData FlightId { get; set; }
            public bool CanDirectToPreCheckout { get; set; }
            public string[] OperationalId { get; set; }
            public string[] Flights { get; set; }

            public struct FlightExtraData
            {
                public string[] OperationalId { get; set; }
                public string[] Flights { get; set; }
            }

            public struct SegmentAndFlightIdx
            {
                public int SegmentIndex { get; set; }
                public int ScheduleIndex { get; set; }
                public int SegmentHour { get; set; }
            }

            public List<PromotionItem> SelectedItems { get; set; }
            public string GtwUserToken { get; set; }
        }
        private class RecoverCacheData
        {
            private BrandContext brandCtx;
            private readonly CA.Model.Context.BrandContext _caBrandCtx;
            private readonly ConfigurationManager config;
            private readonly ILogger logger;
            private readonly ICacheService cache;
            private readonly RequestUpsellOrCheckoutDTO request;
            private readonly IContextBrands _contextBrands;
            private readonly IRequestPromotions _promotion;
            private readonly CA.UseCase.RateToken.GetRateTokenPriceInfo _getRateTokenPriceInfo;
            private readonly IHttpClient _httpClient;
            private readonly IJsonService _json;


            public RecoverCacheData(ConfigurationManager config, ILogger logger, ICacheService cache,
                BrandContext brandCtx, RequestUpsellOrCheckoutDTO request, IContextBrands contextBrands,
                IRequestPromotions promotion, CA.Model.Context.BrandContext caBrandCtx, CA.UseCase.RateToken.GetRateTokenPriceInfo getRateTokenPriceInfo,
                IHttpClient httpClient, IJsonService json)
            {
                this.config = config;
                this.logger = logger;
                this.cache = cache;
                this.brandCtx = brandCtx;
                this.request = request;
                _contextBrands = contextBrands;
                _caBrandCtx = caBrandCtx;
                _promotion = promotion;
                _getRateTokenPriceInfo = getRateTokenPriceInfo;
                _httpClient = httpClient;
                _json = json;
            }

            private class priceGroupData
            {
                public string uid { get; set; }
                public FareProfile fare { get; set; }
                public string[] tokens { get; set; }
                public string[] baggages { get; set; }
                public Models.GtwAereo.BaggageByPTC BaggagesNew { get; set; }
            }

            private void ValidateBrandContext(string token)
            {
                if (string.IsNullOrWhiteSpace(token)) return;

                var rt = SearchParser.ParseRateToken(token);
                if (rt.Attribute("ags")?.Value == "LOJ")
                {
                    brandCtx = _contextBrands.GetContext(IContextBrand.LOJA_CTX).Clone();
                    brandCtx.GatewayHeaders.BranchId = rt.Attribute("bri")?.Value;
                }
            }

            async public Task<object> Execute()
            {
                var cachedPriceGroups = await getCachedPriceGroups();
                if (cachedPriceGroups == null)
                    throw new Exception("Cached price group not found.");

                ValidateBrandContext(cachedPriceGroups.Count > 0
                    ? cachedPriceGroups.First().cached?.Tokens.FirstOrDefault().Value
                    : null);

                var tokenKeys = request.CacheIds.Select(x => x.SegmentHour != 0 ? $"{x.SegmentIndex}-{x.SegmentHour}" : $"{x.SegmentIndex}-{x.ScheduleIndex}").ToList();

                var priceGroups = (from pg in cachedPriceGroups
                                   select new priceGroupData
                                   {
                                       uid = pg.pgUid,
                                       fare = pg.cached.FareProfile,
                                       tokens = getPriceGroupItems(pg.cached.Tokens, tokenKeys),
                                       baggages = getPriceGroupItems(pg.cached.Baggages, tokenKeys),
                                       BaggagesNew = pg.cached.BaggagesNew
                                   }).ToArray();

                var obj = new CheckoutData(
                    new CheckoutData.RequestData
                    {
                        Provider = request.Provider,
                        CanDirectToNewCheckout = request.CanDirectToNewCheckout,
                        ClientId = request.ClientId,
                        SearchId = request.SearchId,
                        CanDirectToPreCheckout = request.CanDirectToPreCheckout,
                        OperationalId = request.OperationalId,
                        Flights = request.Flights
                    }, config, logger, brandCtx, _promotion, request.SelectedItems, request.GtwUserToken, _caBrandCtx, _getRateTokenPriceInfo, _httpClient, _json);


                var rateToken = priceGroups.First().tokens.FirstOrDefault();
                var priceWithoutTax = new GetRateTokenAttributes().Execute(rateToken, "sot").First().Value;
                var priceWithTax = new GetRateTokenAttributes().Execute(rateToken, "swt").First().Value;

                if (config.AppSetting["UpSellBooking"] == "false" && request.OfferUpsell && priceGroups.Length > 1)
                    return buildUpsellResponse(priceGroups);

                // Busca disponibilidade de upgrades de forma assíncrona
                // Inicio da execução assíncrona
                var firstResponse = (await obj.CheckAvailability(priceGroups.First().tokens)).First();
                FlightChange flightChange = new FlightChange();
                if (firstResponse.response.Error == null && firstResponse.response.PriceGroup != null)
                {
                    flightChange.Price = await VerifyPriceChange(priceWithTax, priceWithoutTax, firstResponse.response, _caBrandCtx, request);
                    flightChange.Refundable = VerifyRefundable(priceGroups.First().fare.Services, firstResponse.response.PriceGroup.Segments);
                    flightChange.Baggages = VerifyBaggages(priceGroups.First().baggages, firstResponse.response.PriceGroup.Segments, logger);
                    flightChange.BaggagesNew = VerifyBaggagesNew(priceGroups.First().BaggagesNew, firstResponse.response.PriceGroup, logger);
                }

                var extraParamsToCart = new
                {
                    flights = request.FlightId.Flights,
                    operationalIds = request.FlightId.OperationalId
                };
                if (firstResponse.response.Error != null || priceGroups.Length == 1)
                    return await obj.BuildCheckoutResponse(request.SearchId, request.ClientId, firstResponse.ms, firstResponse.response, extraParamsToCart, flightChange);

                var availableUpgrades = await obj.CheckAvailability(priceGroups.Skip(1).Select(x => x.tokens).ToArray());

                if (availableUpgrades.All(x => x.response.Error != null))
                    return await obj.BuildCheckoutResponse(request.SearchId, request.ClientId, firstResponse.ms, firstResponse.response, extraParamsToCart, flightChange);

                return buildUpsellResponse(priceGroups, new[] { firstResponse }.Concat(availableUpgrades).ToArray());
            }

            private string[] getPriceGroupItems(Dictionary<string, string> items, IEnumerable<string> tokenKeys)
            {
                return tokenKeys.Select(x =>
                {
                    var itemFound = items.Where(s =>
                    {
                        string schedule = x.Length > 3 ? s.Key.Substring(4) : s.Key.Substring(2, 1);
                        var newSearchKey = $"{s.Key.Substring(0, 1)}-{schedule}".Replace("--", "-");
                        return x.Equals(newSearchKey);
                    }).ToList();

                    return itemFound.Count > 0 ? items[itemFound.Select(s => s.Key).ToArray().First()] : null;
                }).ToArray();
            }

            private List<UpsellAndCheckoutData.BaggageChange> VerifyBaggages(string[] currentBaggages, List<Segment> segments, ILogger logger)
            {
                List<UpsellAndCheckoutData.BaggageChange> baggages = new List<UpsellAndCheckoutData.BaggageChange>();

                for (int i = 0; i < segments.Count; i++)
                {
                    FareProfile fareProfile = segments[i]?.FareProfile;

                    UpsellAndCheckoutData.BaggageChange currentUpdated = JsonConvert.DeserializeObject<UpsellAndCheckoutData.BaggageChange>(currentBaggages[i]);

                    var handLuggageUpSell = fareProfile?.Services?.FirstOrDefault(s => s.Type == "HAND_LUGGAGE");
                    var checkedBaggageUpSell = fareProfile?.Baggage;

                    // Bagagem de mão
                    currentUpdated.HandLuggage.NewQuantity = (handLuggageUpSell?.IsIncluded ?? false) ? 1 : 0;

                    var parsedWeight = Services.HandBaggageWeightService.parse(handLuggageUpSell?.Description);

                    // Validação para quando o peso da bagagem de mão do UpSell for 0, vai utilizar o peso corrente do avail.
                    var handLuggageNewWeight = !(handLuggageUpSell?.IsIncluded ?? false)
                        ? 0 // Se não houver bagagem de mão ou não estiver incluída, o peso será 0
                        : parsedWeight == 0
                            ? currentUpdated.HandLuggage.CurrentWeight // Se o peso analisado for 0, use o peso atual
                            : parsedWeight;

                    currentUpdated.HandLuggage.NewWeight = handLuggageNewWeight;

                    // Metodo para setar o peso da bagagem de mão que veio do Avail, pois existem casos onde vem a bagagem, porém não vem o peso. (nesse caso pega do UpSell)
                    var handLuggage = currentUpdated.HandLuggage;
                    if (handLuggage.CurrentQuantity > 0 && handLuggage.CurrentWeight == 0 &&
                        handLuggage.NewQuantity == handLuggage.CurrentQuantity && handLuggage.NewWeight > 0 &&
                        handLuggage.CurrentWeight != handLuggage.NewWeight
                    )
                    {
                        currentUpdated.HandLuggage.CurrentWeight = handLuggageNewWeight;
                        VerifyBaggagesLogInformation("Bagagem de mão com peso no upsell e sem peso no avail", segments, logger, currentBaggages[i], handLuggageUpSell, checkedBaggageUpSell, i);
                    }

                    // Bagagem despachada
                    var checkedBaggageNewQuantity = decimal.ToInt32(checkedBaggageUpSell?.Quantity ?? 0);
                    var checkedBaggageNewWeight = decimal.ToInt32(checkedBaggageUpSell?.Weight ?? 0);

                    // Mesma validação da bagagem de mão, eu posso ter peso no avail e não ter no upsell e vice versa.
                    if (checkedBaggageNewQuantity > 0 && checkedBaggageNewWeight == 0)
                    {
                        checkedBaggageNewWeight = (currentUpdated.Checked.CurrentQuantity == checkedBaggageNewQuantity) ? currentUpdated.Checked.CurrentWeight : 0;
                        VerifyBaggagesLogInformation("Bagagem despachada com peso no avail e sem peso no upsell", segments, logger, currentBaggages[i], handLuggageUpSell, checkedBaggageUpSell, i);
                    }
                    else if (currentUpdated.Checked.CurrentQuantity > 0 && currentUpdated.Checked.CurrentWeight == 0)
                    {
                        currentUpdated.Checked.CurrentWeight = (currentUpdated.Checked.CurrentQuantity == checkedBaggageNewQuantity) ? checkedBaggageNewWeight : 0;
                        VerifyBaggagesLogInformation("Bagagem despachada com peso no upsell e sem peso no avail", segments, logger, currentBaggages[i], handLuggageUpSell, checkedBaggageUpSell, i);
                    }

                    currentUpdated.Checked.NewQuantity = checkedBaggageNewQuantity;
                    currentUpdated.Checked.NewWeight = checkedBaggageNewWeight;

                    baggages.Add(currentUpdated);
                }

                return baggages;
            }

            private Dictionary<string, List<UpsellAndCheckoutData.BaggageNewChange>> VerifyBaggagesNew(Models.GtwAereo.BaggageByPTC currentBaggages, Models.GtwAereo.PriceGroup updatedPriceGroup, ILogger logger)
            {
                var baggagesComparison = new Dictionary<string, List<UpsellAndCheckoutData.BaggageNewChange>>();

                void CompareBaggages(
                    List<Models.GtwAereo.BaggageType> current,
                    List<Models.GtwAereo.BaggageType> updated,
                    string passengerType)
                {
                    var changes = new List<UpsellAndCheckoutData.BaggageNewChange>();
                    var currentBags = current ?? new List<Models.GtwAereo.BaggageType>();
                    var updatedBags = updated ?? new List<Models.GtwAereo.BaggageType>();

                    for (int i = 0; i < Math.Max(currentBags.Count, updatedBags.Count); i++)
                    {
                        var currentBag = i < currentBags.Count ? currentBags[i] : null;
                        var updatedBag = i < updatedBags.Count ? updatedBags[i] : null;

                        changes.Add(new UpsellAndCheckoutData.BaggageNewChange
                        {
                            Type = updatedBag?.Type ?? currentBag?.Type,
                            Current = new UpsellAndCheckoutData.BaggageDetails
                            {
                                Count = currentBag?.Count ?? 0,
                                Weight = currentBag?.Weight,
                                Dimensions = currentBag?.Dimensions == null ? null : new UpsellAndCheckoutData.BaggageDimensionsChange
                                {
                                    Length = currentBag.Dimensions.Length ?? 0,
                                    Width = currentBag.Dimensions.Width ?? 0,
                                    Depth = currentBag.Dimensions.Depth ?? 0
                                },
                                LinearSize = currentBag?.LinearSize
                            },
                            New = new UpsellAndCheckoutData.BaggageDetails
                            {
                                Count = updatedBag?.Count ?? 0,
                                Weight = updatedBag?.Weight,
                                Dimensions = updatedBag?.Dimensions == null ? null : new UpsellAndCheckoutData.BaggageDimensionsChange
                                {
                                    Length = updatedBag.Dimensions.Length ?? 0,
                                    Width = updatedBag.Dimensions.Width ?? 0,
                                    Depth = updatedBag.Dimensions.Depth ?? 0
                                },
                                LinearSize = updatedBag?.LinearSize
                            }
                        });
                    }

                    baggagesComparison[passengerType] = changes;
                }

                CompareBaggages(currentBaggages.ADT, updatedPriceGroup?.Segments?.FirstOrDefault()?.Baggages?.ADT, "adt");
                CompareBaggages(currentBaggages.CHD, updatedPriceGroup?.Segments?.FirstOrDefault()?.Baggages?.CHD, "chd");
                CompareBaggages(currentBaggages.INF, updatedPriceGroup?.Segments?.FirstOrDefault()?.Baggages?.INF, "inf");

                logger.LogInformation($"Processado bagagens novas: {JsonConvert.SerializeObject(baggagesComparison)}");
                return baggagesComparison;
            }


            private static void VerifyBaggagesLogInformation(
                string message,
                List<Segment> segments,
                ILogger logger,
                string currentBaggages,
                FareProfileService handLuggageUpSell,
                Baggage checkedBaggageUpSell,
                int index
                )
            {
                try
                {
                    var informationMessage = new
                    {
                        message = message,
                        departure = segments[index]?.Departure,
                        arrival = segments[index]?.Arrival,
                        departureDate = segments[index]?.DepartureDate,
                        arrivalDate = segments[index]?.ArrivalDate,
                        routeRPH = segments[index]?.RouteRPH,
                        managedBy = segments[index]?.Legs[0]?.ManagedBy?.Name,
                        operatedBy = segments[index]?.Legs[0]?.OperatedBy?.Name,
                        flightNumber = segments[index]?.Legs[0]?.FlightNumber,
                        baggagesAvail = JsonConvert.DeserializeObject<BaggageChange>(currentBaggages),
                        handLuggageUpSell = handLuggageUpSell,
                        checkedBaggageUpSell = checkedBaggageUpSell,
                    };

                    logger.LogInformation(JsonConvert.SerializeObject(informationMessage));
                }
                catch (Exception)
                {
                }
            }

            private UpsellAndCheckoutData.RefundableChange VerifyRefundable(List<FareProfileService> currentServices, List<Segment> segments)
            {
                bool currentRefundable = currentServices?.FirstOrDefault(service => service.Type != null && service.Type.Equals("REFUNDABLE"))?.IsIncluded ?? false;

                bool newRefundable = segments.All(segment => segment?.FareProfile?.Services?.FirstOrDefault(service => service.Type != null && service.Type.Equals("REFUNDABLE"))?.IsIncluded ?? false);

                return new UpsellAndCheckoutData.RefundableChange
                {
                    currentRefundable = currentRefundable,
                    newRefundable = newRefundable
                };
            }


            private async Task<PriceChange> VerifyPriceChange(string priceWithTax, string priceWithoutTax, PriceResponse response,
                CA.Model.Context.BrandContext caBrandCtx, RequestUpsellOrCheckoutDTO request)
            {

                var currentPriceWithTax = (int)Math.Ceiling(decimal.Parse(priceWithTax));
                var newPriceWithTax = (int)Math.Ceiling(response.PriceGroup.FareGroup.PriceWithTax);

                // Chamada do promotion comentada por hora, pois o front não está enviando os valores promocionados
                // Então estava dando diferença de valores, o interessante é o front passar os valores promocionados para comparação, pois no cache não temos essa informação
                //var priceWithPromotion = (int)Math.Ceiling(await CheckPromotion(response, caBrandCtx, request));

                var priceWithPromotion = newPriceWithTax;
                var currentPriceWithoutTax = (int)Math.Ceiling(decimal.Parse(priceWithoutTax));
                var newPriceWithoutTax = (int)Math.Ceiling(response.PriceGroup.FareGroup.PriceWithoutTax);

                var isPriceChange = true;
                var priceDifference = priceWithPromotion - currentPriceWithTax;

                string priceChangeType;
                if (priceDifference == 0)
                {
                    priceChangeType = PriceChangeType.EQUAL.ToString();
                    isPriceChange = false;
                }
                else if (priceDifference > 0)
                    priceChangeType = PriceChangeType.EXPENSIVE.ToString();
                else
                    priceChangeType = PriceChangeType.LOWEST.ToString();

                var priceChange = new PriceChange
                {
                    IsChange = isPriceChange,
                    CurrentPriceWithoutTax = currentPriceWithoutTax,
                    NewPriceWithoutTax = newPriceWithoutTax,
                    CurrentPriceWithTax = currentPriceWithTax,
                    NewPriceWithTax = priceWithPromotion,
                    PriceDifference = priceDifference,
                    PriceChangeType = priceChangeType
                };

                return priceChange;
            }

            private async Task<decimal> CheckPromotion(PriceResponse priceResponse, CA.Model.Context.BrandContext caBrandCtx, RequestUpsellOrCheckoutDTO request)
            {
                var opportunitiesRequestItems = new[]
                {
                    new OpportunitiesRequestItem(0,
                        priceResponse.PriceGroup.Segments.First().RateToken,
                        new OpportunitiesRequestItem.OpportunitiesRequestItemParam(request.OperationalId, request.Flights)
                    )
                };
                var promotionsResponse = await _promotion.Execute(caBrandCtx, this.request.GtwUserToken, opportunitiesRequestItems, null);
                var hasPromotion = promotionsResponse?.AvailableItems == null || !promotionsResponse.AvailableItems.Any();
                if (!hasPromotion)
                    return priceResponse.PriceGroup.FareGroup.PriceWithTax;

                var priceWithPromo = promotionsResponse.AvailableItems.First().Promotion.PriceWithTax;
                var priceResult = priceWithPromo > 0 ? priceWithPromo : priceResponse.PriceGroup.FareGroup.PriceWithTax;
                return priceResult;
            }

            async private Task<List<(string pgUid, RateTokenFareGroupUpgCache cached)>> getCachedPriceGroups()
            {
                var cachedPg = await cache.GetValue(request.PriceGroupId);
                if (string.IsNullOrEmpty(cachedPg))
                    return null;

                var pg = JsonConvert.DeserializeObject<RateTokenFareGroupUpgCache>(cachedPg);
                var allPgs = new List<(string, RateTokenFareGroupUpgCache)>() { (request.PriceGroupId, pg) };

                if (request.OfferUpsell && pg.PossibleUpgrades.Count > 0)
                {
                    var cachedUpgrades = await cache.GetValue(pg.PossibleUpgrades);
                    allPgs.AddRange(getPossibleUpgrades(pg.PossibleUpgrades, cachedUpgrades));
                }
                return allPgs;

                IEnumerable<(string, RateTokenFareGroupUpgCache)> getPossibleUpgrades(List<string> upgradeIds, string[] cachedUpgrades)
                {
                    var max = Math.Min(cachedUpgrades.Length, 3); //No máximo 4 itens (o atual e mais 3 upgrades) serão apresentados.
                    for (var i = 0; i < max; i++)
                        if (!string.IsNullOrEmpty(cachedUpgrades[i]))
                            yield return (upgradeIds[i], JsonConvert.DeserializeObject<RateTokenFareGroupUpgCache>(cachedUpgrades[i]));
                }
            }
            private object buildUpsellResponse(priceGroupData[] priceGroups, (long ms, PriceResponse response)[] allResponses)
            {
                var branchId = brandCtx.GatewayHeaders.BranchId;
                var agentSign = brandCtx.GatewayHeaders.AgentSign;
                var lstResult = new List<priceGroupData>();
                for (var i = 0; i < allResponses.Length; i++)
                {
                    var item = allResponses[i];
                    Pricing.LogPricing(logger, agentSign, branchId, item.response, request.Provider, item.ms, true, brandCtx.isCvc(), request.SearchId, request.ClientId);

                    if (item.response.Error == null)
                        lstResult.Add(priceGroups[i]);
                }
                return new { UpsellData = lstResult.Select(x => new { x.uid, x.fare }) };
            }

            // Novo método buildUpsellResponse que apenas retorna os priceGroups
            // Esse cara está sendo chamado para evitar que toda vez faça uma "reserva" do voo na compania sempre qe faça o upsell
            private object buildUpsellResponse(priceGroupData[] priceGroups)
            {
                return new { UpsellData = priceGroups.Select(x => new { x.uid, x.BaggagesNew, x.fare }) };
            }
        }
    }
}
