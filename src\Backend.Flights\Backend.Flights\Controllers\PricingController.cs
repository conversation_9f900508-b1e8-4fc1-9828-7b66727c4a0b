﻿using Backend.Flights.Business;
using Backend.Flights.CA.Services.Http;
using Backend.Flights.CA.Services.Json;
using Backend.Flights.Models;
using Backend.Flights.Models.GtwAereo;
using Backend.Flights.Models.Price;
using Backend.Flights.Services;
using Backend.Flights.Util;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Backend.Flights.Controllers
{
    [ApiVersion("1.0")]
    [Route("api/[controller]")]
    [ApiController]
    public class PricingController : ControllerBase
    {
        private readonly ILogger<Availability> _logger;
        private readonly ConfigurationManager _config;
        private readonly BrandContext _brandContext;
        private readonly IHttpClient _httpClient;
        private readonly IJsonService _json;

        public PricingController(ILogger<Availability> logger, ConfigurationManager config, IContextBrand brandContext, IHttpClient httpClient, IJsonService json)
        {
            _logger = logger;
            _config = config;
            _brandContext = brandContext.Context;
            _httpClient = httpClient;
            _json = json;
        }

        [EnableCors("AllowAnyOrigin")]
        [HttpPost]
        public async Task<ActionResult<PriceResponse>> Post(PriceRequest request)
        {
            try
            {
                PriceResponse response = await new Pricing(_config, _brandContext, _logger, _httpClient, _json).VerifyFromCheckout(request);

                if (response.Error != null)
                    return StatusCode(500, response.Error.Message);

                return Ok(response);
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.Message);
            }
        }

    }
}