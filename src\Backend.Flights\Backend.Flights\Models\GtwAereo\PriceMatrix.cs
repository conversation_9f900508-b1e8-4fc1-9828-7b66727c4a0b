﻿using System.Collections.Generic;

namespace Backend.Flights.Models.GtwAereo
{
    public class PriceMatrix
    {
        public string Currency { get; set; }
        public List<PriceMatrixColumn> Columns { get; set; }
    }

    public class PriceMatrixColumn
    {
        public List<AirCompany> AirCompanies { get; set; }
        public List<PriceMatrixRow> Rows { get; set; }
    }

    public class PriceMatrixRow
    {
        public int NumberOfStops { get; set; }
        public decimal Price { get; set; }
        public decimal PriceWithoutTax { get; set; }
        public bool BestPrice { get; set; }
    }
}
