﻿using System.Collections.Generic;

namespace Backend.Flights.Models.GtwAereo
{
    public class FareGroup
    {
        public string ReCharging { get; set; }
        public string Currency { get; set; }
        public decimal? Discount { get; set; }
        public decimal? DiscountPercentage { get; set; }
        public decimal PriceWithTax { get; set; }
        public decimal PriceWithoutTax { get; set; }
        public decimal Markup { get; set; }
        public List<Fare> Fares { get; set; }
        public OriginalPriceInfo OriginalPriceInfo { get; set; }
        public BaggageByPTC Baggages { get; set; } // Novo campo para bagagens
    }

    public class Fare
    {
        public string PassengersType { get; set; }
        public int PassengersCount { get; set; }
        public decimal PriceWithTax { get; set; }
        public decimal PriceWithoutTax { get; set; }
        public List<Tax> Taxes { get; set; }
        public OriginalPriceInfo OriginalPriceInfo { get; set; }
    }

    public class OriginalPriceInfo
    {
        public string Currency { get; set; }
        public double ExchangeRate { get; set; }
        public double BaseFare { get; set; }
    }

    public class Tax
    {
        public string Code { get; set; }
        public string Description { get; set; }
        public List<TaxValues> Values { get; set; }

        public List<TaxDetail> Details { get; set; }
    }

    public class TaxDetail
    {
        public string Code { get; set; }
        public string Description { get; set; }
        public decimal Amount { get; set; }
    }

    public class TaxValues
    {
        public string Currency { get; set; }
        public decimal Amount { get; set; }
    }
}
