using System;
using Backend.Flights.CA.Services.Cache;
using Backend.Flights.CA.Model.Cart;
using Backend.Flights.CA.Model.Opportunities;
using Backend.Flights.CA.Model.Promotions;
using Backend.Flights.CA.UseCase.RateTokenCache;
using System.Linq;
using System.Threading.Tasks;
using Backend.Flights.CA.UseCase.Checkout;
using System.Collections.Generic;

namespace Backend.Flights.CA.UseCase.Promotions
{
    public class RequestPromotionsFromCachedFareGroup
    {
        private readonly ICacheService _cacheService;
        private readonly DesserializeCheckoutCache _desserializeCheckoutCache;
        private readonly IGetRateTokenCache _getRateTokenCache;
        private readonly IRequestPromotions _requestPromotions;

        public RequestPromotionsFromCachedFareGroup(
            ICacheService cacheService,
            DesserializeCheckoutCache desserializeCheckoutCache,
            IGetRateTokenCache getRateTokenCache,
            IRequestPromotions requestPromotions
        )
        {
            _cacheService = cacheService;
            _desserializeCheckoutCache = desserializeCheckoutCache;
            _getRateTokenCache = getRateTokenCache;
            _requestPromotions = requestPromotions;
        }

        public async Task<PromotionsCachedFareGroupResponse[]> Execute(
            Model.Context.BrandContext brandContext,
            string userToken,
            PromotionsRequestFlightItem[] availableItems,
            CartItem[] selectedItems
        )
        {
            if (brandContext?.PromotionsActive != true || string.IsNullOrWhiteSpace(userToken) || availableItems == null || availableItems.Length == 0)
            {
                return null;
            }

            string[] flightsCachedV4 = await _cacheService.GetItems(availableItems.Select(i => $"f/{i.UID}"));
            List<Model.Cache.CartCheckout> cachedV4 = new List<Model.Cache.CartCheckout>();

            foreach (var flightCache in flightsCachedV4.Where(fc => fc != null))
            {
                try
                {
                    var cacheData = _desserializeCheckoutCache.Execute(flightCache);
                    cachedV4.Add(cacheData);
                }
                catch { }
            }

            Model.RateTokenFareGroupUpgCache[] cached = new List<Model.RateTokenFareGroupUpgCache>().ToArray();

            if (cachedV4.Count <= 0)
            {
                cached = await _getRateTokenCache.Execute(availableItems.Select(i => i.UID));
                if (cached == null)
                {
                    return null;
                }
            }

            OpportunitiesRequestItem[] pAvaiableItems = new List<OpportunitiesRequestItem>().ToArray();
            /*
            Item1 = RPH = 0: Voo de Ida! 1: Voo de Volta!
            Item2 = Indice do Voo
            Item3 = Chave de Indentificação do Voo (RPH + Indice do Voo + segment hour)
            Item4 = RateToken
            Item5 = FlightId
            */
            List<Tuple<int, int, string, string, string>> mountRateTokensToPromotion = new List<Tuple<int, int, string, string, string>>();

            if (cachedV4.Count > 0)
            {
                var filterFlights = cachedV4
                    .Select((flight, index) => (flight, index))
                    .Where(item =>
                        item.flight != null &&
                        item.flight?.CartItem != null &&
                        item.flight?.CartItem?.FareGroup != null &&
                        item.flight?.CartItem?.FareGroup?.RateTokens != null &&
                        item.flight?.CartItem?.FareGroup?.RateTokens?.Count() > 0
                    ).ToList();

                // Feito para pegar os RateTokens de cada voo de acordo com o horário de cada segmento
                int newRPH = 0;
                filterFlights.ForEach((Action<(Model.Cache.CartCheckout flight, int index)>)(fl =>
                {
                    foreach (var item in fl.flight.Options)
                    {
                        if (item.Key.StartsWith("0-"))
                        {
                            mountRateTokensToPromotion.Add(new Tuple<int, int, string, string, string>(newRPH, fl.index, item.Key, item.Value.Token, availableItems[fl.index].UID));
                            newRPH++;
                        }
                    }
                }));

                pAvaiableItems = MountAvailableItems(availableItems, mountRateTokensToPromotion);
            }
            else
            {
                var filterFlights = cached
                    .Select((flight, index) => (flight, index))
                    .Where(index => index.flight?.Tokens != null && index.flight.Tokens.Count > 0).ToList();

                // Feito para pegar os RateTokens de cada voo de acordo com o horário de cada segmento
                int newRPH = 0;
                filterFlights.ForEach((Action<(Model.RateTokenFareGroupUpgCache flight, int index)>)(fl =>
                {
                    foreach (var item in fl.flight.Tokens)
                    {
                        if (item.Key.StartsWith("0-"))
                        {
                            mountRateTokensToPromotion.Add(new Tuple<int, int, string, string, string>(newRPH, fl.index, item.Key, item.Value, availableItems[fl.index].UID));
                            newRPH++;
                        }
                    }
                }));

                pAvaiableItems = MountAvailableItems(availableItems, mountRateTokensToPromotion);
            }

            PromotionsResponse promotions = await _requestPromotions.Execute(
                brandContext,
                userToken,
                pAvaiableItems,
                selectedItems
            );


            if (promotions != null && promotions?.AvailableItems != null && promotions?.AvailableItems?.Count() > 0)
            {
                var fareGroupResponses = promotions.AvailableItems.Select((promotion, index) =>
                {
                    return new PromotionsCachedFareGroupResponse()
                    {
                        FlightId = mountRateTokensToPromotion.Where(m => m.Item1 == promotion.RPH).FirstOrDefault()?.Item5,
                        RateToken = promotion?.Promotion?.PromoIds?.Count > 0 ? promotion?.RateToken : null,
                        IdentificationKey = mountRateTokensToPromotion.Where(m => m.Item1 == promotion.RPH).FirstOrDefault()?.Item3,
                        Promotion = promotion?.Promotion,
                        Statements = promotion?.Statements,
                        RPH = index
                    };
                }).ToArray();

                await UpdateRateTokenProfitSplitForPromotions(fareGroupResponses.ToList(), brandContext, userToken);

                return fareGroupResponses;
            }

            return availableItems.Select((a, i) =>
            {
                var promotion = promotions?.AvailableItems?.FirstOrDefault(p => p.RPH == i);

                if (promotion == null)
                    return null;

                return new PromotionsCachedFareGroupResponse()
                {
                    FlightId = a.UID,
                    RateToken = promotion?.Promotion?.PromoIds?.Count > 0 ? promotion?.RateToken : null,
                    IdentificationKey = mountRateTokensToPromotion.Where(m => m.Item1 == promotion?.RPH).FirstOrDefault()?.Item3,
                    Promotion = promotion?.Promotion,
                    Statements = promotion?.Statements
                };
            }).Where(resp => resp != null).ToArray();
        }

        private static OpportunitiesRequestItem[] MountAvailableItems(PromotionsRequestFlightItem[] availableItems, List<Tuple<int, int, string, string, string>> mountRateTokensToPromotion)
        {
            return mountRateTokensToPromotion
                        .Select(i => new OpportunitiesRequestItem(
                            i.Item1,
                            i.Item4,
                            new OpportunitiesRequestItem.OpportunitiesRequestItemParam(availableItems[i.Item2]?.OperationalId, availableItems[i.Item2]?.Flights)
                        ))
                        .ToArray();
        }

        private async Task UpdateRateTokenProfitSplitForPromotions(List<PromotionsCachedFareGroupResponse> fareGroupResponses, Model.Context.BrandContext brandContext, string userToken)
        {
            try
            {
                if (brandContext.IsProfitSplit())
                {
                    Console.WriteLine("[UpdateRateTokenProfitSplitForPromotions] - Atualizando RateToken com Profit Split");
                    var defaultGroup = 1;

                    // Mapeamento dos segmentos para CartItem
                    var selectedItems = fareGroupResponses.Select((response, index) =>
                        new CartItem
                        {
                            RPH = response.RPH,
                            RateToken = response.RateToken,
                            Group = defaultGroup.ToString(),
                            RouteRPH = 0
                        }
                    ).ToArray();

                    var requestParams = new OpportunitiesRequest.RequestParams
                    {
                        ProfileName = "shopping-cart",
                        ShowSteps = false,
                        ShowPayloads = false,
                        ProfitSplit = new OpportunitiesRequest.ProfitSplit
                        {
                            Products = new List<OpportunitiesRequest.ProfitSplitProduct>
                    {
                        new OpportunitiesRequest.ProfitSplitProduct
                        {
                            Type = "AIR",
                            Groups = new List<int> { defaultGroup }
                        }
                    }
                        }
                    };

                    // Chama o método de promoções novamente para pegar as atualizações
                    var promotionsResponse = await _requestPromotions.Execute(
                        brandContext,
                        userToken,
                        null,
                        selectedItems,
                        requestParams,
                        true
                    );

                    if (promotionsResponse != null && promotionsResponse.SelectedItems.Any())
                    {
                        // Atualiza os RateTokens no fareGroupResponses
                        foreach (var fareResponse in fareGroupResponses)
                        {
                            var updatedRateToken = promotionsResponse.SelectedItems.FirstOrDefault(x => x.RPH == fareResponse.RPH)?.RateToken;

                            if (!string.IsNullOrEmpty(updatedRateToken))
                            {
                                fareResponse.RateToken = updatedRateToken;
                            }
                        }
                    }
                }
            }
            catch (System.Exception ex)
            {
                Console.WriteLine($"[UpdateRateTokenProfitSplitForPromotions] - Erro ao atualizar RateToken com Profit Split: {ex.Message}");
            }
        }
    }
}