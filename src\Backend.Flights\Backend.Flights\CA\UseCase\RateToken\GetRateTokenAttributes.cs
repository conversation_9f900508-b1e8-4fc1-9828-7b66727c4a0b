﻿using Microsoft.AspNetCore.WebUtilities;
using System.Collections.Generic;
using System.Linq;
using System.Xml.Linq;

namespace Backend.Flights.CA.UseCase.RateToken
{
    public class GetRateTokenAttributes
    {
        public Dictionary<string, string> Execute(string token, params string[] keys)
        {
            var decode64 = Base64UrlTextEncoder.Decode(token);
            var decodeStr = System.Text.Encoding.UTF8.GetString(decode64);

            var rateToken = XDocument.Parse(decodeStr).Element("rateToken");

            return new Dictionary<string, string>(
                keys.Select(k => new KeyValuePair<string, string>(k, rateToken?.Attribute(k)?.Value))
                );

        }
    }
}