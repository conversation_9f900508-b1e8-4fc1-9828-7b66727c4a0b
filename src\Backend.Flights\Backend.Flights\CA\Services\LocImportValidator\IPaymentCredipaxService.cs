using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Backend.Flights.CA.Model;
using Backend.Flights.CA.Model.Context;
using Backend.Flights.CA.Model.LocImport;

namespace Backend.Flights.CA.Services.LocImportValidator
{
    public interface IPaymentCredipaxService
    {
        /// <summary>
        /// Valida os dados de pagamento/credipax chamando o endpoint configurado.
        /// </summary>
        /// <param name="loc">Localizador (P_LOCALIZADOR).</param>
        /// <param name="branch"><PERSON><PERSON><PERSON> da filial (P_CD_FILIAL).</param>
        /// <param name="userToken">Token do usuário.</param>
        /// <returns>SafeResult com ImportLocPaymentValidationResponse ou tupla de erro.</returns>
        Task<SafeResult<ImportLocPaymentValidationResponse, HttpErrorResult>> Validate(string loc, int branch, string userToken);
    }
}